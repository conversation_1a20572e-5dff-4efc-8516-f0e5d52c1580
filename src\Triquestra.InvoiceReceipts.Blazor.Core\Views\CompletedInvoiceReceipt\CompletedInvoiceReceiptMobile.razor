@using Infinity.Blazor.Components.ProductSearchDropdown.View
@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Tags
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.CompletedInvoiceReceipt
@using Infinity.InvoiceReceipts.Blazor.Views.Common.Report
@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList
@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedReceiptSummaryPanel
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization;
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums

@inherits RazorBaseMvvm<CompletedInvoiceReceiptViewModel>
<div class="page-container">
    
    <!-- header -->
    <div>
        <div class="page-header">
            <div class="header-group">
                <InfBackButton OnClick="ViewModel.RedirectToPreviousPage" />
                <div class="title">
                    @Localizer.GetString(Translations.InvoiceReceipt)
                </div>
            </div>
            <div class="buttons">
                <DownloadReportButton StockReceiptCode="@StockReceiptCode" />
            </div>
        </div>
    </div>

    <CompletedReceiptSummaryPanelMobile InvoiceReceipt="ViewModel.CurrentInvoiceReceipt" />

</div>

<div class="productlist-wrapper mt-3">
    <CompletedProductListMobile IsMatchedToPurchaseOrder="ViewModel.CurrentInvoiceReceipt.IsMatchedToPurchaseOrders" ProductLines="ViewModel.ProductLines" IsLoading="ViewModel.IsLoading" />
</div>