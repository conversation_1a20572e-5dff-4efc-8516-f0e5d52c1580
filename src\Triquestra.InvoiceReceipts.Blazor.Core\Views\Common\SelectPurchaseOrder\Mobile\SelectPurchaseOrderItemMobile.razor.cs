﻿using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Windows.Input;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Mobile
{
    public partial class SelectPurchaseOrderItemMobile
    {
        private bool _isChecked;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public PurchaseOrder PurchaseOrder { get; set; } = new();

        [Parameter]
        public IRelayCommand<PurchaseOrder>? CheckedChangedCommand { get; set; }

        private void IsCheckedChanged()
        {
            _isChecked = !_isChecked;

            if (CheckedChangedCommand?.CanExecute(PurchaseOrder) == true)
            {
                CheckedChangedCommand?.Execute(PurchaseOrder);
            }
        }
    }
}