﻿using Triquestra.InvoiceReceipts.DTOs;
using Triquestra.InvoiceReceipts.Bff.Service.ApiWrapper;

namespace Triquestra.InvoiceReceipts.Bff.Api.Configuration
{
    public static class AppConfigurationManager
    {
        public static AppConfigurationModel RegisterTriquestraConfiguration(IServiceCollection services, bool isDevelopment, string userAgent, ConfigurationManager configurationManager, NLog.ILogger logger)
        {
            var triquestraApplication = new TriquestraApplicationBuilder(isDevelopment, userAgent, configurationManager, logger);
            var apiWrapper = new InvoiceReceiptApiWrapper(triquestraApplication.AppConfiguration, userAgent);

            services.AddSingleton(triquestraApplication.AppConfiguration);
            services.AddSingleton(apiWrapper.ApiWrapper);
            services.AddSingleton(apiWrapper);

            return triquestraApplication.AppConfiguration;
        }
    }
}