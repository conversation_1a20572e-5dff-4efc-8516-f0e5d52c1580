﻿@using Infinity.Blazor.UIControls.Layouts
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
@using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile.ProductItem
@using Telerik.Blazor.Components

<div class="productlist-container-mobile">
    @if (IsLoading)
    {
        <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
    }
    else if (!PurchaseOrderLines.Any())
    {
        <CardContainer>
            <CardBody Class="no-data-body">
                <div>
                    <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                </div>
                <label class="text-center text-muted">@Localizer.GetString(Translations.NoPurchaseOrdersToDisburse)</label>
            </CardBody>
        </CardContainer>
    }
    else
    {
        <label class="product-list-label">@Localizer.GetString(Translations.ItemsOnPO)</label>

        @foreach (var item in PurchaseOrderLines)
        {
           <DisbursementProductItemMobile @key="@item.Key"
                                          PurchaseOrderLine="@item"
                                          SiteCode="@SiteCode"
                                          IsExpanded="IsCardExpand(item.Key!)"
                                          SelectProductCommand="_selectProductCommand"
                                          DataChangedCommand="DataChangedCommand" />
        }
    }
</div>