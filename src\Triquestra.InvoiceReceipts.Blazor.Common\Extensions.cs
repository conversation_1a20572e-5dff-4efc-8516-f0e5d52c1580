﻿using Microsoft.Extensions.Configuration;
using System.Globalization;

namespace Triquestra.InvoiceReceipts.Blazor.Common
{
    public static class Extensions
    {
        public static void SetDefaultCurrentCulture(this IConfiguration configuration)
        {
            var defaultCountryCode = configuration["TimeZone.Country"];

            if (!string.IsNullOrEmpty(defaultCountryCode))
            {
                var cultureInfo = new CultureInfo(defaultCountryCode);
                cultureInfo = cultureInfo.SetCultureCurrencyFormatsAsync();

                CultureInfo.DefaultThreadCurrentCulture = cultureInfo;
                CultureInfo.DefaultThreadCurrentUICulture = cultureInfo;
            }
        }

        private static CultureInfo SetCultureCurrencyFormatsAsync(this CultureInfo culture)
        {
            const int NegativeFormatPattern = 2; // pattern: "$-n"
            var regionInfo = new RegionInfo(culture.Name);

            return new CultureInfo(culture.Name)
            {
                NumberFormat =
                {
                    CurrencySymbol = regionInfo.CurrencySymbol.Length > 1 ? "$" : regionInfo.CurrencySymbol,
                    CurrencyNegativePattern = NegativeFormatPattern
                }
            };
        }
    }
}