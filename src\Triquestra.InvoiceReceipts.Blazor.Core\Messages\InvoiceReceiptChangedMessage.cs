﻿using CommunityToolkit.Mvvm.Messaging.Messages;
using Infinity.InvoiceReceipts.Blazor.Enums;

namespace Infinity.InvoiceReceipts.Blazor.Messages
{
    public class InvoiceReceiptChangedMessage : ValueChangedMessage<string>
    {
        public InvoiceReceiptChangedEventType EventType { get; private set; }

        public InvoiceReceiptChangedMessage(string stockReceiptCode, InvoiceReceiptChangedEventType eventType) : base(stockReceiptCode)
        {
            EventType = eventType;
        }
    }
}