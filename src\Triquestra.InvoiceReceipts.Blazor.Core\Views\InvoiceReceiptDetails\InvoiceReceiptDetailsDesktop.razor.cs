using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList;
using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductSearchPanel;
using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Desktop;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptDetails
{
    public partial class InvoiceReceiptDetailsDesktop : IDisposable
    {
        private IEnumerable<ProductClientBffDto> _selectedProducts = new List<ProductClientBffDto>();

        private ProductSearchPanelDesktop? _productSearchPanelRef;

        private ProductListDesktop? _productListRef;

        private IRelayCommand<string>? _productDropdownSearchBarcodeCommand;
        private IRelayCommand<object>? _completeReceiptCommand;

        private bool _isDisbursementDialogVisible;
        private DisbursementDialogDesktop _disbursementDialogRef;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IJSRuntime JsRuntime { get; set; }

        [Inject]
        public IUserNotificationService NotificationService { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; } = string.Empty;

        [Parameter]
        [SupplyParameterFromQuery]
        public string ReturnUrl { get; set; } = string.Empty;

        public InvoiceReceiptDetailsDesktop()
        {
            _productDropdownSearchBarcodeCommand = new AsyncRelayCommand<string>(ProductDropdownSearchBarcodeHandlerAsync);
            _completeReceiptCommand = new AsyncRelayCommand<object?>(CompleteReceiptHandlerAsync);
        }

        protected override async Task OnInitializedAsync()
        {
            ViewModel.ReturnUrl = ReturnUrl;
            ViewModel.IsMobile = false;
            ViewModel.ProductLines.CollectionChanged += ViewModel_CollectionChanged;
            ViewModel.DataChanged = new EventCallback(this, StateHasChanged);
            await ViewModel.InitializeAsync(StockReceiptCode);
            await base.OnInitializedAsync();
        }

        private void ViewModel_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        private async Task CompleteReceiptHandlerAsync(object? _)
        {
            if (!(await ViewModel.ValidateFreightDisbursementAsync()))
            {
                return;
            }

            if (ViewModel.ShouldShowPurchaseOrderDisbursement())
            {
                _isDisbursementDialogVisible = true;
                StateHasChanged();

                await _disbursementDialogRef.LoadDataAsync(
                    ViewModel.InvoiceSummaryModel.SelectedSite!.SiteCode!.Value,
                    ViewModel.ProductLines.ToList());
            }
            else
            {
                await ViewModel.ConfirmAndCompleteAsync();
            }
        }

        private async Task ProductDropdownSearchBarcodeHandlerAsync(string barcode)
        {
            await _productSearchPanelRef.SetEnabledStatusAsync(false);
            var productCode = await ViewModel.ScanProductAsync(barcode, _productSearchPanelRef!.IsByAnySupplier);
            await _productSearchPanelRef.SetEnabledStatusAsync(true);

            if (!string.IsNullOrEmpty(productCode))
            {
                var productLine = ViewModel.ProductLines.FirstOrDefault(x => x.ProductCode == productCode);
                if (productLine != null)
                {
                    await _productListRef!.SetExpanedItemsAsync(new List<ProductLineViewModel> { productLine });
                }
            }

            StateHasChanged();
            await _productSearchPanelRef!.FocusAndSelectAllTextInSearchBoxAsync();
        }

        private void ProductListDataChangedHandler()
        {
            ViewModel.IsDataChanged = true;
            
            if (ViewModel.FreightDisbursementType != FreightDisbursementType.Manual)
            {
                ViewModel.CalculateFreights();
            }
        }

        public override void Dispose()
        {
            ViewModel.Dispose();
            ViewModel.ProductLines.CollectionChanged -= ViewModel_CollectionChanged;
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}