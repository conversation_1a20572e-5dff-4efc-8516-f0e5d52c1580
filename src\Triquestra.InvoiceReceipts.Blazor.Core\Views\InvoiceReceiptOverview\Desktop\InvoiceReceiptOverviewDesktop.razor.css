﻿.text-darkest {
    color: #202020;
}

.receipt-overview-tabs ::deep .k-card-body.receipt-overview-tabs-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 12px 12px 0 0;
}

    .receipt-overview-tabs ::deep .k-card-body.receipt-overview-tabs-actions label {
        font-weight: 500;
        margin-bottom: 10px;
        white-space: nowrap;
    }

    .receipt-overview-tabs ::deep .k-card-body.receipt-overview-tabs-actions #triquestra-calendar-container,
    .receipt-overview-tabs ::deep .k-card-body.receipt-overview-tabs-actions #triquestra-calendar-selection {
        background: #F9F9F9;
    }

.receipt-overview-tabs .date-range-field-container {
    min-width: 100px;
    width: 320px;
}

.receipt-overview-tabs ::deep .border-top {
    border-radius: 0 0 12px 12px;
}

::deep #triquestra-calendar-selection {
    width: 100%;
}

::deep .receipt-details-grid {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.k-tabstrip-content {
    background-color: transparent;
}

::deep .k-grid.triquestra-gridview, ::deep .k-pager-wrap.telerik-blazor.k-grid-pager {
    border-radius: 0 0 12px 12px;
}