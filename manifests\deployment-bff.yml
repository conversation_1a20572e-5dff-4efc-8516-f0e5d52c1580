apiVersion : apps/v1
kind: Deployment
metadata:
  name: invoicereceipts-bff
spec:
  replicas: 1
  selector:
    matchLabels:
      app: invoicereceipts-bff
  template:
    metadata:
      labels:
        app: invoicereceipts-bff
    spec:
      containers:
      - name: invoicereceipts-bff
        image: infinitycr.azurecr.io/invoicereceipts-bff:__image-tag__
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 80
          protocol: TCP
          
        envFrom:
        - configMapRef:
            name: global-config
        - secretRef:
            name: global-secrets

        env:
          - name: TriquestraApiApiRoot
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: triquestra_api_host  

          - name: TriquestraApiBaseUrl
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: triquestra_api_base_url     

          - name: DomainApiClientId
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: domain_api_client_id

          - name: DomainApiClientSecret
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: domain_api_client_secret         
          
          - name: InfinityApiApiRoot
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: infinity_apis_host

          - name: InvoiceReceiptsBffBasePath
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: invoicereceipts_bff_base_path
          
          - name: InvoiceReceiptsBlazorHostUrl
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: invoicereceipts_blazor_host_url
          
          - name: IdentityServerClientId
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: identity_blazor_client_id
          
          - name: IdentityServerClientSecret
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: identity_blazor_client_secret
          
          - name: IdentityServerAuthority
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: identity_server_authority
          
          - name: IdentityServerDatabaseConnection
            valueFrom:
              secretKeyRef:
                name: global-secrets
                key: DATABASE_CONNECTION_STRING

          - name: InvoiceReceiptsCacheExpirationInMinutes
            valueFrom:
              configMapKeyRef:
                name: invoicereceipts-bff-configmap
                key: invoicereceipts_cache_expiration_in_minutes

          - name: IMAGE_NAME
            value: triquestra-invoicereceipts-bff-migrations:__image-tag__
          - name: ROLENAME
            value: Triquestra.InvoiceReceipts.Bff
        livenessProbe:
          initialDelaySeconds: 20
          timeoutSeconds: 5
          httpGet:
            port: 80
            path: /Triquestra/invoicereceipts_bff/health
        readinessProbe:
          initialDelaySeconds: 5
          timeoutSeconds: 5
          httpGet:
            port: 80
            path: /Triquestra/invoicereceipts_bff/health