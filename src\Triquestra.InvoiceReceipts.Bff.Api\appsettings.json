{"InvoiceReceiptsBlazorHostUrl": "", "InvoiceReceiptsBffBasePath": "/Triquestra/invoicereceipts_bff", "InvoiceReceiptsCacheExpirationInMinutes": 60, "DomainApi.ClientId": "REPLACE_ME", "DomainApi.ClientSecret": "REPLACE_ME", "TriquestraApi.ApiRoot": "https://infinityapp01-dev.australiaeast.cloudapp.azure.com:8056", "TriquestraApi.BaseUrl": "/Triquestra/", "InfinityApi.ApiRoot": "http://infinitypdev01.triquestratest.com", "IdentityServer.ClientId": "REPLACE_ME", "IdentityServer.ClientSecret": "REPLACE_ME", "IdentityServerDatabaseConnection": "REPLACE_ME", "IdentityServer.Authority": "https://infinitypdev01.triquestratest.com", "AllowedHosts": "*", "AppInsights.Environment": "Development", "AppInsights.ImageName": "1.0.0", "AppInsights.RoleName": "Triquestra.InvoiceReceipts.Bff", "AppInsights.EnableSQLInstrumentation": "false"}