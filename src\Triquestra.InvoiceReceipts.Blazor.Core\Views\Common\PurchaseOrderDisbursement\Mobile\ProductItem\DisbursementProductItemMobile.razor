﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Utillities
@using System.Globalization
@using Telerik.Blazor;
@using Telerik.Blazor.Components

@inject IStringLocalizer<Resources> Localizer

<div>
    <InfExpandablePanelBar Expanded="IsExpanded"
                           ItemClickCommand="_panelBarClickCommand"
                           Class="product-item-wrapper"
                           Key="@PurchaseOrderLine.Key"
                           HideHeaderWhenExpanded="true"
                           @bind-ExpandedItems="ExpandedItems">
        <HeaderContent>
            <DisbursementProductItemHeader DataItem="PurchaseOrderLine" />
        </HeaderContent>
        <ChildContent>
            <CardBody>
                <div class="card-item k-d-flex">
                    <div class="card-item-description-col k-d-flex-col">
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ProductCode)</label>
                            <div class="card-item-value">@PurchaseOrderLine.ProductCode</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.SKU)</label>
                            <div class="card-item-value">@PurchaseOrderLine.Sku</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ProductName)</label>
                            <span class="text-end">@PurchaseOrderLine.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)</span>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.PONumber)</label>
                            <span class="text-end">@PurchaseOrderLine.PurchaseOrderCode</span>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.Logged)</label>
                            <span class="text-end">@PurchaseOrderLine.Logged.ToString(CultureInfo.DefaultThreadCurrentCulture!.DateTimeFormat.ShortDatePattern)</span>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.Receive)</label>
                            <span class="text-end">@PurchaseOrderLine.ReceiptQuantity.ToString(UIConstants.DecimalDisplayFormat)</span>
                        </div>
                        <hr class="mt-3 mb-2" />
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.BackOrder)</label>
                            <InfNumericTextBox @bind-Value="@PurchaseOrderLine.BackOrder"
                                               Decimals="2" Width="100%"
                                               Focusable="true" Customizable="true" 
                                               TextAlign="TextAlign.Center"
                                               InputMode="Infinity.Blazor.UIControls.InputFields.NumericKeyboardType.Decimal"
                                               Min="@UIConstants.MinNumericTextBoxValue"
                                               Max="PurchaseOrderLine.MaxBackOrder"/>
                        </div>
                    </div>
                </div>
            </CardBody>
        </ChildContent>
    </InfExpandablePanelBar>
</div>