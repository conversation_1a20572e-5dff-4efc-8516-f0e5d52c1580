﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.Report
{
    public partial class InvoiceReceiptReportDialog
    {
        private const string TimezoneOffsetParam = "timezoneoffset";

        [Inject]
        public IConfiguration Configuration { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; } = string.Empty;

        [Parameter]
        public bool Visible { get; set; }

        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        private string InvoiceReceiptReportUrl => Configuration.GetValue<string>("InvoiceReceiptReportUrl") + StockReceiptCode + $"?{TimezoneOffsetParam}=" + DateTimeOffset.Now.Offset.TotalHours;
    }
}