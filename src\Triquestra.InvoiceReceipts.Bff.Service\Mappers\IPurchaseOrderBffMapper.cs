﻿using Triquestra.InfinityAPI.Transactions.PurchaseOrders.Models.DTOs;
using Triquestra.InfinityAPI.Transactions.StockReceipt.Models.DTOs;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Service.Mappers
{
    public interface IPurchaseOrderBffMapper
    {
        PurchaseOrderBffDto ToPurchaseOrderBffDto(PurchaseOrderDto dto);

        PurchaseOrderReceiptBffDto ToPurchaseOrderReceiptBffDto(PurchaseOrderReceiptDto dto);

        SearchParametersDto ToSearchParametersDto(PurchaseOrderSearchRequestBffDto bffDto);
    }
}