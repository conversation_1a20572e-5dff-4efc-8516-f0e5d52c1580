﻿namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts
{
    public abstract class PurchaseOrderReceiptBaseBffDto
    {
        public int? SiteCode { get; set; }

        public string PurchaseOrderCode { get; set; } = string.Empty;

        public string PurchaseOrderReceiptStatus { get; set; } = string.Empty;

        public string ExternalReference { get; set; } = string.Empty;

        public string SupplierReference { get; set; } = string.Empty;

        public DateTime? ReceiptDate { get; set; }
    }
}