﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.CalendarPickers
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
@using Microsoft.JSInterop;
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Base;

@inherits RazorBaseMvvm<InvoiceReceiptOverviewViewModel>

@inject IStringLocalizer<Resources> Localizer

<CardBody Class="d-block overview-actions">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <div class="h5 text-darkest">@Localizer.GetString(Translations.ReceiptByInvoiceTitle)</div>
            <div class="text-muted">@Localizer.GetString(Translations.ReceiptByInvoiceDescription)</div>
        </div>
        <InfButton Enabled="true"
                   FillMode="FillMode.Solid"
                   LabelText="@Localizer.GetString(Translations.CreateNewInvoice)"
                   ClickCommand="ViewModel.CreateNewInvoiceCommand">
            <IconTemplate>
                <AddIcon />
            </IconTemplate>
        </InfButton>
    </div>
</CardBody>

<div class="receipt-overview-tabs">
    <CardBody Class="receipt-overview-tabs-actions">
        <div class="d-flex flex-row w-100">
            <div class="me-2" style="width: 40%">
                <label class="text-darkest fs-14">@Localizer.GetString(Translations.SearchByInvoiceNumber)</label>

                <InfTextBox Width="100%"
                            IconPosition="Infinity.Blazor.UIControls.Icons.IconPosition.Left"
                            PlaceHolder="@Localizer.GetString(Translations.EnterInvoiceNumber)"
                            @bind-Value="@_currentInvoiceCode"
                            ChangeCommand="_searchBoxChangedCommand">
                    <IconTemplate>
                        <Infinity.Blazor.Components.Icons.SearchIcon />
                    </IconTemplate>
                </InfTextBox>
            </div>
            <div class="date-range-field-container">
                @RenderDateRangeFilter()
            </div>
        </div>
    </CardBody>

    <InvoiceReceiptOverviewDesktopGridView @ref="_gridViewRef" />
</div>
@code {
    private RenderFragment RenderDateRangeFilter()
    {
        return __builder =>
        {
            <label class="text-darkest  fs-14">@Localizer.GetString(Translations.FilterByDateRange)</label>
            <InfCalendarPicker @ref="_calendarPickerRef"
                               SetDatesCommand="@_setDatesCommand"
                               DefaultDateRange="@Infinity.Blazor.UIControls.CalendarPickers.TriquestraDateRangeType.ThisMonth"
                               Enabled="_isCalendarEnabled" />
        };
    }
}