﻿using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InfinityAPI.Transactions.PurchaseOrders.Models.DTOs;
using Triquestra.InfinityAPI.Transactions.StockReceipt.Models.DTOs;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Service.Mappers
{
    public class InvoiceReceiptsBffMapper : IInvoiceReceiptsBffMapper
    {
        public TaxedProductObjectBffDto ToTaxedProductObjectBffDto(ProductObjectBffDto bffDto)
        {
            if (bffDto == null) return new();

            return new()
            {
                ProductCode = bffDto.ProductCode,
                Sku = bffDto.Sku,
                Description = bffDto.Description,
                Unit = bffDto.Unit,
                StandardSellingPrice = bffDto.StandardSellingPrice,
                Cost = bffDto.Cost,
                NonStock = bffDto.NonStock,
                SiteSpecific = bffDto.SiteSpecific,
                SupplierCode = bffDto.SupplierCode,
                SupplierName = bffDto.SupplierName,
                Archived = bffDto.Archived,
                ProductCodeParentUnit = bffDto.ProductCodeParentUnit,
                ConversionToParentUnit = bffDto.ConversionToParentUnit,
                SupplierProductCode = bffDto.SupplierProductCode,
                TaxCode = bffDto.TaxCode,
                PurchasingRules = bffDto.PurchasingRules,
                AlternateSuppliers = bffDto.AlternateSuppliers,
                HierarchyPosition = bffDto.HierarchyPosition,
                TargetMarginPercentage = bffDto.TargetMarginPercentage,
            };
        }

        public StockReceiptResponseBffDto ToStockReceiptResponseBffDto(StockReciptResponseDto dto)
        {
            return new()
            {
                Archived = dto.Archived,
                Created = dto.Created,
                StockReceiptCode = dto.StockReceiptCode,
                Updated = dto.Updated,
            };
        }

        public InvoiceReceiptCreateDto ToInvoiceReceiptCreateDto(InvoiceReceiptCreateBffDto bffDto)
        {
            return new()
            {
                CreatedBy = bffDto.CreatedBy,
                FreightDisbursmentType = bffDto.FreightDisbursmentType,
                InvoiceCode = bffDto.InvoiceCode,
                InvoiceDate = bffDto.InvoiceDate,
                InvoiceReceiptStatus = bffDto.InvoiceReceiptStatus,
                Note = bffDto.Note,
                SiteCode = bffDto.SiteCode,
                SupplierCode = bffDto.SupplierCode,
                TotalFreight = bffDto.TotalFreight,
                TotalInputTax = bffDto.TotalInputTax,
                TotalInvoiceValue = bffDto.TotalInvoiceValue,
                Lines = bffDto.Lines.Select(ToInvoiceReceiptLineDto).ToList(),
            };
        }

        public InvoiceReceiptSearchRequestDto ToInvoiceReceiptSearchRequestDto(InvoiceReceiptSearchRequestBffDto bffDto)
        {
            return new()
            {
                StartDate = bffDto.StartDate,
                EndDate = bffDto.EndDate,
                InvoiceCode = bffDto.InvoiceCode,
                InvoiceReceiptStatus = bffDto.InvoiceReceiptStatus,
                Offset = bffDto.Offset,
                PageSize = bffDto.PageSize,
                ResponseType = bffDto.ResponseType,
                SiteCodes = bffDto.SiteCodes,
            };
        }

        public InvoiceReceiptBffDto ToInvoiceReceiptBffDto(InvoiceReceiptDto dto)
        {
            return new()
            {
                Created = dto.Created,
                CreatedBy = dto.CreatedBy,
                FreightDisbursmentType = dto.FreightDisbursmentType,
                InvoiceCode = dto.InvoiceCode,
                InvoiceReceiptStatus = dto.InvoiceReceiptStatus,
                InvoiceDate = dto.InvoiceDate ?? DateTime.MinValue,
                Note = dto.Note,
                SiteCode = dto.SiteCode,
                Lines = dto.Lines?.Select(ToInvoiceReceiptLineBffDto).OrderBy(x => x.LineNumber).ToList() ?? new(),
                StockReceiptCode = dto.StockReceiptCode,
                SupplierCode = dto.SupplierCode,
                TotalFreight = dto.TotalFreight,
                TotalInvoiceValue = dto.TotalInvoiceValue,
                TotalInputTax = dto.TotalInputTax,
                Updated = dto.Updated,
                UpdatedBy = dto.UpdatedBy,
            };
        }

        public InvoiceReceiptUpdateDto ToInvoiceReceiptUpdateDto(InvoiceReceiptUpdateBffDto bffDto)
        {
            return new()
            {
                UpdatedBy = bffDto.UpdatedBy,
                FreightDisbursmentType = bffDto.FreightDisbursmentType,
                InvoiceCode = bffDto.InvoiceCode,
                InvoiceDate = bffDto.InvoiceDate,
                InvoiceReceiptStatus = bffDto.InvoiceReceiptStatus,
                Note = bffDto.Note,
                SiteCode = bffDto.SiteCode,
                SupplierCode = bffDto.SupplierCode,
                TotalFreight = bffDto.TotalFreight,
                TotalInputTax = bffDto.TotalInputTax,
                TotalInvoiceValue = bffDto.TotalInvoiceValue,
                Lines = bffDto.Lines.Select(ToInvoiceReceiptLineDto).ToList(),
            };
        }

        public InvoiceReceiptStatusUpdateDto ToInvoiceReceiptStatusUpdateDto(InvoiceReceiptStatusUpdateBffDto bffDto)
        {
            return new()
            {
                UpdatedBy = bffDto.UpdatedBy,
                InvoiceReceiptStatus = bffDto.InvoiceReceiptStatus,
            };
        }

        private static InvoiceReceiptLineBffDto ToInvoiceReceiptLineBffDto(InvoiceReceiptLineDto dto)
        {
            return new()
            {
                FreightDisbursmentAmount = dto.FreightDisbursmentAmount,
                InvoiceCostPriceExcludingInputTax = dto.InvoiceCostPriceExcludingInputTax,
                InvoiceQuantity = dto.InvoiceQuantity,
                LineNumber = dto.LineNumber ?? 0,
                ProductCode = dto.ProductCode,
                ReceiptCostPriceExcludingInputTax = dto.ReceiptCostPriceExcludingInputTax,
                ReceiptQuantity = dto.ReceiptQuantity,
                RequestLabel = dto.RequestLabel,
                UpdateStandardSellingPrice = dto.UpdateStandardSellingPrice,
                PurchaseOrderDisbursements = dto.PurchaseOrderDisbursements?.Select(ToPurchaseOrderDisbursementBffDto).ToList() ?? new(),
            };
        }

        private static InvoiceReceiptLineDto ToInvoiceReceiptLineDto(InvoiceReceiptLineBffDto bffDto)
        {
            return new()
            {
                FreightDisbursmentAmount = bffDto.FreightDisbursmentAmount,
                InvoiceCostPriceExcludingInputTax = bffDto.InvoiceCostPriceExcludingInputTax,
                InvoiceQuantity = bffDto.InvoiceQuantity,
                LineNumber = bffDto.LineNumber,
                ProductCode = bffDto.ProductCode,
                ReceiptCostPriceExcludingInputTax = bffDto.ReceiptCostPriceExcludingInputTax,
                ReceiptQuantity = bffDto.ReceiptQuantity,
                RequestLabel = bffDto.RequestLabel,
                UpdateStandardSellingPrice = bffDto.UpdateStandardSellingPrice,
                PurchaseOrderDisbursements = bffDto.PurchaseOrderDisbursements.Count == 0 ? null : bffDto.PurchaseOrderDisbursements.Select(ToPurchaseOrderDisbursementDto).ToList(),
            };
        }

        private static PurchaseOrderDisbursementBffDto ToPurchaseOrderDisbursementBffDto(PurchaseOrderDisbursementDto dto)
        {
            return new()
            {
                BackOrderQuantity = dto.BackorderQuantity,
                PurchaseOrderNumber = dto.PurchaseOrderNumber,
                Quantity = dto.Quantity,
            };
        }
        
        private static PurchaseOrderDisbursementDto ToPurchaseOrderDisbursementDto(PurchaseOrderDisbursementBffDto bffDto)
        {
            return new()
            {
                BackorderQuantity = bffDto.BackOrderQuantity,
                PurchaseOrderNumber = bffDto.PurchaseOrderNumber,
                Quantity = bffDto.Quantity,
            };
        }
    }
}