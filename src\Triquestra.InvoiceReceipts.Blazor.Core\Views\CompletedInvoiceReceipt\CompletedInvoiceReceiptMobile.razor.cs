﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt
{
    public partial class CompletedInvoiceReceiptMobile
    {
        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; } = string.Empty;

        [Parameter]
        [SupplyParameterFromQuery]
        public string ReturnUrl { get; set; } = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            ViewModel.ReturnUrl = ReturnUrl;
            await ViewModel.InitializeAsync(StockReceiptCode);
            await base.OnInitializedAsync();
        }
    }
}