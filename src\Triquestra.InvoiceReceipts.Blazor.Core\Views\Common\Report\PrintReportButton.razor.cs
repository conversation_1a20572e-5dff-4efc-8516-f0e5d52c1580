﻿using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Microsoft.AspNetCore.Components;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.Report
{
    public partial class PrintReportButton
    {
        [Parameter]
        public string StockReceiptCode { get; set; }

        private IRelayCommand<object> _printReportCommand;

        public bool _isReportDialogVisible;

        public PrintReportButton()
        {
            _printReportCommand = new RelayCommand<object?>(PrintReportHandler);
        }

        private void PrintReportHandler(object? _)
        {
            WeakReferenceMessenger.Default.Send(new ShowReceiptReportMessage(StockReceiptCode));
        }
    }
}