﻿using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Triquesta.InfinityWeb.Common.Authorization;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;

namespace Infinity.InvoiceReceipts.Blazor.Configuration
{
    public static class ConfigureHttpClients
    {
        public static void AddInvoiceReceiptHttpClient(this WebAssemblyHostBuilder builder)
        {
            builder.Services.AddHttpClient(InvoiceReceiptClientSettingConstants.BffHttpClientName, httpClient =>
            {
                httpClient.BaseAddress = new Uri(builder.Configuration[InvoiceReceiptClientSettingConstants.BffHostUrl]!);
            })
                .AddHttpMessageHandler<AntiforgeryHandler>();
            builder.Services.AddTransient(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient(InvoiceReceiptClientSettingConstants.BffHttpClientName));
        }
    }
}