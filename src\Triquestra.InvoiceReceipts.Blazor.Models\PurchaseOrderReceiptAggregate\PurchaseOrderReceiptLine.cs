﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate
{
    public class PurchaseOrderReceiptLine : IAggregateRoot
    {
        public int? LineNumber { get; set; }

        public decimal? ReceiptQuantity { get; set; }

        public string ProductCode { get; set; } = string.Empty;

        public bool? RequestLabel { get; set; }

        public decimal? BackOrderQuantity { get; set; }

        public decimal Cost { get; set; }
    }
}