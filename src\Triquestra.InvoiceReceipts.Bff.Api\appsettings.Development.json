{"InvoiceReceiptsBlazorHostUrl": "https://localhost:5710", "InvoiceReceiptsBffBasePath": "/Triquestra/invoicereceipts_bff", "DomainApi.ClientId": "InfinityCloudClient", "DomainApi.ClientSecret": "P@ssw0rd!012", "TriquestraApi.ApiRoot": "https://infinityapp01-dev.australiaeast.cloudapp.azure.com:8056", "TriquestraApi.BaseUrl": "/Triquestra/", "IdentityServer.ClientId": "Triquestra.Infinity.IdentityServerInvoiceReceiptsDevelopment", "IdentityServer.ClientSecret": "951a18faf7e7e9d9329d91c40d9324c4a2b862bacee1e02bab136b8355ab93b9_dev", "IdentityServerDatabaseConnection": "REPLACE_ME", "AllowedHosts": "*", "AppInsights.Environment": "Development", "AppInsights.ImageName": "1.0.0", "AppInsights.RoleName": "Triquestra.InvoiceReceipts.Bff", "AppInsights.EnableSQLInstrumentation": "false"}