﻿using CommunityToolkit.Mvvm.Messaging;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Triquestra.Common.Bff.DTOs.Bff.Products;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt
{
    public partial class CompletedInvoiceReceiptDesktop : IDisposable
    {
        private bool _isReportDialogVisible;

        private IEnumerable<ProductClientBffDto> _selectedProducts = new List<ProductClientBffDto>();

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; } = string.Empty;

        [Parameter]
        [SupplyParameterFromQuery]
        public string ReturnUrl { get; set; } = string.Empty;

        public CompletedInvoiceReceiptDesktop()
        {
            RegisterMessages();
        }

        protected override async Task OnInitializedAsync()
        {
            ViewModel.ReturnUrl = ReturnUrl;
            ViewModel.RegisterMessages();
            await ViewModel.InitializeAsync(StockReceiptCode);
            await base.OnInitializedAsync();
        }

        private void RegisterMessages()
        {
            WeakReferenceMessenger.Default.Register<CompletedInvoiceReceiptDesktop, ShowReceiptReportMessage>(this, (view, message) =>
            {
                _isReportDialogVisible = true;
                StateHasChanged();
            });
        }

        public override void Dispose()
        {
            WeakReferenceMessenger.Default.UnregisterAll(this);
            ViewModel.UnregisterMessages();
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}