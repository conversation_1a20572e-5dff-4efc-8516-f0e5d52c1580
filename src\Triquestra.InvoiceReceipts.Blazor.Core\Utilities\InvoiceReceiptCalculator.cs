﻿namespace Infinity.InvoiceReceipts.Blazor.Utilities
{
    internal static class InvoiceReceiptCalculator
    {
        public static decimal? CalculateCurrentProfitMargin(decimal? sellPrice, decimal taxRate, decimal? cost)
        {
            return sellPrice.GetValueOrDefault(0) == 0 ? 0 : (sellPrice / (1 + (taxRate / 100)) - cost) / (sellPrice / (1 + (taxRate / 100))) * 100;
        }

        public static decimal CalculateReceiptQuantity(decimal receiptPack, decimal packSize) => receiptPack * packSize;

        public static decimal CalculateReceiptPack(decimal receiptQuantity, decimal packSize) => packSize == 0 ? 0 : receiptQuantity / packSize;

        public static decimal CalculateTotalCost(decimal receiptQuantity, decimal cost) => receiptQuantity * cost;

        public static decimal CalculateCost(decimal totalCost, decimal receiptQuantity) => totalCost / receiptQuantity;

        public static decimal CalculateAdjustment(decimal totalAmount, decimal runningTotal) => totalAmount - runningTotal;

        public static decimal CalculateFreightPerUnit(decimal totalReceiptQuantity, decimal totalFreight) => totalReceiptQuantity == 0 ? 0 : totalFreight / totalReceiptQuantity;

        public static decimal CalculateBackOrder(decimal purchaseQuantity, decimal receiptQuantity) => Math.Max(0, purchaseQuantity - receiptQuantity);
    }
}