﻿@using Infinity.Blazor.UIControls.Constants;
@using Infinity.Blazor.UIControls.Utillities
@using Microsoft.AspNetCore.Components.Authorization
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Views

@inherits LayoutComponentBase

<TriquestraMediaQuery Media="@UIControlConstants.MobileScreenMediaQuery" OnChange="MediaQueryChangeHandler" />
<TelerikRootComponent>
    <AuthorizeView>
        <Authorized>
            <TelerikWindow Modal="true" />
            <TriquestraDialog @ref="@UserNotificationService.DialogBuilderRef" />
            <TriquestraConfirmWithCheckboxDialog @ref="@UserNotificationService.ConfirmWithCheckboxDialogRef" />
            <TriquestraProgress WindowInstance="@UserNotificationService.Instance" @ref="@UserNotificationService.BuilderRef"></TriquestraProgress>
            <TriquestraToastNotification @ref="ToastNotificationService.TriquestraToastNotificationRef" />
            @if (_isMobile == true)
            {
                <MobileLayout MenuItems="_menuItems" UserName="@context.User.Identity.Name">
                    @Body
                </MobileLayout>
            }
            else if (_isMobile == false)
            {
                <DesktopLayout MenuItems="_menuItems">
                    @Body
                </DesktopLayout>
            }
        </Authorized>
        <NotAuthorized>
            <RedirectToLogin />
        </NotAuthorized>
    </AuthorizeView>
</TelerikRootComponent>