﻿@using Infinity.Blazor.UIControls.Layouts
@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList.CompletedProductListItemMobile
@using Telerik.Blazor.Components

<div class="productlist-container-mobile">
    @if (!ProductLines.Any())
    {
        <CardContainer>
            <CardBody Class="no-data-body">
                <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
            </CardBody>
        </CardContainer>
    }
    else
    {
        <label class="product-list-label">@Localizer.GetString(Translations.ProductList)</label>

        @foreach (var item in ProductLines)
        {
            <CompletedProductListItemMobile @key="@item.ProductCode"
                                           IsMatchedToPurchaseOrder="IsMatchedToPurchaseOrder"
                                           ProductLine="@item"
                                           IsExpanded="@IsCardExpand(item.ProductCode!)"
                                           SelectProductCommand="_selectProductCommand"
                                           ElementId="@GetProductItemElementId(item.ProductCode)"/>
        }
    }
</div>