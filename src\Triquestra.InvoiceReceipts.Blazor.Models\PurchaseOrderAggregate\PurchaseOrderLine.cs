﻿using System;
using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate
{
    public class PurchaseOrderLine : IAggregateRoot
    {
        public string Key { get; private set; } = Guid.NewGuid().ToString();

        public int? LineNumber { get; set; }

        public string PurchaseOrderCode { get; set; } = string.Empty;

        public string ProductCode { get; set; } = string.Empty;

        public string Sku { get; set; } = string.Empty;

        public string? Description { get; set; }

        public string SupplierProductCode { get; set; } = string.Empty;

        public string PurchaseUnit { get; set; } = string.Empty;

        public decimal? PurchaseQuantity { get; set; }

        public decimal? ExtendedCostPriceExcludingInputTax { get; set; }

        public decimal? Cost { get; set; }

        public string? Unit { get; set; }

        public string? PackUnit { get; set; }

        public int PackSize { get; set; } = 1;

        public string SupplierCode { get; set; } = string.Empty;

        public string SupplierName { get; set; } = string.Empty;

        public decimal? StandardSellingPrice { get; set; }
        
        public DateTime Logged{ get; set; } = DateTime.MinValue;

        public decimal? BackOrder { get; set; }

        public decimal? MaxBackOrder { get; set; }

        public decimal ReceiptQuantity { get; set; }
    }
}