﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:8781",
      "sslPort": 44356
    }
  },
  "profiles": {
    "Triquestra.InvoiceReceipts.Bff.Api": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:5700;http://localhost:5701",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "InvoiceReceiptsBffBasePath": "/Triquestra/invoicereceipts_bff"
      }
    },
    "IIS Express": {
      "commandName": "IISExpress",
      "launchBrowser": true,
      "launchUrl": "swagger",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
