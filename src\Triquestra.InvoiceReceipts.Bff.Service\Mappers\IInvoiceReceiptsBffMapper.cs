﻿using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InfinityAPI.Transactions.PurchaseOrders.Models.DTOs;
using Triquestra.InfinityAPI.Transactions.StockReceipt.Models.DTOs;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Service.Mappers
{
    public interface IInvoiceReceiptsBffMapper
    {
        TaxedProductObjectBffDto ToTaxedProductObjectBffDto(ProductObjectBffDto bffDto);
        StockReceiptResponseBffDto ToStockReceiptResponseBffDto(StockReciptResponseDto dto);
        InvoiceReceiptCreateDto ToInvoiceReceiptCreateDto(InvoiceReceiptCreateBffDto bffDto);
        InvoiceReceiptSearchRequestDto ToInvoiceReceiptSearchRequestDto(InvoiceReceiptSearchRequestBffDto bffDto);
        InvoiceReceiptBffDto ToInvoiceReceiptBffDto(InvoiceReceiptDto dto);
        InvoiceReceiptUpdateDto ToInvoiceReceiptUpdateDto(InvoiceReceiptUpdateBffDto bffDto);
        InvoiceReceiptStatusUpdateDto ToInvoiceReceiptStatusUpdateDto(InvoiceReceiptStatusUpdateBffDto bffDto);
    }
}