﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Constants;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Triquesta.InfinityWeb.Common.Services;
using Triquesta.InfinityWeb.Common.ViewModels;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList
{
    public partial class ProductListMobile
    {
        private string? _expandedKey = null;
        private string? _swipedKey = null;

        public IRelayCommand<string> _selectProductCommand;
        public IRelayCommand<object> _deleteCommand;

        private Dictionary<string, ProductListItemMobile.ProductListItemMobile?> _productListItemMobileRefDictionary = new();

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IUserNotificationService UserNotificationService { get; set; }

        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        [Parameter]
        public ObservableCollection<ProductLineViewModel> ProductLines { get; set; }

        [Parameter]
        public ICommand? DataChangedCommand { get; set; }

        [Parameter]
        public int SiteCode { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsChangeSellPriceReceipt { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsFreightEditable { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsMatchToPurchaseOrder { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsBackOrders { get; set; }

        public ProductListMobile()
        {
            _selectProductCommand = new RelayCommand<string>(SelectProductChanged);
            _deleteCommand = new AsyncRelayCommand<object>(DeleteProductHandlerAsync);
        }

        protected override Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                _productListItemMobileRefDictionary.Clear();

                foreach (var item in ProductLines)
                {
                    _productListItemMobileRefDictionary.Add(item.Key, null);
                }
            }

            return base.OnAfterRenderAsync(firstRender);
        }

        public void Refresh()
        {
            foreach (var item in _productListItemMobileRefDictionary.Values)
            {
                item.InitializeData();
            }
        }

        public void ResetState()
        {
            _expandedKey = string.Empty;
            _swipedKey = string.Empty;
        }

        private bool IsCardExpand(string key)
        {
            return _expandedKey == key;
        }

        private void SelectProductChanged(string key)
        {
            if (string.IsNullOrEmpty(_expandedKey))
            {
                _expandedKey = key;
            }
            else
            {
                _expandedKey = key == _expandedKey ? null : key;
            }

            if (_expandedKey == null)
            {
                _ = ScrollToProductInputAsync();
            }
            else
            {
                _ = ScrollToUpdateButtonAsync(UIConstants.ProductListItemUpdateButtonIdPrefix + _expandedKey);
            }

            _swipedKey = null;

            StateHasChanged();
        }

        private async Task DeleteProductHandlerAsync(object? parameter)
        {
            var product = parameter as ProductLineViewModel;

            if (product != null)
            {
                var answer = await UserNotificationService.ConfirmCancelAsync(
                    Localizer.GetString(Translations.DeleteItemConfirm_Message),
                    Localizer.GetString(Translations.DeleteConfirmation),
                    Localizer.GetString(Translations.YesDelete));

                if (answer == Answer.Yes)
                {
                    ProductLines.Remove(product);

                    if (DataChangedCommand?.CanExecute(null) == true)
                    {
                        DataChangedCommand.Execute(null);
                    }
                }
            }
        }

        private bool IsCardSwiped(string key)
        {
            return _swipedKey == key;
        }

        private void IsSwipedChanged(string key, bool isSwiped)
        {
            if (!isSwiped)
            {
                _swipedKey = string.Empty;
            }
            else
            {
                if (string.IsNullOrEmpty(_swipedKey))
                {
                    _swipedKey = key;
                }
                else
                {
                    _swipedKey = key == _swipedKey ? null : key;
                }
            }

            _expandedKey = null;

            StateHasChanged();
        }

        private async Task ScrollToUpdateButtonAsync(string elementId)
        {
            const int yOffset = 40;
            await Task.Delay(UIConstants.DelayTimeBeforeScrolling);
            await JSRuntime.InvokeVoidAsync(UIControlJsFunctions.ScrollElementToBottomIfOutOfView, elementId, yOffset);
        }

        private async Task ScrollToProductInputAsync()
        {
            const int yOffset = -110;
            await Task.Delay(UIConstants.DelayTimeBeforeScrolling);
            await JSRuntime.InvokeVoidAsync(UIControlJsFunctions.ScrollIntoView, UIConstants.ProductSearchWrapperId, yOffset);
        }
    }
}