﻿namespace Infinity.InvoiceReceipts.Blazor.Constants
{
    public static class Translations
    {
        public const string NoInvoiceReceiptsFound = "NoInvoiceReceiptsFound";
        public const string ReceiptByInvoiceTitle = "ReceiptByInvoiceTitle";
        public const string ReceiptByInvoiceDescription = "ReceiptByInvoiceDescription";
        public const string CreateNewInvoice = "CreateNewInvoice";
        public const string SearchByInvoiceNumber = "SearchByInvoiceNumber";
        public const string EnterInvoiceNumber = "EnterInvoiceNumber";
        public const string FilterByDateRange = "FilterByDateRange";
        public const string InvoiceReceiptList = "InvoiceReceiptList";
        public const string SearchForInvoiceNumber = "SearchForInvoiceNumber";

        public const string Invoice = "Invoice";
        public const string StartInvoiceReceipt = "StartInvoiceReceipt";
        public const string NewInvoiceReceiptDetails = "NewInvoiceReceiptDetails";
        public const string NewInvoiceReceiptDetails_Description = "NewInvoiceReceiptDetails_Description";
        public const string Site = "Site";
        public const string Supplier = "Supplier";
        public const string InvoiceNumber = "InvoiceNumber";
        public const string InvoiceDate = "InvoiceDate";
        public const string GrossAmount = "GrossAmount";
        public const string TaxAmount = "TaxAmount";
        public const string Total = "Total";
        public const string LoadingSuppliersPlaceholder = "LoadingSuppliersPlaceholder";
        public const string FreightExtra = "FreightExtra";
        public const string StartReceipt = "StartReceipt";
        public const string MatchToPurchaseOrders = "MatchToPurchaseOrders";
        public const string MatchToPurchaseOrders_Description = "MatchToPurchaseOrders_Description";

        public const string InvoiceReceipt = "InvoiceReceipt";
        public const string SaveAsDraft = "SaveAsDraft";
        public const string CompleteReceipt = "CompleteReceipt";
        public const string NewInvoiceReceipt = "NewInvoiceReceipt";
        public const string ReceiptInformation = "ReceiptInformation";
        public const string ProductList_NoData_Message = "ProductList_NoData_Message";
        public const string Product = "Product";
        public const string RequestLabel = "RequestLabel";
        public const string SKU = "SKU";
        public const string SupProductCode = "SupProductCode";
        public const string StartedOn = "StartedOn";
        public const string InvNo = "InvNo";
        public const string Optional = "Optional";
        public const string Tax = "Tax";
        public const string FreightExtraDisbursement = "FreightExtraDisbursement";
        public const string RunningTotal = "RunningTotal";
        public const string AdjustmentRequired = "AdjustmentRequired";
        public const string Insert = "Insert";
        public const string Note = "Note";
        public const string Note_Placeholder = "Note_Placeholder";
        public const string AddProductsForReceipt = "AddProductsForReceipt";
        public const string AddProductsForReceipt_Description = "AddProductsForReceipt_Description";
        public const string AddNewProduct = "AddNewProduct";
        public const string ProductSearchInputPlaceholder = "ProductSearchInputPlaceholder";
        public const string FreightExtraDisbursement_None = "FreightExtraDisbursement_None";
        public const string FreightExtraDisbursement_PerLine = "FreightExtraDisbursement_PerLine";
        public const string FreightExtraDisbursement_PerQty = "FreightExtraDisbursement_PerQty";
        public const string FreightExtraDisbursement_Manual = "FreightExtraDisbursement_Manual";
        public const string AdjustmentRequired_Note = "AdjustmentRequired_Note";
        public const string ScanProductNotFoundMessage = "ScanProductNotFoundMessage";
        public const string Delete = "Delete";
        public const string ReceiptQuantity = "ReceiptQuantity";
        public const string ReceiptPack = "ReceiptPack";
        public const string Cost = "Cost";
        public const string TotalCost = "TotalCost";
        public const string SellPrice = "SellPrice";
        public const string FreightExtras = "FreightExtras";
        public const string OnHandOrder = "OnHandOrder";
        public const string TGPM = "TGPM";
        public const string CGPM = "CGPM";
        public const string ByAnySupplier = "ByAnySupplier";
        public const string DeleteSelectedRows = "DeleteSelectedRows";
        public const string ClearTable = "ClearTable";
        public const string Complete = "Complete";
        public const string ReceiptNote = "ReceiptNote";
        public const string ReceiptQty = "ReceiptQty";
        public const string Update = "Update";
        public const string ProductCode = "ProductCode";
        public const string ProductName = "ProductName";
        public const string ProductSearchInputPlaceholderMobile = "ProductSearchInputPlaceholderMobile";
        public const string ProductList = "ProductList";
        public const string DeleteSelectedItemsConfirm_Message = "DeleteSelectedItemsConfirm_Message";
        public const string DeleteConfirmation = "DeleteConfirmation";
        public const string ClearTableConfirm_Message = "ClearTableConfirm_Message";
        public const string ClearTableConfirmation = "ClearTableConfirmation";
        public const string DeleteItemConfirm_Message = "DeleteItemConfirm_Message";
        public const string YesDelete = "YesDelete";
        public const string YesClear = "YesClear";

        public const string InsertAdjustmentConfirmation = "InsertAdjustmentConfirmation";
        public const string InsertAdjustmentMessage = "InsertAdjustmentMessage";
        public const string YesInsert = "YesInsert";

        public const string InvoiceNo = "InvoiceNo";

        public const string SaveDraft_Confirm_Content = "SaveDraft_Confirm_Content";
        public const string SaveDraft_Confirm_Title = "SaveDraft_Confirm_Title";
        public const string YesSaveAsDraft = "YesSaveAsDraft";
        public const string FreightDisbursementProblem_Title = "FreightDisbursementProblem_Title";
        public const string FreightDisbursementProblem_Content = "FreightDisbursementProblem_Content";
        public const string LeaveConfirmation_Title = "LeaveConfirmation_Title";
        public const string LeaveConfirmation_Content = "LeaveConfirmation_Content";
        public const string Discard = "Discard";
        public const string SaveDraftFailed_Title = "SaveDraftFailed_Title";
        public const string SaveDraftFailed_Content = "SaveDraftFailed_Content";
        public const string InvoiceReceiptDraftSaved = "InvoiceReceiptDraftSaved";
        public const string OK = "OK";

        public const string CreatedUpdated = "CreatedUpdated";
        public const string Status = "Status";
        public const string DraftInvoice = "DraftInvoice";
        public const string CompletedInvoice = "CompletedInvoice";

        public const string CompleteConfirmation = "CompleteConfirmation";
        public const string CompleteConfirmation_Content = "CompleteConfirmation_Content";
        public const string PrintReceipt = "PrintReceipt";
        public const string YesComplete = "YesComplete";
        public const string CompleteFailed_Content = "CompleteFailed_Content";
        public const string CompleteFailed_Title = "CompleteFailed_Title";
        public const string InvoiceReceiptCompleted = "InvoiceReceiptCompleted";

        public const string LoadReceiptFailed = "LoadReceiptFailed";
        public const string LoadReceiptFailed_Content = "LoadReceiptFailed_Content";
        public const string CreatedOn = "CreatedOn";
        public const string DeleteDraft = "DeleteDraft";
        public const string UpdateDraft = "UpdateDraft";

        public const string UpdateDraftConfirmation = "UpdateDraftConfirmation";
        public const string UpdateDraftConfirmation_Content = "UpdateDraftConfirmation_Content";
        public const string YesUpdateDraft = "YesUpdateDraft";
        public const string UpdateReceiptFailed = "UpdateReceiptFailed";
        public const string UpdateReceiptFailed_Content = "UpdateReceiptFailed_Content";

        public const string DeleteDraftConfirm_Content = "DeleteDraftConfirm_Content";
        public const string DeleteDraftFailed = "DeleteDraftFailed";
        public const string DeleteDraftFailed_Content = "DeleteDraftFailed_Content";
        public const string InvoiceReceiptDeleted = "InvoiceReceiptDeleted";

        public const string CompletedOn = "CompletedOn";
        public const string CompletedBy = "CompletedBy";
        public const string ReceiptNo = "ReceiptNo";
        public const string InvoiceTotal = "InvoiceTotal";
        public const string ReceiptTotal = "ReceiptTotal";
        public const string Download = "Download";

        public const string InvoiceReceiptReport = "InvoiceReceiptReport";

        public const string PurchaseOrderDisbursement = "PurchaseOrderDisbursement";
        public const string PONumber = "PONumber";
        public const string Logged = "Logged";
        public const string BackOrder = "BackOrder";
        public const string ItemsOnPO = "ItemsOnPO";
        public const string Receive = "Receive";
        public const string NoPurchaseOrdersToDisburse = "NoPurchaseOrdersToDisburse";

        public const string SelectPurchaseOrder = "SelectPurchaseOrder";
        public const string Confirm = "Confirm";
        public const string NoPurchaseOrdersFound = "NoPurchaseOrdersFound";
        public const string Variance = "Variance";
        public const string BackOrders = "BackOrders";
        public const string InvCost = "InvCost";
        public const string InvQty = "InvQty";
        public const string Label = "Label";
        public const string InvoiceCost = "InvoiceCost";
        public const string InvoiceQuantity = "InvoiceQuantity";
        public const string BackOrdersNote_Mobile = "BackOrdersNote_Mobile";

        public const string BackOrdersConfirmation = "BackOrdersConfirmation";
        public const string BackOrdersConfirmation_Content = "BackOrdersConfirmation_Content";
        public const string YesChange = "YesChange";

        public const string ReceiptCost = "ReceiptCost";

        public const string POMatchProblem = "POMatchProblem";
        public const string POMatchProblemMessage = "POMatchProblemMessage";
    }
}