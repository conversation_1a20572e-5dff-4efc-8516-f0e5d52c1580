﻿using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Blazor.Services
{
    public interface IInvoiceReceiptService
    {
        Task<SystemConfigurations> GetSystemConfigurationsAsync(int? siteCode);
        Task<List<Site>> GetSitesAsync();
        Task<Supplier> GetSupplierAsync(string supplierCode);
        Task<List<Supplier>> GetActiveSuppliersAsync();
        Task<decimal> GetTaxRateAsync();
        Task<Product?> ScanProductAsync(string barcode, int? siteCode);

        Task<List<Product>> GetProductsAsync(List<string> productCodes, int? siteCode);

        Task<List<ProductInventoryDetail>> GetProductInventoriesAsync(IEnumerable<string> productCodes, int? siteCode);
        Task<string> CreateInvoiceReceiptAsync(InvoiceReceiptCreateBffDto requestBffDto);
        Task<List<InvoiceReceipt>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto requestBffDto);
        Task<InvoiceReceipt> GetInvoiceReceiptAsync(string stockReceiptCode);
        Task<string> UpdateInvoiceReceiptAsync(InvoiceReceiptUpdateBffDto requestBffDto);
        Task<string> UpdateInvoiceReceiptStatusAsync(InvoiceReceiptStatusUpdateBffDto requestBffDto);
        Task<List<PurchaseOrder>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto requestBffDto);
        Task<List<PurchaseOrderReceipt>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto requestBffDto);
        Task<List<PurchaseOrder>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto requestBffDto);
    }
}