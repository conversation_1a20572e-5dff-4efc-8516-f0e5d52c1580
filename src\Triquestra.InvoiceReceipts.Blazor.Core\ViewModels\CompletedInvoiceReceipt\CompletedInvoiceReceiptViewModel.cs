using CommunityToolkit.Mvvm.Messaging;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Enums;
using Infinity.InvoiceReceipts.Blazor.Mappers;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using Telerik.DataSource.Extensions;
using Triquesta.InfinityWeb.Common.Base;
using Triquesta.InfinityWeb.Common.Constants;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.Common.Helpers;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.CompletedInvoiceReceipt
{
    public sealed class CompletedInvoiceReceiptViewModel : BaseViewModel
    {
        private readonly NavigationManager _navigationManager;

        private readonly IInvoiceReceiptService _invoiceReceiptService;

        private readonly IInvoiceReceiptModelMapper _modelMapper;

        private readonly IStringLocalizer<Resources> _stringLocalizer;

        private readonly IUserNotificationService _userNotificationService;

        private readonly ITriquestraToastNotificationService _toastNotificationService;

        #region Properties

        public InvoiceReceipt CurrentInvoiceReceipt { get; private set; } = new();

        private string _currentStockReceiptCode = string.Empty;

        public string CurrentStockReceiptCode
        {
            get => _currentStockReceiptCode;
            set => SetValue(ref _currentStockReceiptCode, value);
        }

        private ObservableCollection<ProductLineViewModel> _productLines = new();

        public ObservableCollection<ProductLineViewModel> ProductLines
        {
            get => _productLines;
            set => SetValue(ref _productLines, value);
        }

        private bool _isLoading;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetValue(ref _isLoading, value);
        }

        public string ReturnUrl { get; set; } = string.Empty;

        #endregion Properties

        public CompletedInvoiceReceiptViewModel(
            NavigationManager navigationManager,
            IInvoiceReceiptService invoiceReceiptService,
            IInvoiceReceiptModelMapper modelMapper,
            IStringLocalizer<Resources> stringLocalizer,
            IUserNotificationService userNotificationService,
            ITriquestraToastNotificationService toastNotificationService)
        {
            _navigationManager = navigationManager;
            _invoiceReceiptService = invoiceReceiptService;
            _modelMapper = modelMapper;
            _stringLocalizer = stringLocalizer;
            _userNotificationService = userNotificationService;

            _toastNotificationService = toastNotificationService;
        }

        public async Task InitializeAsync(string stockReceiptCode)
        {
            CurrentStockReceiptCode = stockReceiptCode;

            IsLoading = true;

            var isSucessful = await LoadInvoiceReceiptAsync();

            IsLoading = false;

            if (!isSucessful)
            {
                RedirectToPreviousPage();
            }
        }

        private async Task<bool> LoadInvoiceReceiptAsync()
        {
            CurrentInvoiceReceipt = await _invoiceReceiptService.GetInvoiceReceiptAsync(CurrentStockReceiptCode);

            if (string.IsNullOrEmpty(CurrentInvoiceReceipt.StockReceiptCode) || CurrentInvoiceReceipt.InvoiceReceiptStatus != InvoiceReceiptStatus.Complete)
            {
                await ShowLoadInvoiceReceiptFailedMessageAsync();
                return false;
            }
            else
            {
                var productLines = CurrentInvoiceReceipt.Lines.Select(_modelMapper.MapToProductLineViewModel).ToList();

                ProductLines.Clear();
                ProductLines.AddRange(productLines);

                await AssignProductsDetailsForLoadingInvoiceReceiptAsync(productLines);
            }

            return true;
        }

        public void RegisterMessages()
        {
            WeakReferenceMessenger.Default.Register<CompletedInvoiceReceiptViewModel, InvoiceReceiptChangedMessage>(this, (viewModel, message) =>
            {
                if (message.EventType == InvoiceReceiptChangedEventType.Complete)
                {
                    _toastNotificationService.ShowMessage(_stringLocalizer.GetString(Translations.InvoiceReceiptCompleted), TriquestraToastNotificationType.Success);
                }
            });
        }

        public void UnregisterMessages()
        {
            WeakReferenceMessenger.Default.UnregisterAll(this);
        }

        public void RedirectToPreviousPage()
        {
            _navigationManager.NavigateTo(string.IsNullOrWhiteSpace(ReturnUrl) ? InvoiceReceiptNavigationUrls.InvoiceReceiptOverview : ReturnUrl);
        }

        private async Task AssignProductsDetailsForLoadingInvoiceReceiptAsync(List<ProductLineViewModel> lineViewModels)
        {
            var productCodes = lineViewModels
                .Select(x => x.ProductCode!)
                .Where(x => !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x))
                .ToList();

            var productsDetails = await _invoiceReceiptService.GetProductsAsync(productCodes, CurrentInvoiceReceipt.SiteCode);

            foreach (var lineViewModel in lineViewModels)
            {
                if (lineViewModel.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode)
                {
                    lineViewModel.Description = InvoiceReceiptDataConstants.AdjustmentProductDescription;
                }
                else if (lineViewModel.ProductCode == InvoiceReceiptDataConstants.FreightProductCode)
                {
                    lineViewModel.Description = InvoiceReceiptDataConstants.FreightProductDescription;
                }
                else
                {
                    var product = productsDetails.Find(x => x.ProductCode == lineViewModel.ProductCode)!;

                    lineViewModel.SKU = product.SKU;
                    lineViewModel.Description = product.Description;
                    lineViewModel.SupplierProductCode = product.SupplierProductCode;

                    lineViewModel.SupplierName = product.SupplierName;
                    lineViewModel.TaxRate = product.TaxRate;
                    lineViewModel.PackSize = product.PurchasingRules?.PackSize ?? 1;
                    lineViewModel.TargetMarginPercentage = product.TargetMarginPercentage ?? 0;
                }
            }
        }

        private async Task ShowLoadInvoiceReceiptFailedMessageAsync()
        {
            await _userNotificationService.ConfirmAsync(
                _stringLocalizer.GetString(Translations.LoadReceiptFailed_Content),
                _stringLocalizer.GetString(Translations.LoadReceiptFailed),
                _stringLocalizer.GetString(Translations.OK), false);
        }
    }
}