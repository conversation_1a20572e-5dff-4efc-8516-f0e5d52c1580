﻿using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;

namespace Infinity.InvoiceReceipts.Blazor.Mappers
{
    public interface IInvoiceReceiptModelMapper
    {
        ProductLineViewModel MapToProductLineViewModel(Product product);
        ProductLineViewModel MapToProductLineViewModel(InvoiceReceiptLine invoiceReceiptLine);
        ProductLineViewModel MapToProductLineViewModel(PurchaseOrderLine line);
        InvoiceSummaryModel MapToInvoiceSummaryModel(InvoiceReceipt invoiceReceipt);
        InvoiceReceiptLineBffDto MapToInvoiceReceiptLineBffDto(ProductLineViewModel productLine, bool isMatchedToPurchaseOrders, bool isPurchaseOrderDisbursements);
    }
}