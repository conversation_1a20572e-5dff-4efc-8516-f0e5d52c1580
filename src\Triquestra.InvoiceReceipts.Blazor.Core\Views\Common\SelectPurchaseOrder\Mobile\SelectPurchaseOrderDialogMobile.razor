﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor;
@using Triquesta.InfinityWeb.Common.Base;
@using Infinity.Blazor.UIControls.Windows;
@using Triquestra.InvoiceReceipts.Blazor.Common.Constants

@inherits RazorBaseMvvm<SelectPurchaseOrderViewModel>

@inject IStringLocalizer<Resources> Localizer
@if (Visible)
{
    <div class="page-container">
        <div>
            <div class="page-header">
                <div class="header-group">
                    <div class="back-button">
                        <InfButton FillMode="FillMode.Outline" ClickCommand="_backButtonClickCommand">
                            <IconTemplate>
                                <span class="material-icons icon-md">
                                    arrow_back
                                </span>
                            </IconTemplate>
                        </InfButton>
                    </div>
                    <div class="title">
                        @Localizer.GetString(Translations.SelectPurchaseOrder)
                    </div>
                </div>
                <div class="buttons">
                    <InfButton LabelText="@Localizer.GetString(Translations.Confirm)"
                               Enabled="@(_selectPurchaseOrderListRef?.SelectedItems.Count > 0)"
                               ClickCommand="ConfirmCommand" />
                </div>
            </div>
        </div>
        <SelectPurchaseOrderListMobile @ref="_selectPurchaseOrderListRef" 
                                       PurchaseOrders="ViewModel.PurchaseOrders" 
                                       IsLoading="ViewModel.IsLoading"
                                       SelectedItemsChangedCommand="_selectedItemsChangedCommand"/>
    </div>
}
