﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate
{
    public class Product : IAggregateRoot
    {
        public string ProductCode { get; set; } = string.Empty;

        public string SKU { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public decimal? Cost { get; set; }

        public string Unit { get; set; } = string.Empty;
        public string SupplierCode { get; set; } = string.Empty;
        public string SupplierName { get; set; } = string.Empty;

        public string SupplierProductCode { get; set; } = string.Empty;

        public ProductPurchasingRules? PurchasingRules { get; set; }

        public decimal TaxRate { get; set; }

        public decimal? TargetMarginPercentage { get; set; }

        public decimal? CurrentProfitMargin { get; set; }

        public decimal? StandardSellingPrice { get; set; }

        public int? HierarchyPosition { get; set; }
    }
}