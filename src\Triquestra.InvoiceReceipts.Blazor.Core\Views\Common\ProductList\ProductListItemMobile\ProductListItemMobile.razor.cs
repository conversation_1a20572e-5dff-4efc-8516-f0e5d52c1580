using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Utilities;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Newtonsoft.Json;
using System.Windows.Input;
using Telerik.Blazor.Components;
using Triquesta.InfinityWeb.Common.Constants;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
{
    public partial class ProductListItemMobile
    {
        public IRelayCommand<PanelBarItemClickEventArgs> _panelBarClickCommand;

        private decimal _invoiceCost;
        private decimal _invoiceQuantity;
        private decimal _receiptQuantity;
        private decimal _receiptPack;
        private decimal _backOrders;

        private decimal _cost;
        private decimal? _sellPrice;
        private decimal _totalCost;
        private decimal? _freightExtras;

        private bool _isDataChanged;

        public bool _isExpanded;

        [Parameter]
        public bool IsExpanded
        {
            get { return _isExpanded; }
            set
            {
                _isExpanded = value;
                if (_isExpanded)
                {
                    ExpandedItems = new List<string> { ProductLine.Key };
                }
                else
                {
                    ExpandedItems = new List<string>();
                }
            }
        }

        [Parameter]
        public ProductLineViewModel ProductLine { get; set; }
        
        [Parameter]
        public IRelayCommand<object> DeleteCommand { get; set; }

        [Parameter]
        public IRelayCommand<string> SelectProductCommand { get; set; }

        [Parameter]
        public ICommand? DataChangedCommand { get; set; }

        [Parameter]
        public int SiteCode { get; set; }

        [Parameter]
        public IEnumerable<object> ExpandedItems { get; set; } = new List<string>();

        [Parameter]
        [EditorRequired]
        public bool IsChangeSellPriceReceipt { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsFreightEditable { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsMatchToPurchaseOrder { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsBackOrders { get; set; }

        [Parameter]
        public bool IsSwiped { get; set; }

        [Parameter]
        public EventCallback<bool> IsSwipedChanged { get; set; }


        private IRelayCommand<object>? _updateButtonClickCommand;

        public decimal? CurrentProfitMargin => InvoiceReceiptCalculator.CalculateCurrentProfitMargin(_sellPrice, ProductLine.TaxRate, _cost);
        
        private string UpdateButtonId => UIConstants.ProductListItemUpdateButtonIdPrefix + ProductLine.Key;

        public ProductListItemMobile()
        {
            _updateButtonClickCommand = new RelayCommand<object>(UpdateButtonClickHandler);
            _panelBarClickCommand = new RelayCommand<PanelBarItemClickEventArgs>(PanelBarClickHandler);
        }

        protected override Task OnInitializedAsync()
        {
            InitializeData();

            return base.OnInitializedAsync();
        }

        protected override void OnParametersSet()
        {
            if (!IsExpanded)
            {
                InitializeData();
            }

            base.OnParametersSet();
        }

        public void InitializeData()
        {
            _invoiceCost = ProductLine.InvoiceCost;
            _invoiceQuantity = ProductLine.InvoiceQuantity;
            _receiptQuantity = ProductLine.ReceiptQuantity;
            _receiptPack = ProductLine.ReceiptPack;
            _backOrders = ProductLine.BackOrder;
            _cost = ProductLine.Cost;
            _totalCost = ProductLine.TotalCost;
            _sellPrice = ProductLine.StandardSellingPrice;
            _freightExtras = ProductLine.FreightExtras;
            _isDataChanged = false;
        }

        private void UpdateButtonClickHandler(object? _)
        {
            ProductLine.InvoiceCost = _invoiceCost;
            ProductLine.InvoiceQuantity = _invoiceQuantity;
            ProductLine.ReceiptQuantity = _receiptQuantity;
            ProductLine.ReceiptPack = _receiptPack;
            ProductLine.BackOrder = _backOrders;
            ProductLine.Cost = _cost;
            ProductLine.StandardSellingPrice = _sellPrice ?? 0;
            ProductLine.FreightExtras = _freightExtras ?? 0;
            ProductLine.TotalCost = _totalCost;

            if (SelectProductCommand?.CanExecute(ProductLine.Key!) == true)
            {
                SelectProductCommand.Execute(ProductLine.Key!);
            }

            TriggerDataChangedCommand();
        }

        private void PanelBarClickHandler(PanelBarItemClickEventArgs? args)
        {
            var key = args!.Item.ToString();

            if (SelectProductCommand?.CanExecute(key) == true)
            {
                SelectProductCommand.Execute(key);
            }
        }

        private void TriggerDataChangedCommand()
        {
            if (DataChangedCommand?.CanExecute(null) == true)
            {
                DataChangedCommand.Execute(null);
            }
        }

        private void OnCostChanged(decimal value)
        {
            _cost = value;
            UpdateTotalCost();
            _isDataChanged = true;
        }

        private void UpdateTotalCost()
        {
            _totalCost = InvoiceReceiptCalculator.CalculateTotalCost(_receiptQuantity, _cost);
        }

        private void OnTotalCostChanged(decimal value)
        {
            _totalCost = value;
            _cost = InvoiceReceiptCalculator.CalculateCost(_totalCost, _receiptQuantity);
            _isDataChanged = true;
        }

        private void OnSellPriceChanged(decimal value)
        {
            _sellPrice = value;
            _isDataChanged = true;
        }
        
        private void OnFreightExtrasChanged(decimal? value)
        {
            _freightExtras = value;
            _isDataChanged = true;
        }

        private void OnInvoiceQuantityChanged(decimal value)
        {
            _invoiceQuantity = value;
            
            if (_receiptQuantity < _invoiceQuantity)
            {
                OnReceiptQuantityChanged(value);
            }

            _isDataChanged = true;
        }

        private void OnInvoiceCostChanged(decimal value)
        {
            _invoiceCost = value;
            _isDataChanged = true;
        }

        private void OnReceiptQuantityChanged(decimal value)
        {
            _receiptQuantity = value;
            _receiptPack = InvoiceReceiptCalculator.CalculateReceiptPack(_receiptQuantity, ProductLine.PackSize);
            UpdateBackOrders();
            UpdateTotalCost();
            _isDataChanged = true;
        }

        private void OnReceiptPackChanged(decimal value)
        {
            _receiptPack = value;
            _receiptQuantity = InvoiceReceiptCalculator.CalculateReceiptQuantity(_receiptPack, ProductLine.PackSize);
            UpdateBackOrders();
            UpdateTotalCost();
            _isDataChanged = true;
        }

        private void UpdateBackOrders()
        {
            _backOrders = IsBackOrders ? InvoiceReceiptCalculator.CalculateBackOrder(ProductLine.PurchaseQuantity, _receiptQuantity) : 0;
        }

        private void OnBackOrdersChanged(decimal value)
        {
            if (_receiptQuantity + value <= ProductLine.PurchaseQuantity)
            {
                _backOrders = value;
                _isDataChanged = true;
            }
        }

        private string GetItemCardStyleClass()
        {
            var classList = "product-item-wrapper " 
                + (ProductLine.IsFlaggedForUpdatedState ? "productlist-item-blue-color" : string.Empty);

            if (CurrentProfitMargin < ProductLine.TargetMarginPercentage)
            {
                classList += " text-red";
            }

            if (ProductLine.IsFlaggedForUpdatedState)
            {
                // no need to await, it will block the rendering
                _ = RemoveBackgroundColorAsync();
            }

            return classList;
        }

        private async Task RemoveBackgroundColorAsync()
        {
            await Task.Delay(CommonUIConstants.TimeToHighlightBackgroundMiliseconds);
            ProductLine.IsFlaggedForUpdatedState = false;
            StateHasChanged();
        }
    }
}