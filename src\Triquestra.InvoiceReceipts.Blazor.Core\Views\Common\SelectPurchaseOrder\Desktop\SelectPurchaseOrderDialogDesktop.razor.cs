using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using System.Windows.Input;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Desktop
{
    public partial class SelectPurchaseOrderDialogDesktop : IDisposable
    {
        private SelectPurchaseOrderListDesktop? _selectPurchaseOrderListRef;

        private ICommand _selectedItemsChangedCommand;

        [Parameter]
        public bool Visible { get; set; }

        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        [Parameter]
        public IRelayCommand<object>? ConfirmCommand { set; get; }

        public IEnumerable<PurchaseOrder> SelectedItems => _selectPurchaseOrderListRef?.SelectedItems ?? new List<PurchaseOrder>();

        public SelectPurchaseOrderDialogDesktop()
        {
            _selectedItemsChangedCommand = new RelayCommand(StateHasChanged);
        }

        protected override Task OnInitializedAsync()
        {
            ViewModel.PurchaseOrders.CollectionChanged += PurchaseOrders_CollectionChanged;
            return base.OnInitializedAsync();
        }

        private void PurchaseOrders_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        public async Task LoadDataAsync(int siteCode, string supplierCode)
        {
            await ViewModel.InitializeAsync(siteCode, supplierCode);
        }

        private async Task CloseDialogAsync()
        {
            Visible = false;

            if (VisibleChanged.HasDelegate)
            {
                await VisibleChanged.InvokeAsync();
            }
        }

        public override void Dispose()
        {
            ViewModel.PurchaseOrders.CollectionChanged -= PurchaseOrders_CollectionChanged;
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}