﻿using Microsoft.Extensions.Caching.Memory;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Hierarchies;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Bff.Service.ApiWrapper;
using Triquestra.InvoiceReceipts.Bff.Service.Mappers;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;
using CacheKeys = Triquestra.InvoiceReceipts.Bff.Service.Constants.CacheKeys;

namespace Triquestra.InvoiceReceipts.Bff.Service
{
    public class InvoiceReceiptBffService : IInvoiceReceiptBffService
    {
        private readonly IBffDataService _bffDataService;
        private readonly AppConfigurationModel _configurationModel;
        private readonly IMemoryCache _memoryCache;
        private readonly IInvoiceReceiptsBffMapper _bffMapper;
        private readonly InvoiceReceiptApiWrapper _wrapper;
        private readonly IPurchaseOrderBffService _purchaseOrderBffService;

        public InvoiceReceiptBffService(
            IBffDataService bffDataService,
            AppConfigurationModel configurationModel,
            IMemoryCache memoryCache,
            IInvoiceReceiptsBffMapper mapper,
            InvoiceReceiptApiWrapper wrapper,
            IPurchaseOrderBffService purchaseOrderBffService)
        {
            _bffDataService = bffDataService;
            _configurationModel = configurationModel;
            _memoryCache = memoryCache;
            _bffMapper = mapper;
            _wrapper = wrapper;
            _purchaseOrderBffService = purchaseOrderBffService;
        }

        public async Task<BffResultDto<SupplierBffDto>> GetSupplierAsync(string supplierCode)
        {
            var supplier = await _bffDataService.GetSupplierAsync(supplierCode);
            return new() { Data = supplier ?? new(), StatusCode = 200 };
        }

        public async Task<BffResultDto<List<SupplierBffDto>>> GetActiveSuppliersAsync()
        {
            var suppliers = await _bffDataService.GetAllSuppliers(_configurationModel.CacheExpirationInMinutes);
            suppliers = suppliers.Where(x => x.Archived != true).ToList();
            return new() { Data = suppliers, StatusCode = 200 };
        }

        public async Task<BffResultDto<List<SiteBffDto>>> GetSitesAsync()
        {
            var cachedSites = await GetAllSitesFromCacheAsync();
            return new() { Data = cachedSites ?? new(), StatusCode = 200 };
        }

        public async Task<BffResultDto<TaxedProductObjectBffDto>> GetProductAsync(string productCode, short? siteCode)
        {
            var productsResponse = await _bffDataService.GetProductAsync(productCode, siteCode);

            if (!productsResponse.IsSucessful)
            {
                return new() { Data = new(), ErrorMsg = productsResponse.ErrorMsg, StatusCode = productsResponse.StatusCode };
            }

            var product = _bffMapper.ToTaxedProductObjectBffDto(productsResponse.Data);

            await AssignTaxRateAndTargetMarginPercentageAsync(new List<TaxedProductObjectBffDto> { product });

            return new() { Data = product, StatusCode = 200 };
        }

        public async Task<BffResultDto<List<TaxedProductObjectBffDto>>> GetProductsAsync(List<string> productCodes, short? siteCode)
        {
            var productsResponse = await _bffDataService.GetProductsAsync(productCodes, siteCode);

            if (!productsResponse.IsSucessful)
            {
                return new() { Data = new(), ErrorMsg = productsResponse.ErrorMsg, StatusCode = productsResponse.StatusCode };
            }

            var products = productsResponse.Data.Select(_bffMapper.ToTaxedProductObjectBffDto).ToList();

            await AssignTaxRateAndTargetMarginPercentageAsync(products);

            return new() { Data = products, StatusCode = 200 };
        }

        public BffResultDto<StockReceiptResponseBffDto> CreateInvoiceReceipt(InvoiceReceiptCreateBffDto bffDto)
        {
            var invoiceReceiptCreateDto = _bffMapper.ToInvoiceReceiptCreateDto(bffDto);
            var result = _wrapper.ApiWrapper.StockReceiptProxy.CreateInvoiceReceipt(invoiceReceiptCreateDto);

            if (result.IsSucessful && result.Data != null)
            {
                return new() { Data = _bffMapper.ToStockReceiptResponseBffDto(result.Data), StatusCode = 200 };
            }

            return new() { Data = new(), ErrorMsg = result.ErrorMessage, StatusCode = result.StatusCode };
        }

        public async Task<BffResultDto<List<InvoiceReceiptBffDto>>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto requestBffDto)
        {
            var searchDto = _bffMapper.ToInvoiceReceiptSearchRequestDto(requestBffDto);
            var response = _wrapper.ApiWrapper.StockReceiptProxy.SearchInvoiceReceipts(searchDto);

            if (!response.IsSucessful)
            {
                return new() { Data = new(), ErrorMsg = response.ErrorMessage, StatusCode = response.StatusCode };
            }

            var orderedStatusList = new[] { InvoiceReceiptStatus.Draft, InvoiceReceiptStatus.Complete };

            var invoiceReceipts = response.Data.SearchResult
                .Select(_bffMapper.ToInvoiceReceiptBffDto)
                .Where(x => orderedStatusList.Contains(x.InvoiceReceiptStatus))
                .OrderBy(x => Array.IndexOf(orderedStatusList, x.InvoiceReceiptStatus))
                .ThenByDescending(x => x.Updated ?? x.Created)
                .ToList();

            if (invoiceReceipts.Count > 0)
            {
                await Task.WhenAll(AssignSiteNamesAsync(invoiceReceipts), AssignSupplierNamesAsync(invoiceReceipts));
            }

            return new() { Data = invoiceReceipts, StatusCode = 200 };
        }

        public async Task<BffResultDto<InvoiceReceiptBffDto>> GetInvoiceReceiptsAsync(string stockReceiptCode)
        {
            var response = _wrapper.ApiWrapper.StockReceiptProxy.GetInvoiceReceiptByCode(stockReceiptCode);

            if (!response.IsSucessful)
            {
                return new() { Data = new(), ErrorMsg = response.ErrorMessage, StatusCode = response.StatusCode };
            }

            var invoiceReceipt = _bffMapper.ToInvoiceReceiptBffDto(response.Data);
            var invoiceReceipts = new List<InvoiceReceiptBffDto> { invoiceReceipt };

            await Task.WhenAll(
                AssignSiteNamesAsync(invoiceReceipts),
                AssignSupplierNamesAsync(invoiceReceipts),
                AssignDetailsFromMatchingPurchaseOrders(invoiceReceipt));

            return new() { Data = invoiceReceipt, StatusCode = 200 };
        }

        public BffResultDto<StockReceiptResponseBffDto> UpdateInvoiceReceipt(InvoiceReceiptUpdateBffDto requestBffDto)
        {
            var requestDto = _bffMapper.ToInvoiceReceiptUpdateDto(requestBffDto);
            var response = _wrapper.ApiWrapper.StockReceiptProxy.UpdateInvoiceReceipt(requestBffDto.StockReceiptCode, requestDto);

            if (response.IsSucessful && response.Data != null)
            {
                return new() { Data = _bffMapper.ToStockReceiptResponseBffDto(response.Data), StatusCode = 200 };
            }

            return new() { Data = new(), ErrorMsg = response.ErrorMessage, StatusCode = response.StatusCode };
        }

        public BffResultDto<StockReceiptResponseBffDto> UpdateInvoiceReceiptStatus(InvoiceReceiptStatusUpdateBffDto requestBffDto)
        {
            var requestDto = _bffMapper.ToInvoiceReceiptStatusUpdateDto(requestBffDto);
            var response = _wrapper.ApiWrapper.StockReceiptProxy.UpdateInvoiceReceiptStatus(requestBffDto.StockReceiptCode, requestDto);

            if (response.IsSucessful && response.Data != null)
            {
                return new() { Data = _bffMapper.ToStockReceiptResponseBffDto(response.Data), StatusCode = 200 };
            }

            return new() { Data = new(), ErrorMsg = response.ErrorMessage, StatusCode = response.StatusCode };
        }

        private async Task AssignSiteNamesAsync(List<InvoiceReceiptBffDto> invoiceReceipts)
        {
            var sites = await GetAllSitesFromCacheAsync();
            if (sites != null && sites.Count > 0)
            {
                foreach (var item in invoiceReceipts)
                {
                    var site = sites.Find(x => x.SiteCode == item.SiteCode);
                    item.SiteName = site?.Name ?? string.Empty;
                }
            }
        }

        private async Task AssignSupplierNamesAsync(List<InvoiceReceiptBffDto> invoiceReceipts)
        {
            var suppliers = await _bffDataService.GetAllSuppliers();
            if (suppliers != null && suppliers.Count > 0)
            {
                foreach (var item in invoiceReceipts)
                {
                    var supplier = suppliers.Find(x => x.SupplierCode == item.SupplierCode);
                    item.SupplierName = supplier?.SupplierName ?? string.Empty;
                }
            }
        }

        private async Task AssignDetailsFromMatchingPurchaseOrders(InvoiceReceiptBffDto invoiceReceipt)
        {
            if (!invoiceReceipt.IsMatchedToPurchaseOrders)
            {
                return;
            }

            var purchaseOrderCodes = invoiceReceipt.Lines.SelectMany(x => x.PurchaseOrderDisbursements).Select(x => x.PurchaseOrderNumber).Distinct().ToList();
            var purchaseOrders = await _purchaseOrderBffService.GetPurchaseOrdersAsync(purchaseOrderCodes);

            invoiceReceipt.HasReceiptedPurchaseOrder = purchaseOrders.Exists(x => x.PurchaseOrderStatus == PurchaseOrderStatus.Complete);

            AssignPurchaseQuantitiesFromMatchingPurchaseOrders(invoiceReceipt.Lines, purchaseOrders);
        }

        private static void AssignPurchaseQuantitiesFromMatchingPurchaseOrders(List<InvoiceReceiptLineBffDto> invoiceReceiptLines, List<PurchaseOrderBffDto> purchaseOrders)
        {
            foreach (var line in invoiceReceiptLines)
            {
                foreach (var disbursement in line.PurchaseOrderDisbursements)
                {
                    var purchaseOrder = purchaseOrders.Find(x => x.PurchaseOrderCode == disbursement.PurchaseOrderNumber);
                    var product = purchaseOrder?.Lines.Find(x => x.ProductCode == line.ProductCode);

                    disbursement.PurchaseQuantity = product?.PurchaseQuantity ?? 0;
                }
            }
        }

        private async Task AssignTaxRateAndTargetMarginPercentageAsync(List<TaxedProductObjectBffDto> products)
        {
            var taxesResult = _bffDataService.GetTaxes(_configurationModel.CacheExpirationInMinutes);

            foreach (var product in products)
            {
                // Assign TaxRate
                if (taxesResult.IsSucessful && taxesResult.Data.Count > 0)
                {
                    var tax = taxesResult.Data.Find(x => x.TaxCode == product.TaxCode);
                    product.TaxRate = tax?.TaxRate;
                }

                // Assign TargetMarginPercentage
                if (product.TargetMarginPercentage is null or 0 && product.HierarchyPosition.HasValue)
                {
                    product.TargetMarginPercentage = await GetTargetMarginPercentageAsync(product.HierarchyPosition.Value);
                }
            }
        }

        private async Task<decimal> GetTargetMarginPercentageAsync(int hierarchyCode)
        {
            var hierarchiesResponse = await _bffDataService.GetHierarchiesAsync(0);

            if (!hierarchiesResponse.IsSucessful)
            {
                return 0;
            }

            var resultValue = 0m;

            foreach (var item in hierarchiesResponse.Data)
            {
                var searchResult = SearchForTargetMarginPercentage(item, hierarchyCode);

                if (searchResult.HasValue)
                {
                    resultValue = searchResult.Value;
                    break;
                }
            }

            return resultValue;
        }

        private static decimal? SearchForTargetMarginPercentage(HierarchyBffDto parent, int hierarchyCode)
        {
            if (parent.HierarchyCode == hierarchyCode)
            {
                return parent.TargetMarginPercentage ?? 0;
            }

            if (parent.Children.Count > 0)
            {
                foreach (var child in parent.Children)
                {
                    if (child.HierarchyCode == hierarchyCode)
                    {
                        return child.TargetMarginPercentage ?? parent.TargetMarginPercentage ?? 0;
                    }

                    if (child.Children.Count > 0)
                    {
                        var grandchild = child.Children.Find(x => x.HierarchyCode == hierarchyCode);
                        if (grandchild != null)
                        {
                            return grandchild.TargetMarginPercentage ?? child.TargetMarginPercentage ?? parent.TargetMarginPercentage ?? 0;
                        }
                    }
                }
            }

            return null;
        }

        private async Task<List<SiteBffDto>> GetAllSitesFromCacheAsync()
        {
            return await _memoryCache.GetOrCreateAsync(
                CacheKeys.AllSites,
                async cacheEntry =>
                {
                    cacheEntry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_configurationModel.CacheExpirationInMinutes);
                    return await _bffDataService.GetSites();
                }) ?? new();
        }
    }
}