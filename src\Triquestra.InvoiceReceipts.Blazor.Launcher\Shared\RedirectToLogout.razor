﻿@inject NavigationManager Navigation
@inject IConfiguration Configuration

@code {
    [Parameter]
    public string? ReturnUrl { get; set; }

    protected override void OnInitialized()
    {
        if (string.IsNullOrWhiteSpace(ReturnUrl))
            Navigation.NavigateTo(Navigation.ToBaseRelativePath(Navigation.Uri));

        var bffHost = Configuration["InvoiceReceiptsBffHostUrl"];
        var bffBasePath = Configuration["InvoiceReceiptsBffBasePath"];

        var currentUri = new Uri(Navigation.Uri);
        var blazorAppRedirectUrl = $"{currentUri.Scheme}://{currentUri.Authority}/app/inventory";
        var returnUrl = Uri.EscapeDataString($"{bffBasePath}/AuthenticationRedirect/logout?returnFullUrl={blazorAppRedirectUrl}");

        Navigation.NavigateTo($"{bffHost}{ReturnUrl}&returnUrl={returnUrl}", forceLoad: true);
    }
}
