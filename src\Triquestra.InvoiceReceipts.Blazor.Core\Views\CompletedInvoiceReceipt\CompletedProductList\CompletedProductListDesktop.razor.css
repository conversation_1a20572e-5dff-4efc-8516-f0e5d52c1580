﻿label {
    font-weight: 500;
    font-size: 14px;
    padding: 5px 0;
}

.productlist-container {
    margin-top: -15px;
}

.productlist-container ::deep .card-component {
    padding: 0px;
}

.productlist-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 48px 24px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.product_description {
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    color: #202020;
}

.product_code {
    font-weight: 400;
    font-size: 12px;
    line-height: 14.06px;
    color: #898989;
}

::deep .k-table-row.text-red td,
::deep .k-table-row.text-red .product_description,
::deep .k-table-row.text-red .product_code {
    color: red;
}

.productlist-container-actions {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.productlist-container ::deep .k-grid-content .k-grid-table td {
    vertical-align: top;
    padding-top: 1rem;
}

.productlist-container ::deep .cost-value-box {
    border-radius: 8px;
    background-color: #F9F9F9;
    padding: 0 10px;
    border: 1px solid #DCDCDC;
    margin-top: -0.8rem;
    height: 48px;
    line-height: 48px;
    text-align: right;
}

.productlist-container ::deep .triquestra-gridview {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}