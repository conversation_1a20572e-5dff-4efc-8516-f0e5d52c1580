@using Infinity.Blazor.UIControls.Buttons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor;
@using Triquesta.InfinityWeb.Common.Base;
@using Infinity.Blazor.UIControls.Windows;
@using Triquestra.InvoiceReceipts.Blazor.Common.Constants

@inherits RazorBaseMvvm<SelectPurchaseOrderViewModel>

@inject IStringLocalizer<Resources> Localizer

<InfWindow Visible="@Visible" VisibleChanged="VisibleChanged" Class="select-po-dialog" HasCloseButton="false" IsModal>
    <TitleContent>
        <div class="ms-3 w-100 d-flex align-items-center justify-content-between">
            <div>
                <span role="button">
                    <ArrowBackIcon OnClick="CloseDialogAsync" Width="24" Height="24" />
                </span>
                <span Class="ms-3">@Localizer.GetString(Translations.SelectPurchaseOrder)</span>
            </div>
            <InfButton LabelText="@Localizer.GetString(Translations.Confirm)"
                       Enabled="@(_selectPurchaseOrderListRef?.SelectedItems.Count() > 0)"
                       ClickCommand="ConfirmCommand" />
        </div>
    </TitleContent>
    <ChildContent>
        <div style="height: 100%; max-width: 100%">
            <SelectPurchaseOrderListDesktop @ref="_selectPurchaseOrderListRef"
                                            PurchaseOrders="ViewModel.PurchaseOrders"
                                            IsLoading="ViewModel.IsLoading"
                                            SelectedItemsChangedCommand="_selectedItemsChangedCommand" />
        </div>
    </ChildContent>
</InfWindow>

<style>
    /* Cannot use scoped css because the window is created at a dynamic location and can not be wrapped in a html tag. */
    .select-po-dialog {
        width: 70%;
        height: 500px;
        top: 100px !important;
        left: calc((100vw - 70%) / 2) !important;
        transform: none !important;
    }

        .select-po-dialog .k-window-content {
            overflow: hidden;
            position: static;
        }

        .select-po-dialog .k-window-title {
            font-size: 18px;
            font-weight: 600;
        }

        /* filter popup */
        .select-po-dialog .productlist-container .k-animation-container {
            margin-left: calc((100vw - 70%) / -2 + 14.5%);
            margin-top: -100px;
        }

             /* filter dropdownlist */
            .select-po-dialog .productlist-container .k-animation-container[aria-label="Options list"] {
                margin-left: -22.3%;
            }
</style>