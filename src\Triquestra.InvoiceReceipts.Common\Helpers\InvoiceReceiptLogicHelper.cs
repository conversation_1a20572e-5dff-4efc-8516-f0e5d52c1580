﻿using Triquestra.InvoiceReceipts.Common.Constants;

namespace Triquestra.InvoiceReceipts.Common.Helpers
{
    public static class InvoiceReceiptLogicHelper
    {
        public static bool IsAdjustmentOrFreightProductCode(string productCode)
        {
            return productCode == InvoiceReceiptDataConstants.AdjustmentProductCode || productCode == InvoiceReceiptDataConstants.FreightProductCode;
        }
    }
}
