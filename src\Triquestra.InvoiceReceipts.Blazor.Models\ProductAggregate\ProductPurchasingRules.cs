﻿namespace Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate
{
    public class ProductPurchasingRules
    {
        public bool? BlockPurchaseOrders { get; set; }

        public bool? ManualPurchaseOrdersOnly { get; set; }

        public decimal? MinimumQuantity { get; set; }

        public decimal? MaximumQuantity { get; set; }

        public int? PackSize { get; set; }

        public string PackUnit { get; set; } = string.Empty;
    }
}
