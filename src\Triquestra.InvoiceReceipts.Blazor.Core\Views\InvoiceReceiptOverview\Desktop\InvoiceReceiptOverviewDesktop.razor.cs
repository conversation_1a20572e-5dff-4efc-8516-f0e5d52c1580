﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.CalendarPickers;
using Infinity.InvoiceReceipts.Blazor.Constants;
using System.Windows.Input;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Desktop
{
    public partial class InvoiceReceiptOverviewDesktop : IDisposable
    {
        private InvoiceReceiptOverviewDesktopGridView _gridViewRef;
        private InfCalendarPicker _calendarPickerRef;

        private InfCalendarDateRanges? _currentDateRanges;
        private string _currentInvoiceCode = string.Empty;
        private string _lastSearchedInvoiceCode = string.Empty;
        private TriquestraDateRangeType _previousSelectedDateRangeType = TriquestraDateRangeType.ThisMonth;

        private bool _isCalendarEnabled = true;

        private ICommand _setDatesCommand;
        private ICommand _searchBoxChangedCommand;

        public InvoiceReceiptOverviewDesktop()
        {
            _setDatesCommand = new AsyncRelayCommand<InfCalendarDateRanges>(SetDateRangesHandler);
            _searchBoxChangedCommand = new AsyncRelayCommand<string>(SearchBoxChangedHandler);
        }
        
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);

            if (firstRender)
            {
                await Task.Delay(UIConstants.DelayBeforeExecutingPageLoadedAction);
                ViewModel.ShowNotifications();
            }
        }

        private async Task SetDateRangesHandler(InfCalendarDateRanges? dateRanges)
        {
            _currentDateRanges = dateRanges;
            await LoadDataAsync();
        }

        private async Task SearchBoxChangedHandler(string? _)
        {
            if (_currentInvoiceCode != _lastSearchedInvoiceCode)
            {
                _lastSearchedInvoiceCode = _currentInvoiceCode;
                
                if (string.IsNullOrEmpty(_currentInvoiceCode))
                {
                    _isCalendarEnabled = true;
                    await _calendarPickerRef.SetSelectedDateRangeAsync(_previousSelectedDateRangeType);
                }
                else
                {
                    _isCalendarEnabled = false;
                    _previousSelectedDateRangeType = _calendarPickerRef.SelectedDateRange;
                }
                
                await LoadDataAsync();

                StateHasChanged();
            }
        }

        private async Task LoadDataAsync()
        {
            var hasData = await _gridViewRef.LoadDataAsync(_currentInvoiceCode, _currentDateRanges!.RangeStart, _currentDateRanges!.RangeEnd);

            if (!string.IsNullOrEmpty(_currentInvoiceCode) && hasData)
            {
                await _calendarPickerRef.SetSelectedDateRangeAsync(TriquestraDateRangeType.CustomDate);
            }
        }

        public override void Dispose()
        {
            ViewModel.ClearNotifications();
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}