﻿using Infinity.InvoiceReceipts.Blazor.Mappers;
using Infinity.InvoiceReceipts.Blazor.StateContainers;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Infinity.InvoiceReceipts.Blazor.ViewModels.CompletedInvoiceReceipt;
using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptDetails;
using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview;
using Microsoft.Extensions.DependencyInjection;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.InvoiceReceipts.Blazor.BffClient;
using Triquestra.InvoiceReceipts.Blazor.Mappers;
using Triquestra.InvoiceReceipts.Blazor.Services;

namespace Infinity.InvoiceReceipts.Blazor.Configuration
{
    public static class ConfigureDependencyInjection
    {
        public static void AddInvoiceReceiptDependencies(this IServiceCollection services)
        {
            AddServices(services);
            AddViewModels(services);
        }

        private static void AddServices(IServiceCollection services)
        {
            services.AddSingleton<AuthenticationService>();
            services.AddTransient<IInvoiceReceiptBffClient, InvoiceReceiptBffClient>();
            services.AddTransient<IInvoiceReceiptMapper, InvoiceReceiptMapper>();
            services.AddTransient<IInvoiceReceiptService, InvoiceReceiptService>();
            services.AddTransient<IInvoiceReceiptModelMapper, InvoiceReceiptModelMapper>();
            services.AddSingleton<BlazorStateChangedService>();
        }

        private static void AddViewModels(IServiceCollection services)
        {
            // These view models are registered as singletons to maintain the list of items and highlight the deleted item upon returning from the Invoice Details page.
            services.AddSingleton<InvoiceReceiptOverviewViewModel>();
            services.AddSingleton<InvoiceReceiptOverviewDataListViewModel>();

            services.AddTransient<CreateInvoiceViewModel>();
            services.AddTransient<InvoiceReceiptDetailsViewModel>();
            services.AddTransient<CompletedInvoiceReceiptViewModel>();
            services.AddTransient<PurchaseOrderDisbursementViewModel>();
            services.AddTransient<SelectPurchaseOrderViewModel>();
        }
    }
}