﻿namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrders
{
    public class PurchaseOrderLineBffDto
    {
        public int? LineNumber { get; set; }

        public string ProductCode { get; set; } = string.Empty;

        public string Sku { get; set; } = string.Empty;

        public string Description { get; set; } = string.Empty;

        public string SupplierProductCode { get; set; } = string.Empty;

        public string PurchaseUnit { get; set; } = string.Empty;

        public decimal? PurchaseQuantity { get; set; }
        
        public decimal? BackOrder { get; set; }

        public decimal? UnitCostPriceExcludingInputTax { get; set; }

        public decimal? ExtendedCostPriceExcludingInputTax { get; set; }

        public decimal? TotalInputTax { get; set; }

        public string LineStatus { get; set; } = string.Empty;
    }
}