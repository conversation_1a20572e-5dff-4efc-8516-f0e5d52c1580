﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Triquesta.InfinityWeb.Common" Version="1.2.26" />
    <PackageReference Include="Triquestra.Common.Bff.DTOs" Version="1.2.48" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Common\Triquestra.InvoiceReceipts.Blazor.Common.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.DTOs\Triquestra.InvoiceReceipts.DTOs.csproj" />
  </ItemGroup>

</Project>
