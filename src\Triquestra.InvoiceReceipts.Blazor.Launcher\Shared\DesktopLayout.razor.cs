﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Telerik.Blazor.Components;
using Triquestra.Common.Authorization.Authentication;
using Infinity.Blazor.Components.Navigation.Models;

namespace Triquestra.InvoiceReceipts.Blazor.Launcher.Shared;

public partial class DesktopLayout
{
    private const string LocalStorageMenuDrawerStateKey = "MenuDrawerState";

    [Inject]
    public ILocalStorageService LocalStorage { get; set; }

    [Inject]
    public AuthenticationService authenticationService { get; set; }

    [Inject]
    public IJSRuntime IJSRuntime { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public IEnumerable<MenuItemModel> MenuItems { get; set; }

    private TelerikDrawer<MenuItemModel> MenuDrawer { get; set; }

    private bool Expanded { get; set; } = true;

    protected override async Task OnInitializedAsync()
    {
        var hasKey = await LocalStorage.ContainKeyAsync(LocalStorageMenuDrawerStateKey);
        if (hasKey)
        {
            Expanded = await LocalStorage.GetItemAsync<bool>(LocalStorageMenuDrawerStateKey);
        }
        else
        {
            Expanded = true;
        }
    }

    protected override Task OnAfterRenderAsync(bool firstRender)
    {
        return base.OnAfterRenderAsync(firstRender);
    }

    private async Task ToggleMenuDrawer()
    {
        if (Expanded)
        {
            await MenuDrawer.CollapseAsync();
        }
        else
        {
            await MenuDrawer.ExpandAsync();
        }
    }

    private async Task ExpandedChangedHandler(bool value)
    {
        Expanded = value;
        await LocalStorage.SetItemAsync(LocalStorageMenuDrawerStateKey, value);
    }
}