﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate
{
    public class Supplier : IAggregateRoot
    {
        public string SupplierCode { get; set; } = string.Empty;

        public string ExternalCode { get; set; } = string.Empty;

        public string SupplierName { get; set; } = string.Empty;

        public string SupplierShortName { get; set; } = string.Empty;

        public string WebSite { get; set; } = string.Empty;

        public string TaxNumber { get; set; } = string.Empty;

        public Address Address { get; set; } = new();

        public string Contact { get; set; } = string.Empty;

        public string Email { get; set; } = string.Empty;

        public string PrimaryPhoneNumber { get; set; } = string.Empty;

        public string SecondaryPhoneNumber { get; set; } = string.Empty;

        public string FaxNumber { get; set; } = string.Empty;

        public string Currency { get; set; } = string.Empty;

        public string Notes { get; set; } = string.Empty;

        public DateTime? Created { get; set; }

        public DateTime? Updated { get; set; }

        public bool? Archived { get; set; }

        public bool? IsDefaultBackOrderStatus { get; set; }
    }
}