@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Selectors.RadioGroup
@using Infinity.Blazor.UIControls.Tooltips
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums
@using Triquestra.InvoiceReceipts.Common.Constants

<div class="summary-panel-container">
    <ExpandableCardContainer>
        <TitleContent>
            <div class="position-relative">
                <div>
                    @if (IsDraft)
                    {
                        <StatusTag Status="@InvoiceReceiptStatus.Draft" />
                    }
                    else
                    {
                        @Localizer.GetString(Translations.NewInvoiceReceipt)
                    }
                </div>
            </div>
        </TitleContent>
        <DescriptionContent>
            @Localizer.GetString(IsDraft ? Translations.CreatedOn : Translations.StartedOn)
            <strong>&nbsp;@InvoiceSummary.InvoiceDate.ToString("dd MMMM yyyy")</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.Site)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.SelectedSite?.Name</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.Supplier)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.SelectedSupplier?.SupplierName</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.InvNo)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.InvoiceNumber</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.FreightExtra)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.FreightExtra?.ToString("C")</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.Tax)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.Tax?.ToString("C")</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.Total)
            <span> - </span>
            <strong>&nbsp;@InvoiceSummary.TotalAmount.ToString("C")</strong>
        </DescriptionContent>
        <ExpandChildContent>
            <h5 class="pt-3 border-top">@Localizer.GetString(Translations.ReceiptInformation)</h5>
            <div class="page-summary-info">
                <div class="additional-info-left">
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.FreightExtraDisbursement)
                        </div>
                        <div class="div-right">
                            <InfRadioGroup TValue="FreightDisbursementType"
                                           TItem="KeyValuePair<FreightDisbursementType, string>"
                                           Value="FreightDisbursementType"
                                           ValueChanged="FreightDisbursementTypeChanged"
                                           Data="FreightExtraDisbursementList"
                                           TextField="Value"
                                           ValueField="Key"
                                           Enabled="IsFreightDisbursementEnabled"
                                           IsHorizontal />
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.RunningTotal)
                        </div>
                        <div class="div-right">
                            <span class="currency-value">@RunningTotal.ToString("C")</span>
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        @if (InvoiceSummary.IsMatchToPurchaseOrders)
                        {
                            <div class="div-left">
                                @Localizer.GetString(Translations.Variance)
                            </div>
                            <div class="div-right">
                                <span class="currency-value">@Variance.ToString("C")</span>
                            </div>
                        }
                        else
                        {
                            <div class="div-left">
                                @Localizer.GetString(Translations.AdjustmentRequired)
                                <span role="button" class="info-icon-wrapper" title="@Localizer.GetString(Translations.AdjustmentRequired_Note)"><InfoIcon /></span>
                                <InfTooltip TargetSelector=".info-icon-wrapper" ShowOn="Infinity.Blazor.UIControls.Tooltips.Enums.TooltipShowEvent.Click" />
                            </div>
                            <div class="div-right">

                                <InfButton LabelText="@Localizer.GetString(HasAdjustmentLine ? Translations.Update : Translations.Insert)"
                                           FillMode="FillMode.Outline"
                                           Class="me-5 insert-adjustment-button"
                                           Enabled="(AdjustmentRequired != AdjustmentProductCost)"
                                           ClickCommand="InsertAdjustmentCommand" />
                                <span class="currency-value">@AdjustmentRequired.ToString("C")</span>
                            </div>
                        }
                    </div>
                </div>
                <div class="additional-info-right">
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.Note)
                        </div>
                        <div class="div-right d-block mt-5 note-text">
                            <InfTextArea Width="100%"
                                         PlaceHolder="@Localizer.GetString(Translations.Note_Placeholder)"
                                         Value="@Note"
                                         ValueChanged="NoteChanged" />
                        </div>
                    </div>
                    @if (InvoiceSummary.IsMatchToPurchaseOrders)
                    {
                        <div class="triquestra-field-box" style="margin-top: 60px">
                            <div class="div-left">
                                @Localizer.GetString(Translations.BackOrders)
                            </div>
                            <div class="div-right d-block">
                                <InfCheckbox Value="IsBackOrders" ValueChanged="@((bool value) => OnBackOrdersChangedAsync(value))" />
                            </div>
                        </div>
                    }
                </div>
            </div>
        </ExpandChildContent>
    </ExpandableCardContainer>
</div>