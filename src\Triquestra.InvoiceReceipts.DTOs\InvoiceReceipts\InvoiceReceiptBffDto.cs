﻿namespace Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts
{
    public class InvoiceReceiptBffDto
    {
        public string StockReceiptCode { get; set; } = string.Empty;

        public int? SiteCode { get; set; }

        public string InvoiceCode { get; set; } = string.Empty;

        public string SupplierCode { get; set; } = string.Empty;

        public DateTime InvoiceDate { get; set; }

        public string FreightDisbursmentType { get; set; } = string.Empty;

        public decimal? TotalInvoiceValue { get; set; }

        public decimal? TotalInputTax { get; set; }

        public decimal? TotalFreight { get; set; }

        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? Created { get; set; }

        public string UpdatedBy { get; set; } = string.Empty;

        public DateTime? Updated { get; set; }

        public string InvoiceReceiptStatus { get; set; } = string.Empty;

        public string Note { get; set; } = string.Empty;

        public List<InvoiceReceiptLineBffDto> Lines { get; set; } = new();

        public string SiteName { get; set; } = string.Empty;

        public string SupplierName { get; set; } = string.Empty;

        public bool HasReceiptedPurchaseOrder { get; set; }

        public bool IsMatchedToPurchaseOrders => Lines.TrueForAll(x => x.InvoiceQuantity != 0);
    }
}