﻿@using Telerik.Blazor;
@using Triquesta.InfinityWeb.Common.Base;
@using Infinity.Blazor.UIControls.Windows;
@using Triquestra.InvoiceReceipts.Blazor.Common.Constants

@inject IStringLocalizer<Resources> Localizer

<InfWindow Visible="@Visible" VisibleChanged="VisibleChanged" Class="report-dialog" Title="@Localizer.GetString(Translations.InvoiceReceiptReport)">
    <center style="height: 100%; max-width: 100%">
        @if (!string.IsNullOrEmpty(StockReceiptCode))
        {
            <iframe src="@InvoiceReceiptReportUrl"
                    frameborder="0"
                    scrolling="auto"
                    width="@UIConstants.ReportIframeWidth"
                    style="height: 100%" />
        }
    </center>
</InfWindow>

<style>
/* Cannot use scoped css because the window is created at a dynamic location and can not be wrapped in a html tag. */
.report-dialog {
    position: fixed;
    left: auto;
    transform: none;
    top: 0;
    bottom: 0;
    right: 0;
}

.report-dialog .k-window-title {
    font-size: 18px;
    font-weight: 600;
}
</style>