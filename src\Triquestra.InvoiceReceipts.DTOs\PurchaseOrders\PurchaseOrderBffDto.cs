namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrders
{
    public class PurchaseOrderBffDto
    {
        public DateTime? Created { get; set; }

        public DateTime? Updated { get; set; }

        public bool? Archived { get; set; }

        public string PurchaseOrderCode { get; set; } = string.Empty;

        public short? SiteCode { get; set; }

        public int? TerminalId { get; set; }

        public string SupplierCode { get; set; } = string.Empty;

        public string SupplierName { get; set; } = string.Empty;

        public decimal? NumberOfLines { get; set; }

        public decimal? ExtendedCostPriceExcludingInputTax { get; set; }

        public decimal? TotalInputTax { get; set; }

        public short? DestinationSiteCode { get; set; }

        public string DestinationSiteName { get; set; } = string.Empty;

        public string PurchasingPersonCode { get; set; } = string.Empty;

        public string BillOfLading { get; set; } = string.Empty;

        public DateTime? EstimatedDeliveryDate { get; set; }

        public string AuthorisedByPersonCode { get; set; } = string.Empty;

        public string Note { get; set; } = string.Empty;

        public List<PurchaseOrderLineBffDto> Lines { get; set; } = new();

        public string PurchaseOrderStatus { get; set; } = string.Empty;

        public string SalesOrderCode { get; set; } = string.Empty;

        public string ExternalPurchaseOrderCode { get; set; } = string.Empty;

        public DateTime? UpdatedCreated => Updated ?? Created;
    }
}