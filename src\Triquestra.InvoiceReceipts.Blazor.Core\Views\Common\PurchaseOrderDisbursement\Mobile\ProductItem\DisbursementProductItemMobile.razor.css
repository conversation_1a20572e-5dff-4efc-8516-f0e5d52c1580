﻿.k-listview-header {
    padding: 20px 28px;
    text-transform: uppercase;
}

.k-card {
    box-shadow: none;
    border-radius: 0.8rem;
    margin-bottom: 1rem;
    border-width: 1px;
    padding: 0.55rem;
}

    .k-card .k-card-body,
    .k-card .k-card-actions {
        padding: 0 8px;
    }

    .k-card .k-card-image {
        max-width: unset;
    }

.k-card-horizontal > .k-card-image:last-child {
    border-radius: 0;
}

.k-card .k-card-title {
    padding: 0;
    font-size: 1.285em;
    font-weight: bold;
    line-height: normal;
}

.k-card-subtitle {
    font-size: 14px;
    font-weight: 500;
    line-height: 22.4px;
    color: #323B49;
    margin: 0;
    padding-top: 0.4rem;
}

.k-card .k-card-description {
    font-weight: 400;
    font-size: 10px;
    color: #979797;
    line-height: 16px;
    display: flex;
    margin-top: -0.15rem;
}

.k-count {
    padding-top: 0.7rem;
    color: #323B49;
    font-size: 18px;
    font-weight: 500;
}

.card-date {
    font-size: 0.85em;
    color: #bababa;
}

::deep .k-button-solid-base:focus,
::deep .k-button-solid-base.k-focus {
    box-shadow: none;
}

::deep .k-button-solid-base:hover,
::deep .k-button-solid-base.k-hover {
    border-color: transparent;
    background-color: transparent;
}

::deep .count_remove {
    background: transparent;
    padding: 0;
    margin-top: 0.2rem;
    margin-left: 15rem;
    height: 30px;
    width: 34px;
    border-color: #B00020;
    border-style: solid;
    border-width: 1px;
    max-width: 30px;
    position: absolute;
    right: 1rem;
}

::deep .tb-icon-container .minus {
    position: absolute;
    z-index: 10;
    top: 0.4em;
    border: none;
    background: none;
}

::deep .tb-icon-container .plus {
    position: absolute;
    z-index: 10;
    top: 0.4em;
    right: 0.1em;
    border: none;
    background: none;
    box-shadow: none !important;
}

::deep .k-button-md.k-icon-button > .k-button-icon {
    min-width: 16px;
    min-height: 16px;
}

.k-count-item-card ::deep .k-animation-container {
    width: 100%;
}

/***********************/

.transfer-update-button,
.transfer-update-button ::deep .triquestra-button {
    width: 100%;
}

.warning-message {
    font-size: 13px;
}

::deep .triquestra-input-text-container .triquestra-input-text {
    width: 100%;
}


.card-item-line {
    font-size: 14px;
    font-weight: 500;
    line-height: 22.4px;
    color: #202020;
    margin-top: 15px;
    gap: 10px;
}

.card-item-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    line-height: 22.4px;
    color: #898989;
    white-space: nowrap;
    padding-bottom: 5px;
}

.card-item-value {
    font-size: 14px;
    font-weight: 500;
    text-align: right;
}

.delete ::deep button {
    border-color: #B00020 !important;
}


::deep .text-red .card-item-line {
    color: red !important;
}
