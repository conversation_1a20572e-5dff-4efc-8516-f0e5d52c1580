﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor;
@using Triquesta.InfinityWeb.Common.Base;
@using Infinity.Blazor.UIControls.Windows;
@using Triquestra.InvoiceReceipts.Blazor.Common.Constants

@inherits RazorBaseMvvm<PurchaseOrderDisbursementViewModel>

@inject IStringLocalizer<Resources> Localizer
@if (Visible)
{
    <div class="page-container">
        <div>
            <div class="page-header">
                <div class="header-group">
                    <div class="back-button">
                        <InfButton FillMode="FillMode.Outline" ClickCommand="_backButtonClickCommand">
                            <IconTemplate>
                                <span class="material-icons icon-md">
                                    arrow_back
                                </span>
                            </IconTemplate>
                        </InfButton>
                    </div>
                    <div class="title">
                        @Localizer.GetString(Translations.PurchaseOrderDisbursement)
                    </div>
                </div>
                <div class="buttons">
                    <InfButton LabelText="@Localizer.GetString(Translations.OK)" 
                               IconPosition="IconPosition.Right" 
                               Enabled="!ViewModel.IsLoading"
                               ClickCommand="_okButtonClickCommand">
                        <IconTemplate>
                            <Infinity.Blazor.UIControls.Icons.ArrowForwardIcon Color="@(!ViewModel.IsLoading ? "#fff" : "#898989")" />
                        </IconTemplate>
                    </InfButton>
                </div>
            </div>
        </div>
        <DisbursementProductListMobile PurchaseOrderLines="ViewModel.PurchaseOrderLines" IsLoading="ViewModel.IsLoading" />
    </div>
}
