﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.3" />
		<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.0.3" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
		<PackageReference Include="Triquesta.InfinityWeb.Common" Version="1.2.26" />
	</ItemGroup>
	
	<ItemGroup>
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Common\Triquestra.InvoiceReceipts.Common.csproj" />
	</ItemGroup>
</Project>
