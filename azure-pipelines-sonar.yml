# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
- none

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: UseDotNet@2
  displayName: 'Use .NET Core sdk 8.0.x'
  inputs:
    packageType: 'sdk'
    version: 8.0.x
    includePreviewVersions: true

- task: SonarCloudPrepare@1
  inputs:
    SonarCloud: 'SonarCloud'
    organization: 'triquestra'
    scannerMode: 'MSBuild'
    projectKey: 'triquestra_Triquestra.InvoiceReceipts'
    projectName: 'Triquestra.InvoiceReceipts'
    extraProperties: sonar.exclusions=**/*.js,**/*.css,**/*.scss,**/*.razor,**/*.razor.cs
    cliProjectKey: 

- script: dotnet build
  displayName: 'Build Solution'

- task: SonarCloudAnalyze@1
- task: SonarCloudPublish@1
  inputs:
    pollingTimeoutSec: '300'