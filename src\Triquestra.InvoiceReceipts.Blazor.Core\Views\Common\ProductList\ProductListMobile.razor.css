﻿.k-listview-header {
    padding: 20px 28px;
    text-transform: uppercase;
}

.k-card {
    box-shadow: none;
    border-radius: 0.8rem;
    margin-bottom: 1rem;
    border-width: 1px;
    padding: 0.55rem;
}

    .k-card .k-card-body,
    .k-card .k-card-actions {
        padding: 0 8px;
    }

    .k-card .k-card-image {
        max-width: unset;
    }

.k-card-horizontal > .k-card-image:last-child {
    border-radius: 0;
}

.k-card .k-card-title {
    padding: 0;
    font-size: 1.285em;
    font-weight: bold;
    line-height: normal;
}

.k-card-subtitle {
    font-size: 14px;
    font-weight: 500;
    line-height: 22.4px;
    color: #323B49;
    margin: 0;
    padding-top: 0.4rem;
}

.k-card .k-card-description {
    font-weight: 400;
    font-size: 10px;
    color: #979797;
    line-height: 16px;
    display: flex;
    margin-top: -0.15rem;
}

.k-count {
    padding-top: 0.7rem;
    color: #323B49;
    font-size: 18px;
    font-weight: 500;
}

.card-date {
    font-size: 0.85em;
    color: #bababa;
}

::deep .k-progressbar-horizontal {
    height: 4px;
}

.k-progressbar-label {
    font-weight: 500;
    font-size: 12px;
    align-content: end;
    line-height: 14.52px;
    height: 15px;
    width: 26px;
    padding-top: 0.25rem;
}

::deep .stocktake-chevron-button {
    height: 24px;
    width: 24px;
    margin-top: 11px;
    background: transparent;
    border: none;
}

::deep .k-button-solid-base:focus,
::deep .k-button-solid-base.k-focus {
    box-shadow: none;
}

::deep .k-button-solid-base:hover,
::deep .k-button-solid-base.k-hover {
    border-color: transparent;
    background-color: transparent;
}

::deep .count_remove {
    background: transparent;
    padding: 0;
    margin-top: 0.2rem;
    margin-left: 15rem;
    height: 30px;
    width: 34px;
    border-color: #B00020;
    border-style: solid;
    border-width: 1px;
    max-width: 30px;
    position: absolute;
    right: 1rem;
}

:: deep .tb-icon-container .minus {
    position: absolute;
    z-index: 10;
    top: 0.4em;
    border: none;
    background: none;
}

::deep .tb-icon-container .plus {
    position: absolute;
    z-index: 10;
    top: 0.4em;
    right: 0.1em;
    border: none;
    background: none;
    box-shadow: none !important;
}

::deep .k-button-md.k-icon-button > .k-button-icon {
    min-width: 16px;
    min-height: 16px;
}

.stocktake-action-right {
    position: inherit;
    border: none;
    background-color: transparent;
    z-index: 1004;
}

.stocktake-action-down {
    position: absolute;
    z-index: 1004;
}

.k-count-item-card ::deep .k-animation-container {
    width: 100%;
}

/***********************/
.productlist-container-mobile ::deep .card-component {
    padding: 0px 16px 16px 16px;
}

.productlist-container-mobile ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 10px 20px;
    align-items: center;
    border-radius: 12px;
    flex-direction: column;
}

    .productlist-container-mobile ::deep .k-card-body.no-data-body {
        height: 200px;
        justify-content: center;
    }

    .productlist-container-mobile ::deep .k-card-body .card-item {
        width: 100%;
        margin-bottom: 10px;
    }

.productlist-container-mobile ::deep .k-panelbar {
    display: flex;
    background-color: #FFFFFF;
    margin: 0px 16px 16px 16px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
}

.productlist-container-mobile ::deep .k-panelbar-header {
    width: 100%;
}

.productlist-container-mobile ::deep .card-item-description-col {
    width: 100%;
}

::deep .triquestra-button:hover {
    background-color: #76C3B1 !important;
}

.items-adjustment-title {
    margin: 0px 30px 16px 30px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #202020;
}

.productlist-container-mobile .warning {
    margin: 0px 30px 16px 30px;
    font-weight: 400;
    font-size: 12px;
    line-height: 14.06px;
    color: #000000;
    display: flex;
}

.productlist-container-mobile .warning .warning-message {
    align-self: center;
}

.productlist-container-mobile .warning .warning-icon {
    width: 13.33px;
    height: 13.33px;
}

::deep .productlist-item-blue-color {
    background-color: #E5EFFF !important;
}

.productlist-container-mobile .delete ::deep .triquestra-button {
    width: 16px;
    border-color: #B00020 !important;
}

.productlist-container-mobile .custom-header div:first-child {
    min-width: 50vw;
    width: 50vw;
}

::deep .k-panelbar-toggle.k-i-arrow-chevron-down {
    display: none;
}

.k-dialog-wrapper .k-window-wrapper {
    width: 90% !important;
}

    .k-dialog-wrapper .k-window-wrapper .k-stack-layout {
        width: 100% !important;
    }

::deep .product-item-wrapper {
    overflow: hidden;
}

::deep .triquestra-panelbar .k-state-expanded .k-link {
    padding: 10px 20px 0px 20px !important;
}

.product-list-label {
    font-size: 16px;
    font-weight: 500;
    margin-top: 10px;
    padding-left: 16px;
    padding-bottom: 10px;
}