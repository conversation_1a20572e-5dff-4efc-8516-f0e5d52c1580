﻿using Telerik.DataSource.Extensions;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.InvoiceReceipts.Blazor.BffClient;
using Triquestra.InvoiceReceipts.Blazor.Mappers;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Blazor.Services
{
    public class InvoiceReceiptService : IInvoiceReceiptService
    {
        private readonly IInvoiceReceiptBffClient _bffClient;
        private readonly IInvoiceReceiptMapper _mapper;
        private readonly AuthenticationService _authenticationService;

        public InvoiceReceiptService(IInvoiceReceiptBffClient bffClient, IInvoiceReceiptMapper mapper, AuthenticationService authenticationService)
        {
            _bffClient = bffClient;
            _mapper = mapper;
            _authenticationService = authenticationService;
        }

        public async Task<SystemConfigurations> GetSystemConfigurationsAsync(int? siteCode)
        {
            var bffDto = await _bffClient.GetSystemConfigurationsAsync(siteCode);

            return _mapper.MapToSystemConfigurations(bffDto);
        }

        public async Task<Supplier> GetSupplierAsync(string supplierCode)
        {
            var bffDto = await _bffClient.GetSupplierAsync(supplierCode);

            return _mapper.MapToSupplier(bffDto);
        }

        public async Task<List<Supplier>> GetActiveSuppliersAsync()
        {
            var bffDtos = await _bffClient.GetActiveSuppliersAsync();

            return bffDtos.Select(_mapper.MapToSupplier).OrderBy(x => x.SupplierName).ToList();
        }

        public async Task<List<Site>> GetSitesAsync()
        {
            var bffSites = await _bffClient.GetSitesAsync();
            var sites = bffSites.Select(_mapper.MapToSite).OrderBy(site => site.Name).ToList();

            if (_authenticationService.IsGlobalUser)
            {
                return sites;
            }

            return sites.Where(x => _authenticationService.CurrentUserAllowedSites.Contains(x.SiteCode.GetValueOrDefault())).ToList();
        }

        public async Task<decimal> GetTaxRateAsync()
        {
            const int TaxCode = 1;
            var taxes = await _bffClient.GetTaxesAsync();
            var tax = taxes.Find(x => x.TaxCode == TaxCode);
            return tax?.TaxRate ?? 0;
        }

        public async Task<Product?> ScanProductAsync(string barcode, int? siteCode)
        {
            var bffDto = await _bffClient.ScanProductAsync(barcode, siteCode);
            return string.IsNullOrWhiteSpace(bffDto?.ProductCode) ? null : _mapper.MapToProduct(bffDto);
        }

        public async Task<List<Product>> GetProductsAsync(List<string> productCodes, int? siteCode)
        {
            var bffDtos = await _bffClient.GetProductsAsync(productCodes, siteCode);

            if (bffDtos != null)
            {
                return bffDtos.Where(x => x.Archived != true).Select(_mapper.MapToProduct).ToList();
            }

            return new();
        }

        public async Task<List<ProductInventoryDetail>> GetProductInventoriesAsync(IEnumerable<string> productCodes, int? siteCode)
        {
            var bffDtos = await _bffClient.GetProductInventoriesAsync(productCodes, siteCode);
            if (bffDtos.Count > 0)
            {
                return bffDtos.Select(_mapper.MapToProductInventoryDetail).ToList();
            }

            return new();
        }

        public async Task<string> CreateInvoiceReceiptAsync(InvoiceReceiptCreateBffDto requestBffDto)
        {
            var bffDto = await _bffClient.CreateInvoiceReceiptAsync(requestBffDto);
            return bffDto?.StockReceiptCode ?? string.Empty;
        }

        public async Task<List<InvoiceReceipt>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto requestBffDto)
        {
            var bffDtos = await _bffClient.SearchInvoiceReceiptsAsync(requestBffDto);
            return bffDtos.Select(_mapper.MapToInvoiceReceipt).ToList();
        }

        public async Task<InvoiceReceipt> GetInvoiceReceiptAsync(string stockReceiptCode)
        {
            var bffDto = await _bffClient.GetInvoiceReceiptsAsync(stockReceiptCode);
            return _mapper.MapToInvoiceReceipt(bffDto);
        }

        public async Task<string> UpdateInvoiceReceiptAsync(InvoiceReceiptUpdateBffDto requestBffDto)
        {
            var bffDto = await _bffClient.UpdateInvoiceReceiptAsync(requestBffDto);
            return bffDto?.StockReceiptCode ?? string.Empty;
        }

        public async Task<string> UpdateInvoiceReceiptStatusAsync(InvoiceReceiptStatusUpdateBffDto requestBffDto)
        {
            var bffDto = await _bffClient.UpdateInvoiceReceiptStatusAsync(requestBffDto);
            return bffDto?.StockReceiptCode ?? string.Empty;
        }

        public async Task<List<PurchaseOrder>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            var bffDtos = await _bffClient.SearchPurchaseOrdersForDisbursementAsync(requestBffDto);
            
            return bffDtos.Select(_mapper.MapToPurchaseOrder).ToList();
        }

        public async Task<List<PurchaseOrderReceipt>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto requestBffDto)
        {
            var bffDtos = await _bffClient.SearchCompletedPurchaseOrderReceiptsAsync(requestBffDto);

            return bffDtos.Select(_mapper.MapToPurchaseOrderReceipt).ToList();
        }

        public async Task<List<PurchaseOrder>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            var bffDtos = await _bffClient.SearchOpenPurchaseOrdersAsync(requestBffDto);

            return bffDtos.Select(_mapper.MapToPurchaseOrder).ToList();
        }
    }
}