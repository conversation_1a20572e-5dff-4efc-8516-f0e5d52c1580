﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageSources>
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
		<add key="Telerik_NuGet" value="https://nuget.telerik.com/v3/index.json" />
		<add key="Triquestra_NuGet" value="https://tqaznuget.triquestra.com/nuget/tqnuget/v3/index.json" />
		<add key="Triquestra_UINuGet" value="https://tqaznuget.triquestra.com/nuget/tqnuget-azure/v3/index.json" />
	</packageSources>
	<!--TODO: move to env variables-->
	<packageSourceCredentials>
		<Telerik_NuGet>
			<add key="Username" value="<EMAIL>" />
			<add key="ClearTextPassword" value="fMnPKKBa2pHtEvqodU5VE13Qq1SSmZW4asgNG" />
		</Telerik_NuGet>
		<Triquestra_NuGet>
			<add key="Username" value="codehq" />
			<add key="ClearTextPassword" value="3xeZCq9%P*rH@t2e" />
		</Triquestra_NuGet>
		<Triquestra_UINuGet>
			<add key="Username" value="tqnuget-azure" />
			<add key="ClearTextPassword" value="1AvdqLlje644YB" />
		</Triquestra_UINuGet>
	</packageSourceCredentials>
</configuration>