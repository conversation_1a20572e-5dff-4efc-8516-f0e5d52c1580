﻿.create-page-container {
    margin-top: 80px;
}

.criteria-line-separator {
    margin: 50px 0;
    border: 1px solid #ccc;
}

.create-page-container ::deep .expand-cardcontainer .card-component {
    padding: 0px 32px 16px 32px;
    gap: 38px;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    max-width: 1000px;
}

.create-page-header {
    background-color: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 72px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
}

    .create-page-header .title {
        align-self: center;
        font-weight: 400;
        font-size: 24px;
        line-height: 28.13px;
        color: #202020;
    }

    .create-page-header .header-group {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
    }

    .create-page-header ::deep .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 10px;
        height: 40px;
        margin-right: 1.5rem;
        align-self: center;
    }

    .create-page-header .buttons ::deep button {
        margin-left: 10px;
    }
/*****/
.create-page-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding-top: 24px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
    margin-top: 15px;
}

/*******/

.create-page-container ::deep .custom-tabs .k-splitter,
.create-page-container ::deep .k-card, .k-panelbar.customized-panelbar {
    border: none;
    margin-bottom: 3px;
}

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

.total-line {
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    color: #202020;
    font-weight: 500;
}

.match-po-line ::deep .triquestra-checkbox-label {
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #202020;
}

.match-po-line .checkbox-subtitle {
    font-size: 12px;
    margin-left: 37px;
}

.match-po-line ::deep .triquestra-checkbox-label {
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    color: #202020;
}


@media only screen and (max-width: 640px) {
    .create-page-header .title {
        font-weight: 700;
        font-size: 18px;
    }

    .total-line {
        font-size: 16px;
        font-weight: 400;
    }
}
