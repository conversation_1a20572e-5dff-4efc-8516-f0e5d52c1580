﻿.receipts-details-grid {
    margin: auto;
}

.receipts-details-grid ::deep .card-component {
    padding: 0px 32px 32px 32px;
}

.receipts-details-grid ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 48px 24px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

.grid-empty-content-wrapper {
    display: flex;
    width: 100%;
    height: 300px;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    border-top: 1px solid #eee;
    background-color: #fff;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.receipts-details-grid ::deep .triquestra-button{
    border: unset !important;
    background: transparent !important;
}

.receipts-details-grid ::deep .k-button-outline {
    border: unset !important;
    background: transparent !important;
}

.receipt-details-daterange ::deep .k-card-body {
    padding: 48px 24px;
    justify-content: start !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    margin-bottom: 10px;
    border-radius: 12px;
}