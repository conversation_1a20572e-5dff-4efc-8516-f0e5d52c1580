﻿label {
    font-weight: 500;
    font-size: 14px;
    padding: 5px 0;
}

.productlist-container {
    padding: 10px 10px 30px 10px;
}

.productlist-container ::deep .card-component {
    padding: 0px;
}

.productlist-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 48px 24px;
    justify-content: space-between;
    align-items: center;
    border-radius: 0;
    overflow: auto;
}

.product_description {
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    color: #202020;
}

.product_code {
    font-weight: 400;
    font-size: 12px;
    line-height: 14.06px;
    color: #898989;
}

::deep .k-table-row.text-red td,
::deep .k-table-row.text-red .product_description,
::deep .k-table-row.text-red .product_code {
    color: red;
}

.productlist-container-actions {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.productlist-container ::deep .k-grid-content .k-grid-table td {
    vertical-align: top;
    padding-top: 1.2rem;
}

.productlist-container ::deep .k-grid-content .numeric-container {
    margin-top: -0.8rem;
}