@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Grids
@using Infinity.Blazor.UIControls.Grids.Mapping
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor
@using Telerik.Blazor.Components
@using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate

<div class="productlist-container">
    <CardContainer>
        <CardBody Class="p-0 align-items-start">
            @if (IsLoading)
            {
                <div class="w-100 text-center p-5">
                    <Infinity.Blazor.UIControls.Loaders.InfLoadingBox Height="200px" />
                </div>
            }
            else if (PurchaseOrders.Count == 0)
            {
                <div class="w-100 text-center p-5 mt-5">
                    <div>
                        <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                    </div>
                    <div class="text-muted">@Localizer.GetString(Translations.NoPurchaseOrdersFound)</div>
                </div>
            }
            else
            {
                <InfGridView @ref="_gridRef"
                             Data="@PurchaseOrders"
                             TItem="PurchaseOrder"
                             Class="custom-grid" 
                             Reorderable="true"
                             Sortable="true" 
                             RowHeight="50" 
                             Height="350px"
                             EnableLoaderContainer="true" 
                             FilterMode="@FilterMode.FilterMenu"
                             SelectionParameters="_gridSelectionParameters">
                    <ColumnsTemplate>
                        <GridColumn Field="@nameof(PurchaseOrder.PurchaseOrderCode)" Visible="false" />
                        <GridCheckboxColumn Width="30px" SelectAll="false" SelectAllMode="GridSelectAllMode.Current" CheckBoxOnlySelection="true" />
                        <GridColumn Field="@nameof(PurchaseOrder.PurchaseOrderCode)" Title="@Localizer.GetString(Translations.PONumber)" />
                        <GridColumn Width="50%" Field="@nameof(PurchaseOrder.UpdatedOrCreated)" Title="@Localizer.GetString(Translations.Logged)" Filterable="false">
                            <Template>
                                @{
                                    var item = context as PurchaseOrder;
                                    <span>@(string.Format(LoggedDateTimeFormat, item.UpdatedOrCreated).ToUpper())</span>
                                }
                            </Template>
                        </GridColumn>
                    </ColumnsTemplate>
                </InfGridView>
            }
        </CardBody>
    </CardContainer>
</div>