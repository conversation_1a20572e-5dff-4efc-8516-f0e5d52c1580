﻿using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Mobile
{
    public partial class SelectPurchaseOrderListMobile
    {
        private IRelayCommand<PurchaseOrder> _itemCheckedChangedCommand;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public ObservableCollection<PurchaseOrder> PurchaseOrders { get; set; } = new();

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        public ICommand? SelectedItemsChangedCommand { get; set; }

        public ObservableCollection<PurchaseOrder> SelectedItems = new();

        public SelectPurchaseOrderListMobile()
        {
            _itemCheckedChangedCommand = new RelayCommand<PurchaseOrder>(ItemCheckedChangedHandler);
        }

        private void ItemCheckedChangedHandler(PurchaseOrder? purchaseOrder)
        {
            var existingItem = SelectedItems.FirstOrDefault(x => x.PurchaseOrderCode == purchaseOrder!.PurchaseOrderCode);
            
            if (existingItem != null)
            {
                SelectedItems.Remove(existingItem);
            }
            else
            {
                SelectedItems.Add(purchaseOrder!);
            }

            if (SelectedItemsChangedCommand?.CanExecute(null) == true)
            {
                SelectedItemsChangedCommand.Execute(null);
            }
        }
    }
}