﻿using Infinity.Blazor.UIControls.Grids;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using System.Globalization;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Desktop
{
    public partial class DisbursementProductListDesktop
    {
        private static readonly string GridDateFormat = "{0:" + CultureInfo.DefaultThreadCurrentCulture!.DateTimeFormat.ShortDatePattern + "}";

        private InfGridView<PurchaseOrderLine>? _gridRef;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public ObservableCollection<PurchaseOrderLine> ProductLines { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }
    }
}