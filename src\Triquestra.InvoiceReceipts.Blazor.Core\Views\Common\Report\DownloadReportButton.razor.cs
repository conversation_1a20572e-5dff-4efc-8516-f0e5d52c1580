﻿using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.Report
{
    public partial class DownloadReportButton
    {
        [Inject]
        public IConfiguration Configuration { get; set; }

        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; }

        private IRelayCommand<object?> _downloadReportCommand;

        public DownloadReportButton()
        {
            _downloadReportCommand = new AsyncRelayCommand<object?>(DownloadReportHandler);
        }

        private async Task DownloadReportHandler(object? _)
        {
            var downloadUrl = Configuration.GetValue<string>("InvoiceReceiptReportUrl") + StockReceiptCode + "/pdf";
            await JSRuntime.InvokeVoidAsync(UIConstants.TriggerFileDownloadFunctionName, downloadUrl);
        }
    }
}