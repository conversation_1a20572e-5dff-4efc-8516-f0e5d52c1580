﻿using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;

namespace Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate
{
    public class InvoiceSummaryModel
    {
        public Site? SelectedSite { get; set; }

        public Supplier? SelectedSupplier { get; set; }

        public string InvoiceNumber { get; set; } = string.Empty;

        public DateTime InvoiceDate { get; set; }

        public decimal? FreightExtra { get; set; }

        public decimal? Tax { get; set; }

        public decimal TotalAmount { get; set; }

        public bool IsMatchToPurchaseOrders { get; set; }

        public List<PurchaseOrder> PurchaseOrders { get; set; } = new();

        public bool HasReceiptedPurchaseOrder { get; set; }
    }
}