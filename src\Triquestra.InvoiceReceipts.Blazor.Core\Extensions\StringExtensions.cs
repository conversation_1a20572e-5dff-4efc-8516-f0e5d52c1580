﻿namespace Infinity.InvoiceReceipts.Blazor.Extensions
{
    public static class StringExtensions
    {
        public static string AddEllipsis(this string value, int maxChars)
        {
            if (string.IsNullOrEmpty(value))
            {
                return value;
            }    

            return value.Length <= maxChars ? value : string.Concat(value.AsSpan(0, maxChars), "...");
        }
    }
}