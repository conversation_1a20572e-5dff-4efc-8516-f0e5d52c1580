﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate
{
    public class PurchaseOrderReceipt : IAggregateRoot
    {
        public int? SiteCode { get; set; }

        public string PurchaseOrderCode { get; set; } = string.Empty;

        public string PurchaseOrderReceiptStatus { get; set; } = string.Empty;

        public string ExternalReference { get; set; } = string.Empty;

        public string SupplierReference { get; set; } = string.Empty;

        public DateTime? ReceiptDate { get; set; }

        public string StockReceiptCode { get; set; } = string.Empty;

        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? Created { get; set; }

        public string UpdatedBy { get; set; } = string.Empty;

        public DateTime? Updated { get; set; }

        public int NumberOfLines { get; set; }

        public List<PurchaseOrderReceiptLine> Lines { get; set; } = new();

        public DateTime UpdatedOrCreated => Updated ?? Created!.Value;
    }
}