﻿@page "/app/inventory/receipt-invoice/{stockReceiptCode}/complete"

@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt
@using Microsoft.AspNetCore.Authorization
@using Triquestra.InvoiceReceipts.Common.Constants

@attribute [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]

<TriquestraMediaQuery Media="@UIControlConstants.MobileScreenMediaQuery" OnChange="((changed) => _isMobile = changed)" />

@if (_isMobile == true)
{
    <CompletedInvoiceReceiptMobile StockReceiptCode="@StockReceiptCode" />
}
else if (_isMobile == false)
{
    <CompletedInvoiceReceiptDesktop StockReceiptCode="@StockReceiptCode" />
}

<style>
    /*** hide the left menu and top bar, require the css code here ****/
    .k-drawer {
        display: none;
    }

    .card-header {
        display: none;
    }
</style>