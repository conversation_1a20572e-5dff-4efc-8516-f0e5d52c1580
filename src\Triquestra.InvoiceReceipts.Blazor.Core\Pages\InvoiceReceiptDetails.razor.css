﻿.overview-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 32px;
    justify-content: space-around;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
}

.card-component .overview-container {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.progress_align_center {
    display: flex;
    justify-content: center;
}

@media (max-width: 640px) {
    .overview-container ::deep .k-card-body {
        padding: 24px;
    }
}