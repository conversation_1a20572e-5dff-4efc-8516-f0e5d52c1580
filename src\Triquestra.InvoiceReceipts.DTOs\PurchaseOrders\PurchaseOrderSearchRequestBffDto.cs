﻿namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrders
{
    public class PurchaseOrderSearchRequestBffDto
    {
        public List<int> SiteCodes { get; set; } = new();

        public List<int> DestinationSiteCodes { get; set; } = new();

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public string PurchaseOrderStatus { get; set; } = string.Empty;

        public string PurchaseOrderReference { get; set; } = string.Empty;

        public List<string> ProductCodes { get; set; } = new();

        public List<string> SupplierCodes { get; set; } = new();

        public string ResponseType { get; set; } = string.Empty;

        public int? PageSize { get; set; }

        public int? Offset { get; set; }
    }
}