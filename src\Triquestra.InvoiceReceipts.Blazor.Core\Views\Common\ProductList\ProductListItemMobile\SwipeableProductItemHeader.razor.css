﻿.product-description {
    flex: 0 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    min-width: 110px;
    width: calc(100vw - 175px);
    margin-left: 10px;
    font-size: 0.9rem;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #202020;
}

.product-code {
    color: #202020;
}

.receipt-quantity-label {
    font-size: 14px;
    white-space: nowrap;
    color: #898989;
}

.receipt-quantity {
    text-align: right;
}

@media only screen and (max-width: 380px) {
    .product-description {
        min-width: 90px;
    }
}

.delete-button {
    min-width: 0px;
    max-width: 0px;
    right: -100px;
    position: relative;
    transition: all 1s ease;
    justify-content: center;
    margin-left: 12px;
    margin-top: -12px;
    background: #B00020;
    height: 75px;
    top: 8px;
    display: flex;
    align-items: center;
}

    .delete-button.move {
        min-width: 95px;
        max-width: 95px;
        right: 0px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        transition: all 0.1s ease;
        transform: translate(-5%, 0);
    }

    .delete-button ::deep .k-button.triquestra-button.k-button-outline {
        background: transparent;
        border: 1px solid #fff;
        width: 40px;
        height: 40px;
        padding: 10px;
    }

.text-red, ::deep .text-red {
    color: red !important;
}