﻿namespace Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts
{
    public abstract class InvoiceReceiptBaseBffDto
    {
        public int? SiteCode { get; set; }

        public string InvoiceCode { get; set; } = string.Empty;
        
        public string SupplierCode { get; set; } = string.Empty;

        public DateTime InvoiceDate { get; set; }

        public string FreightDisbursmentType { get; set; } = string.Empty;

        public decimal TotalInvoiceValue { get; set; }

        public decimal TotalInputTax { get; set; }

        public decimal TotalFreight { get; set; }

        public string InvoiceReceiptStatus { get; set; } = string.Empty;

        public string Note { get; set; } = string.Empty;

        public List<InvoiceReceiptLineBffDto> Lines { get; set; } = new();
    }
}
