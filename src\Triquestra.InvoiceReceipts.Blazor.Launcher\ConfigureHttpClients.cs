﻿using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Triquesta.InfinityWeb.Common.Authorization;
using Triquestra.Components.Blazor.BffClient.Constants;

namespace Triquestra.InvoiceReceipts.Blazor.Launcher
{
    public static class ConfigureHttpClients
    {
        public static void AddBlazorComponentsHttpClient(this WebAssemblyHostBuilder builder)
        {
            builder.Services.AddHttpClient(ClientSettingConstants.BlazorComponentsBffApiHttpClientName, httpClient =>
            {
                httpClient.BaseAddress = new Uri(builder.Configuration["BlazorComponentsBffBaseUrl"]!);
            })
                .AddHttpMessageHandler<AntiforgeryHandler>();
            builder.Services.AddTransient(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient(ClientSettingConstants.BlazorComponentsBffApiHttpClientName));
        }
    }
}
