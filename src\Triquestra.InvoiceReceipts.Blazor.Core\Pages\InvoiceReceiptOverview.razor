﻿@page "/app/inventory/receipt-invoice"

@using Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Desktop
@using Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Mobile
@using Microsoft.AspNetCore.Authorization
@using Triquestra.InvoiceReceipts.Common.Constants

@attribute [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]

<TriquestraMediaQuery Media="@UIControlConstants.MobileScreenMediaQuery" OnChange="((changed) => _isMobile = changed)" />
<CardContainer>
    <div class="overview-container">
        @if(_isMobile.HasValue)
        {
            if (_isMobile == true)
            {
                <InvoiceReceiptOverviewMobile />
            }
            else
            {
                <InvoiceReceiptOverviewDesktop />
            }
        }
    </div>
</CardContainer>