﻿@using Infinity.Blazor.UIControls.Layouts
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
@using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile.ProductItem
@using Telerik.Blazor.Components

<div class="list-item">
    <div @key="@PurchaseOrder.PurchaseOrderCode" class="border-bottom p-4 d-flex flex-row">
        <div class="d-flex align-items-center">
            <Infinity.Blazor.UIControls.Selectors.InfCheckbox Value="_isChecked" ValueChanged="IsCheckedChanged" />
        </div>
        <div class="w-100">
            <div class="d-flex justify-content-between">
                <span class="fw-500 text-muted">@Localizer.GetString(Translations.PONumber)</span>
                <span class="triquestra-text-dark">@PurchaseOrder.PurchaseOrderCode</span>
            </div>
            <div class="d-flex justify-content-between mt-2">
                <span class="fw-500 text-muted">@Localizer.GetString(Translations.Logged)</span>
                <span class="triquestra-text-dark">@PurchaseOrder.UpdatedOrCreated?.ToString("dd/MM/yyyy hh:mm:ss tt").ToUpperInvariant()</span>
            </div>
        </div>
    </div>
</div>