﻿@using Infinity.Blazor.Components.Navigation.View
@using Infinity.Blazor.Components.UserAccountDetails.Views
@using Infinity.Blazor.UIControls.Buttons
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Views

<div class="card-header main-topbar">
    <div class="nav-container">
        <div class="menu-container">
            <InfButton ClickCommand="_toggleDrawerCommand" ImageUrl="./app-img/menu.png" Class="menu-button bg-transparent border-white"></InfButton>
            <h2 class="topbar-title">@_pageTitle</h2>
            <div class="user-icon" @onclick="@ToggleUserAccount" title="@UserName">
                <div title="@UserName" class="telerik-blazor k-avatar k-avatar-md k-rounded-full k-avatar-solid k-avatar-solid-primary">
                    <span class="k-avatar-text">
                        @UserName.Substring(0,1).ToUpper()
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
@if (IsUserAccountOpen)
{
    <UserAccountDetailsMobile />
}
<TelerikDrawer @ref="@Drawer" Class="@DrawerClass" Data="MenuItems" Expanded="IsDrawerOpen" ExpandedChanged="ExpandedChangedHandler" Mode="@DrawerMode.Overlay">
    <Template Context="drawerContext">
        <MobileNavMenuList DrawerItems="MenuItems" Drawer="Drawer" MenuItemSelectCommand="_menuItemSelectCommand" />
    </Template>
    <DrawerContent>
        @if (AuthenticationService.PermissionLoadCompleted)
        {
            @ChildContent
        }
        else
        {
            <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
        }
    </DrawerContent>
</TelerikDrawer>

<style>
    .card-header {
        padding-left: 0.5rem;
    }

    .icon {
        width: 24px;
        color: #FFFFFF;
        align-self: center;
    }

    .large-image {
        width: 129px;
    }

    .small-image {
        width: 63px;
    }

    /****top bar menu*****/

    .k-drawer-container {
        top: -5rem;
        position: relative;
        width: 100%;
        min-height: 300px;
        background-color: #F5F5F5;
    }

        .k-drawer-container.k-drawer-open {
            top: 0;
            bottom: 0;
            position: fixed;
            min-height: 100px;
        }

    .k-drawer.k-drawer-start,
    .k-drawer-left .k-drawer {
        top: 0;
        left: 0;
        display: none;
    }

    .k-drawer-open .k-drawer {
        display: flex;
    }

    .k-drawer-close {
        top: 0;
    }

        .k-drawer-close .k-drawer-content {
            top: -2rem !important;
        }

    .k-drawer-expanded {
        display: flex !important;
        z-index: 1;
    }

    .drawer-sm.k-drawer-expanded .k-drawer.telerik-blazor {
        width: 100%;
    }

    .drawer-sm.k-drawer-expanded .k-drawer {
        max-width: 100vh;
    }

    .drawer-sm.k-drawer-expanded .k-overlay {
        display: none;
    }

    .k-drawer-container .k-drawer-content {
        position: relative;
        top: 2.42rem;
        padding-left: 0;
        padding-right: 0;
    }

    .k-drawer-container.k-drawer-overlay .k-drawer {
        position: absolute;
    }

    .k-drawer {
        background: #445D63;
        font-size: 14px;
        font-weight: 500;
    }


    .k-drawer-content {
        padding: 25px;
    }

        .k-drawer-content a {
            color: #3D57D8;
            text-decoration: inherit;
        }

            .k-drawer-content a:hover {
                text-decoration: underline;
            }

        .k-drawer-content h2 {
            font-weight: 300;
        }

    .topbar-title {
        font-size: 20px;
        font-weight: 500;
        line-height: 23.44px;
        overflow: hidden;
        color: #202020;
        margin: 0;
    }

    .separator {
        box-sizing: border-box;
        width: 1px;
        height: 20px;
        background: #E5E5E5;
        border-radius: 0px;
    }

    .menu-container {
        height: 55px;
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        left: 0px;
        top: 0px;
    }

        .menu-container figure {
            width: 19px;
            height: 19px;
            margin: 0 5px 0 0;
        }

        .menu-container .k-menu {
            height: 100%;
        }

    .k-overlay {
        position: absolute;
    }
</style>