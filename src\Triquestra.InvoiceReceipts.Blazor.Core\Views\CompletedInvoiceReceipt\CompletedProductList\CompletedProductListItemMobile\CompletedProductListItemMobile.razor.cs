using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList.CompletedProductListItemMobile
{
    public partial class CompletedProductListItemMobile
    {
        public IRelayCommand<PanelBarItemClickEventArgs> _panelBarClickCommand;

        public bool _isExpanded;

        [Parameter]
        public bool IsExpanded
        {
            get { return _isExpanded; }
            set
            {
                _isExpanded = value;
                if (_isExpanded)
                {
                    ExpandedItems = new List<string> { ProductLine.ProductCode };
                }
                else
                {
                    ExpandedItems = new List<string>();
                }
            }
        }

        [Parameter]
        public ProductLineViewModel ProductLine { get; set; }

        [Parameter]
        public IRelayCommand<string> SelectProductCommand { get; set; }

        [Parameter]
        public IEnumerable<object> ExpandedItems { get; set; } = new List<string>();

        [Parameter]
        public bool IsMatchedToPurchaseOrder { get; set; }
        
        [Parameter]
        public string ElementId { get; set; }

        public CompletedProductListItemMobile()
        {
            _panelBarClickCommand = new RelayCommand<PanelBarItemClickEventArgs>(PanelBarClickHandler);
        }

        private void PanelBarClickHandler(PanelBarItemClickEventArgs? args)
        {
            var productCode = args!.Item.ToString();

            if (SelectProductCommand?.CanExecute(productCode) == true)
            {
                SelectProductCommand.Execute(productCode);
            }
        }
    }
}