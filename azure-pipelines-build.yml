# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
- release-*

pool:
  vmImage: ubuntu-latest

variables:
  - name: 'major'
    value: $(MAJORVERSION)
  - name: 'minor'
    value: 2
  - name: 'patch'
    value: $[counter(variables['minor'], 0)] #this will get reset when minor gets bumped. The number after Counter is the seed number (in my case, I started at 1).
  - name: 'packageVersion'
    value: '$(major).$(minor).$(patch)'
  - name: 'dockerImageTagBuildId'
    value: '$(Build.SourceBranchName)-$(Build.BuildId)'
  - name: 'dockerImageTagLatest'
    value: '$(Build.SourceBranchName)-latest'
  - group: Development
  
steps:

- task: DownloadSecureFile@1
  displayName: 'Download LICENSE.txt'
  inputs:
    secureFile: 'LICENSE.txt'

- task: CopyFiles@2
  displayName: "Copy LICENSE.txt"
  inputs:
    sourceFolder: "$(Agent.TempDirectory)"
    contents: "LICENSE.txt"
    targetFolder: "$(Build.SourcesDirectory)/src/Triquestra.InvoiceReceipts.Blazor.Core/"

- task: CmdLine@2
  displayName: add LICENSE.txt to csproj file
  inputs:
    script: 'sudo sed -i ''s|</Project>|<PropertyGroup><PackageLicenseFile>LICENSE.txt</PackageLicenseFile></PropertyGroup><ItemGroup><None Include="LICENSE.txt" Pack="true" PackagePath=""/></ItemGroup></Project>|g'' src/Triquestra.InvoiceReceipts.Blazor.Core/Triquestra.InvoiceReceipts.Blazor.Core.csproj'

- task: UseDotNet@2
  displayName: 'Use .NET Core sdk 8.0.x'
  inputs:
    packageType: 'sdk'
    version: 8.0.x
    includePreviewVersions: false

- task: Docker@2
  displayName: 'Build bff docker image'
  inputs:
    containerRegistry: 'Azure Container Registry'
    repository: 'invoicereceipts-bff'
    command: 'build'
    Dockerfile: './InvoiceReceipts.Bff.dockerfile'
    buildContext: '**'
    arguments: '--build-arg PROGET_API_KEY=$(ProGet_APIKey) --build-arg PROGET_SOURCE=$(ProGet_Source) --build-arg PACKAGE_VERSION=$(packageVersion)'
    tags: |
      $(dockerImageTagBuildId)
      $(dockerImageTagLatest)
      
- task: Docker@2
  displayName: 'Push bff docker image'
  inputs:
    containerRegistry: 'Azure Container Registry'
    repository: 'invoicereceipts-bff'
    command: 'push'
    tags: |
      $(dockerImageTagBuildId)
      $(dockerImageTagLatest)

- task: CmdLine@2
  displayName: replace containers tag for kubernetes
  inputs:
    script: 'sudo sed -i ''s/__image-tag__/$(dockerImageTagBuildId)/g'' manifests/deployment*.yml'

- task: PublishBuildArtifacts@1
  displayName: publish build artifact
  inputs:
    PathtoPublish: '$(System.DefaultWorkingDirectory)/manifests'
    ArtifactName: 'manifests'
    publishLocation: 'Container'

- script: dotnet tool install pgutil --create-manifest-if-needed
  displayName: Install pgutil

- script: dotnet tool run pgutil builds scan --input='src/Triquestra.InvoiceReceipts.Blazor.Core/Triquestra.InvoiceReceipts.Blazor.Core.csproj' --project-name="InfinityPLUS_InvoiceReceipts" --version=$(packageVersion) --project-type=application --api-key=$(ProGet_APIKey) --source=$(ProGet_Source)
  displayName: Create & Upload ProGet Report

- task: DotNetCoreCLI@2
  displayName: 'Pack Library'
  inputs:
    command: 'pack'
    packagesToPack: '**/src/Triquestra.InvoiceReceipts.Blazor.Core/Triquestra.InvoiceReceipts.Blazor.Core.csproj'
    configuration: 'Release'
    versioningScheme: 'byEnvVar'
    versionEnvVar: 'packageVersion'

- task: DotNetCoreCLI@2
  displayName: 'Push NuGet package to ProGet'
  inputs:
    command: 'custom'
    custom: 'nuget'
    arguments: >
      push "$(Build.ArtifactStagingDirectory)/**/*.nupkg"
      --source $(ProGet_Source_Nuget)
      --api-key $(ProGet_APIKey_Nuget)
      --skip-duplicate
    
- task: WhiteSource@21
  inputs:
    cwd: '$(System.DefaultWorkingDirectory)'
