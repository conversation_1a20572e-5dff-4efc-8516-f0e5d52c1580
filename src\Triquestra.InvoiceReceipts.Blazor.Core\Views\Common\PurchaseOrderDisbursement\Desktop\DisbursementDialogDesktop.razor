﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor;
@using Triquesta.InfinityWeb.Common.Base;
@using Infinity.Blazor.UIControls.Windows;
@using Triquestra.InvoiceReceipts.Blazor.Common.Constants

@inherits RazorBaseMvvm<PurchaseOrderDisbursementViewModel>

@inject IStringLocalizer<Resources> Localizer

<InfWindow Visible="@Visible" VisibleChanged="VisibleChanged" Class="disbursement-dialog" HasCloseButton="false" IsModal>
    <TitleContent>
        <div class="w-100 d-flex align-items-center justify-content-between">
            <div>
                <span role="button">
                    <Infinity.Blazor.UIControls.Icons.ArrowBackIcon OnClick="CloseDialogAsync" />
                </span>
                <span Class="ms-3">@Localizer.GetString(Translations.PurchaseOrderDisbursement)</span>
            </div>
            <InfButton LabelText="@Localizer.GetString(Translations.OK)" 
                       IconPosition="IconPosition.Right" 
                       Enabled="!ViewModel.IsLoading"
                       ClickCommand="_okButtonClickCommand">
                <IconTemplate>
                    <Infinity.Blazor.UIControls.Icons.ArrowForwardIcon Color="@(!ViewModel.IsLoading ? "#fff" : "#898989")" />
                </IconTemplate>
            </InfButton>
        </div>
    </TitleContent>
    <ChildContent>
        <DisbursementProductListDesktop ProductLines="ViewModel.PurchaseOrderLines" IsLoading="ViewModel.IsLoading" />
    </ChildContent>
</InfWindow>

<style>
    /* Cannot use scoped css because the window is created at a dynamic location and can not be wrapped in a html tag. */

    .disbursement-dialog {
        width: 80%;
        height: 550px;
        top: 100px !important;
        left: calc((100vw - 80%) / 2) !important;
        transform: none !important;
    }

    .disbursement-dialog .k-window-content {
        overflow: hidden;
        position: static;
    }

        .disbursement-dialog .k-window-title {
            font-size: 18px;
            font-weight: 500;
        }

        .disbursement-dialog .productlist-container .k-animation-container {
            margin-left: calc((100vw - 80%) / -2 + 9.6%);
            margin-top: -100px;
        }

        /* filter dropdownlist */
        .disbursement-dialog .productlist-container .k-animation-container[aria-label="Options list"] {
            margin-left: -13.2%;
        }
</style>