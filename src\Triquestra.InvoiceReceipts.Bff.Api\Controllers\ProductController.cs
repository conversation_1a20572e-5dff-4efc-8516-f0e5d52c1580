﻿using Duende.Bff;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InvoiceReceipts.Bff.Api.Configuration;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Common.Constants;

namespace Triquestra.InvoiceReceipts.Bff.Api.Controllers
{
    [ApiController]
    [DynamicRoute("[controller]")]
    [BffApi]
    [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]
    public class ProductController : ControllerBase
    {
        private readonly ILogger<ProductController> _logger;
        private readonly IInvoiceReceiptBffService _invoiceReceiptBffService;
        private readonly AuthenticationService _authenticationService;
        private readonly IBffDataService _bffDataService;

        public ProductController(
            ILogger<ProductController> logger,
            IInvoiceReceiptBffService invoiceReceiptBffService,
            AuthenticationService authenticationService,
            IBffDataService bffDataService)
        {
            _logger = logger;
            _invoiceReceiptBffService = invoiceReceiptBffService;
            _authenticationService = authenticationService;
            _bffDataService = bffDataService;
        }

        [HttpGet]
        [Route("scanproduct")]
        public async Task<IActionResult> ScanProduct(string barcode, int? siteCode)
        {
            try
            {
                if (siteCode.HasValue)
                {
                    var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { siteCode });
                    if (!valid)
                        return Unauthorized();
                }

                var result = await _bffDataService.ScanProductBarcodeAsync(barcode, siteCode);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Scan product failed.");
                return Ok(new BffResultDto<ScannedProductBffDto>() { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpGet]
        [Route("getproducts")]
        public async Task<IActionResult> GetProductsAsync(string productCodes, short? siteCode)
        {
            try
            {
                if (siteCode.HasValue)
                {
                    var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { siteCode });
                    if (!valid)
                        return Unauthorized();
                }

                var productCodeList = productCodes.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                var bffResult = await _invoiceReceiptBffService.GetProductsAsync(productCodeList, siteCode);
                return Ok(bffResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get products failed");
                return Ok(new BffResultDto<List<ProductObjectBffDto>> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpGet]
        [Route("getproductinventories")]
        public async Task<IActionResult> GetProductInventoriesAsync(string productCodes, short? siteCode)
        {
            try
            {
                if (string.IsNullOrEmpty(productCodes))
                {
                    return BadRequest();
                }

                if (siteCode.HasValue)
                {
                    var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { siteCode });
                    if (!valid)
                        return Unauthorized();
                }

                var productInventories = await _bffDataService.GetProductInventoriesAsync(productCodes.Split(','), siteCode);
                return Ok(productInventories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get product inventory failed");
                return Ok(new BffResultDto<List<ProductInventoryBffDto>> { Data = new List<ProductInventoryBffDto>(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }
    }
}
