﻿namespace Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts
{
    public class InvoiceReceiptLineBffDto
    {
        public int LineNumber { get; set; }

        public string ProductCode { get; set; } = string.Empty;

        public decimal? ReceiptQuantity { get; set; }

        public decimal? InvoiceQuantity { get; set; }

        public decimal? ReceiptCostPriceExcludingInputTax { get; set; }

        public decimal? InvoiceCostPriceExcludingInputTax { get; set; }

        public decimal? FreightDisbursmentAmount { get; set; }

        public decimal? UpdateStandardSellingPrice { get; set; }

        public bool? RequestLabel { get; set; }

        public List<PurchaseOrderDisbursementBffDto> PurchaseOrderDisbursements { get; set; } = new();
    }
}