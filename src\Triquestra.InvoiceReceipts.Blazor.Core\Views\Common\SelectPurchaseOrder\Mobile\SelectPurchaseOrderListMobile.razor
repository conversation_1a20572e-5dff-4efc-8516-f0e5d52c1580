﻿@using Infinity.Blazor.UIControls.Layouts
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
@using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile.ProductItem
@using Telerik.Blazor.Components

<div class="productlist-container-mobile">
    @if (IsLoading)
    {
        <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
    }
    else if (!PurchaseOrders.Any())
    {
        <CardContainer>
            <CardBody Class="no-data-body">
                <div>
                    <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                </div>
                <label class="text-center text-muted">@Localizer.GetString(Translations.NoPurchaseOrdersFound)</label>
            </CardBody>
        </CardContainer>
    }
    else
    {
        foreach (var item in PurchaseOrders)
        {
            <SelectPurchaseOrderItemMobile @key="@item.PurchaseOrderCode" PurchaseOrder="item" CheckedChangedCommand="_itemCheckedChangedCommand" />
        }
    }
</div>