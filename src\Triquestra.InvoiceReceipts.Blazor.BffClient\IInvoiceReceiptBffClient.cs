﻿using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations;
using Triquestra.Common.Bff.DTOs.Bff.Taxes;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Blazor.BffClient
{
    public interface IInvoiceReceiptBffClient
    {
        Task<SystemConfigurationsBffDto> GetSystemConfigurationsAsync(int? siteCode);
        Task<List<SiteBffDto>> GetSitesAsync();
        Task<SupplierBffDto> GetSupplierAsync(string supplierCode);
        Task<List<SupplierBffDto>> GetActiveSuppliersAsync();
        Task<List<TaxBffDto>> GetTaxesAsync();
        Task<ScannedProductBffDto> ScanProductAsync(string barcode, int? siteCode);
        Task<List<TaxedProductObjectBffDto>> GetProductsAsync(List<string> productCodes, int? siteCode);
        Task<List<ProductInventoryBffDto>> GetProductInventoriesAsync(IEnumerable<string> productCodes, int? siteCode);
        Task<StockReceiptResponseBffDto> CreateInvoiceReceiptAsync(InvoiceReceiptCreateBffDto requestBffDto);
        Task<List<InvoiceReceiptBffDto>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto searchDto);
        Task<InvoiceReceiptBffDto> GetInvoiceReceiptsAsync(string stockReceiptCode);
        Task<StockReceiptResponseBffDto> UpdateInvoiceReceiptAsync(InvoiceReceiptUpdateBffDto requestBffDto);
        Task<StockReceiptResponseBffDto> UpdateInvoiceReceiptStatusAsync(InvoiceReceiptStatusUpdateBffDto requestBffDto);
        Task<List<PurchaseOrderBffDto>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto searchDto);
        Task<List<PurchaseOrderReceiptBffDto>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto searchDto);
        Task<List<PurchaseOrderBffDto>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto searchDto);
    }
}