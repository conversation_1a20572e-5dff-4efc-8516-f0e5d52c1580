﻿.summary-panel-container {
    margin-top: 80px;
}

.summary-panel-container ::deep .expand-cardcontainer .card-component {
    padding: 0px 32px 8px 32px;
    gap: 38px;
    display: flex;
    flex-direction: column;
}

.summary-panel-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding-top: 24px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
    margin-top: 15px;
}

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

.page-summary-info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

    .page-summary-info .triquestra-field-box {
        min-height: 40px;
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .page-summary-info .triquestra-field-box-edit-mode {
        display: flex;
        flex-direction: row;
    }

    .page-summary-info .additional-info-left {
        display: flex;
        flex-direction: column;
        width: 95%;
        padding-left: 5px;
    }

    .page-summary-info .div-left {
        font-weight: 500;
        font-size: 14px;
        line-height: 18.75px;
        color: #202020;
        width: 40%;
    }

    .page-summary-info .div-right {
        font-weight: 400;
        font-size: 14px;
        line-height: 16.41px;
        padding-right: 10px;
        width: 90%;
        text-align: right;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        word-break: break-all;
    }

    .page-summary-info .additional-info-right {
        display: flex;
        flex-direction: column;
        width: 95%;
        border-left: 1px;
        border-left-style: solid;
        border-left-color: #DCDCDC;
        padding-left: 15px;
    }

.expand-cardcontainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .expand-cardcontainer .separator::before,
    .expand-cardcontainer .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

.circle {
    display: inline-block;
    width: 12px;
    height: 12px;
}

    .circle:after {
        content: '';
        display: table;
        width: 12px;
        height: 12px;
        background: #D9D9D9;
        border-radius: 50%;
    }

::deep .k-card.telerik-blazor.k-card-vertical {
    /* make datepicker popup overlaps its parent */
    overflow: visible;
}

.currency-value {
    font-size: 20px;
    font-weight: 500;
}

.page-summary-info .div-right.receipt-total-value {
    font-size: 20px;
    font-weight: 500;
}