﻿@page "/app/inventory/login"
@using Microsoft.AspNetCore.Components.Authorization
@using Triquestra.InvoiceReceipts.Blazor.Launcher.Shared

@inject NavigationManager Navigation

<AuthorizeView>
    <Authorized>
        @{
            Navigation.NavigateTo(InvoiceReceiptNavigationUrls.InvoiceReceiptOverview, forceLoad: true);
        };
    </Authorized>
    <NotAuthorized>
        @* <RedirectToLogin /> *@
        <h1>Hello</h1>
    </NotAuthorized>
</AuthorizeView>
