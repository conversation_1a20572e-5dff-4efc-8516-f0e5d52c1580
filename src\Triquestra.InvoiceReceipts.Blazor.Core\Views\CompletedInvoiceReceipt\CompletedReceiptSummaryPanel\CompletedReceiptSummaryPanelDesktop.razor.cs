﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedReceiptSummaryPanel
{
    public partial class CompletedReceiptSummaryPanelDesktop
    {
        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        [EditorRequired]
        public InvoiceReceipt InvoiceReceipt { get; set; } = new();
    }
}