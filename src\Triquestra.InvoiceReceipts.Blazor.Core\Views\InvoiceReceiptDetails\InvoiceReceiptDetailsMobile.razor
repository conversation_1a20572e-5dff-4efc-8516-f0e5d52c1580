@using Infinity.Blazor.Components.ProductSearchDropdown.View
@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Loaders
@using Infinity.Blazor.UIControls.QRCodeScanner
@using Infinity.Blazor.UIControls.Tags
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptDetails
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductSearchPanel
@using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ReceiptSummaryPanel
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization;
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums

@inherits RazorBaseMvvm<InvoiceReceiptDetailsViewModel>
<div class="page-container @(_isDisbursementDialogVisible ? "d-none" : "")">
    <InfQRCodeScanner @ref="_qrCodeScannerRef" CodeScannedCommand="_codeScannedCommand" ScanFailedCommand="_scanFailedCommand" />

    <!-- header -->
    <div>
        <div class="page-header">
            <div class="header-group">
                <InfBackButton OnClick="ViewModel.NavigateBackAsync" />
                <div class="title">
                    @Localizer.GetString(Translations.InvoiceReceipt)
                </div>
            </div>
            <div class="buttons">
                @if (ViewModel.IsDraft)
                {
                    <InfButton Enabled="@(ViewModel.IsDataChanged && ViewModel.IsSaveEnabled())"
                               LabelText="@Localizer.GetString(Translations.UpdateDraft)"
                               FillMode="FillMode.Outline"
                               ClickCommand="ViewModel.UpdateDraftCommand" />
                }
                else
                {
                    <InfButton Enabled="@ViewModel.IsSaveEnabled()"
                               LabelText="@Localizer.GetString(Translations.SaveAsDraft)"
                               FillMode="FillMode.Outline"
                               ClickCommand="ViewModel.SaveDraftCommand" />
                }

                <InfButton Enabled="ViewModel.IsSaveEnabled()"
                           LabelText="@Localizer.GetString(Translations.Complete)"
                           FillMode="FillMode.Solid"
                           ClickCommand="_completeReceiptCommand">
                </InfButton>
            </div>
        </div>
    </div>
    @if (ViewModel.IsDataLoaded)
    {
        <ReceiptSummaryPanelMobile IsDraft="ViewModel.IsDraft"
                                   InvoiceSummary="ViewModel.InvoiceSummaryModel"
                                   RunningTotal="ViewModel.GetRunningTotal()"
                                   AdjustmentProductCost="ViewModel.AdjustmentProductCost"
                                   AdjustmentRequired="ViewModel.GetAdjustmentRequired()"
                                   FreightDisbursementType="ViewModel.FreightDisbursementType"
                                   FreightDisbursementTypeChanged="OnFreightDisbursementTypeChanged"
                                   IsFreightDisbursementEnabled="ViewModel.IsFreightDisbursementEnabled()"
                                   Note="@ViewModel.Note"
                                   NoteChanged="ViewModel.OnNoteChanged"
                                   InsertAdjustmentCommand="ViewModel.InsertAdjustmentCommand"
                                   DeleteDraftCommand="ViewModel.DeleteDraftCommand"
                                   Variance="ViewModel.Variance"
                                   IsBackOrders="ViewModel.IsBackOrders"
                                   IsBackOrdersChanged="OnIsBackOrdersChanged"
                                   HasAdjustmentLine="ViewModel.HasAdjustmentLine" />

        @if (!ViewModel.InvoiceSummaryModel.IsMatchToPurchaseOrders)
        {
            <div class="product-search-wrapper">
                <ProductSearchPanelMobile @ref="_productSearchPanelRef"
                                          IsLoading="ViewModel.IsLoading"
                                          IsProductNotFound="ViewModel.IsProductNotFound"
                                          SearchBarcodeCommand="_productDropdownSearchBarcodeCommand"
                                          SiteCode="(short?)ViewModel.InvoiceSummaryModel.SelectedSite?.SiteCode"
                                          SupplierCode="@ViewModel.InvoiceSummaryModel.SelectedSupplier?.SupplierCode"
                                          CameraActiveCommand="_cameraActiveCommand" />
            </div>
        }

        <div class="productlist-wrapper mt-3">
            <ProductListMobile @ref="_productListMobileRef"
                               IsLoading="ViewModel.IsDataListLoading"
                               SiteCode="@((int)(ViewModel.InvoiceSummaryModel.SelectedSite?.SiteCode ?? 0))"
                               ProductLines="ViewModel.ProductLines"
                               DataChangedCommand="_dataChangedCommand"
                               IsChangeSellPriceReceipt="ViewModel.SystemConfigurations.IsChangeSellPriceReceipt"
                               IsFreightEditable="ViewModel.FreightDisbursementType == FreightDisbursementType.Manual"
                               IsMatchToPurchaseOrder="ViewModel.InvoiceSummaryModel.IsMatchToPurchaseOrders"
                               IsBackOrders="ViewModel.IsBackOrders" />
        </div>
    }
    else
    {
        <InfLoadingBox />
    }
</div>

<DisbursementDialogMobile @ref="_disbursementDialogRef" @bind-Visible="_isDisbursementDialogVisible" ConfirmCommand="ViewModel.DisbursementConfirnAndCompleteCommand" />
<InfLoadingPopup @bind-Visible="ViewModel.IsLoadingVisible" IsModal />