﻿using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using System.Windows.Input;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Mobile
{
    public partial class SelectPurchaseOrderDialogMobile
    {
        private SelectPurchaseOrderListMobile? _selectPurchaseOrderListRef;

        private ICommand _selectedItemsChangedCommand;

        private IRelayCommand<object>? _backButtonClickCommand;

        [Parameter]
        public bool Visible { get; set; }

        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        [Parameter]
        public IRelayCommand<object>? ConfirmCommand { set; get; }

        public IEnumerable<PurchaseOrder> SelectedItems => _selectPurchaseOrderListRef?.SelectedItems.ToList() ?? new();

        public SelectPurchaseOrderDialogMobile()
        {
            _selectedItemsChangedCommand = new RelayCommand(StateHasChanged);
            _backButtonClickCommand = new AsyncRelayCommand<object>(BackButtonClickHandlerAsync);
        }

        protected override Task OnInitializedAsync()
        {
            ViewModel.PurchaseOrders.CollectionChanged += PurchaseOrders_CollectionChanged;
            return base.OnInitializedAsync();
        }

        private void PurchaseOrders_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        private async Task BackButtonClickHandlerAsync(object? _)
        {
            Visible = false;

            if (VisibleChanged.HasDelegate)
            {
                await VisibleChanged.InvokeAsync(Visible);
            }
        }

        public async Task LoadDataAsync(int siteCode, string supplierCode)
        {
            await ViewModel.InitializeAsync(siteCode, supplierCode);
        }

        public override void Dispose()
        {
            ViewModel.PurchaseOrders.CollectionChanged -= PurchaseOrders_CollectionChanged;
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}