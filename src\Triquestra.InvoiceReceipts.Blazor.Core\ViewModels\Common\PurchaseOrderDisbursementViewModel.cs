﻿using Infinity.InvoiceReceipts.Blazor.Constants;
using System.Collections.ObjectModel;
using Telerik.DataSource.Extensions;
using Triquesta.InfinityWeb.Common.Base;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.Common.Helpers;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.Common
{
    public class PurchaseOrderDisbursementViewModel : BaseViewModel
    {
        private readonly IInvoiceReceiptService _invoiceReceiptService;

        private List<int> _userSiteCodes = new List<int>();

        private bool _isLoading;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetValue(ref _isLoading, value);
        }

        private ObservableCollection<PurchaseOrderLine> _purchaseOrderLines = new();

        public ObservableCollection<PurchaseOrderLine> PurchaseOrderLines
        {
            get => _purchaseOrderLines;
            set => SetValue(ref _purchaseOrderLines, value);
        }

        public PurchaseOrderDisbursementViewModel(IInvoiceReceiptService invoiceReceiptService)
        {
            _invoiceReceiptService = invoiceReceiptService;
        }

        public async Task LoadDataAsync(int siteCode, List<ProductLineViewModel> invoiceReceiptLines)
        {
            IsLoading = true;

            PurchaseOrderLines.Clear();

            var productCodes = invoiceReceiptLines
                .Select(x => x.ProductCode!)
                .Where(x => !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x))
                .ToList();

            var supplierCodes = invoiceReceiptLines.Select(x => x.SupplierCode).Distinct().ToList();

            if (_userSiteCodes.Count == 0)
            {
                var sites = await _invoiceReceiptService.GetSitesAsync();
                _userSiteCodes = sites.Select(x => x.SiteCode!.Value).ToList();
            }

            var siteCodes = new List<int> { InvoiceReceiptConstants.SiteCodeHeadOffice };
            siteCodes.AddRange(_userSiteCodes);

            var purchaseOrders = await _invoiceReceiptService.SearchPurchaseOrdersForDisbursementAsync(new PurchaseOrderSearchRequestBffDto
            {
                SiteCodes = siteCodes,
                DestinationSiteCodes = new List<int> { siteCode, InvoiceReceiptConstants.SiteCodeZero },
                SupplierCodes = supplierCodes,
                ProductCodes = productCodes,
                PageSize = ApiConstants.MaxPageSize,
                Offset = 0,
                ResponseType = ApiConstants.FullResponseType,
            });

            if (purchaseOrders.Count == 0)
            {
                IsLoading = false;
                return;
            }

            var purchaseOrderCodes = purchaseOrders.Select(x => x.PurchaseOrderCode!).ToList();

            var allPurchaseOrderReceipts = await _invoiceReceiptService.SearchCompletedPurchaseOrderReceiptsAsync(new PurchaseOrderReceiptSearchRequestBffDto
            {
                SiteCodes = new List<int> { siteCode },
                PurchaseOrderCodes = purchaseOrderCodes,
            });
            
            allPurchaseOrderReceipts = allPurchaseOrderReceipts.OrderByDescending(receipt => receipt.UpdatedOrCreated).ToList();

            var disbursements = GetDisbursementList(invoiceReceiptLines, purchaseOrders, allPurchaseOrderReceipts);

            PurchaseOrderLines.AddRange(disbursements);

            IsLoading = false;
        }

        private static List<PurchaseOrderLine> GetDisbursementList(
            List<ProductLineViewModel> invoiceReceiptLines, 
            List<PurchaseOrder> purchaseOrders, 
            List<PurchaseOrderReceipt> purchaseOrderReceipts)
        {
            var allPurchaseOrderLines = purchaseOrders
                .SelectMany(x => x.Lines)
                .OrderBy(x => x.ProductCode)
                .ThenBy(x => x.Logged)
                .ToList();

            foreach (var invoiceReceiptLine in invoiceReceiptLines)
            {
                var productCode = invoiceReceiptLine.ProductCode;
                var remainingInvoiceReceiptQuantity = invoiceReceiptLine.ReceiptQuantity;

                var purchaseOrderLines = allPurchaseOrderLines.Where(x => x.ProductCode == productCode).ToList();

                if (purchaseOrderLines.Count > 0)
                {
                    foreach (var purchaseOrderLine in purchaseOrderLines)
                    {
                        if (remainingInvoiceReceiptQuantity < 0)
                        {
                            break;
                        }

                        (decimal receiptQuantity, decimal backOrder) = CalculateDataForPurchaseOrderLine(purchaseOrderLine, purchaseOrderReceipts, remainingInvoiceReceiptQuantity);
                        
                        purchaseOrderLine.ReceiptQuantity = receiptQuantity;
                        purchaseOrderLine.BackOrder = backOrder;
                        purchaseOrderLine.MaxBackOrder = backOrder;

                        remainingInvoiceReceiptQuantity -= purchaseOrderLine.ReceiptQuantity;
                    }
                }
            }

            return allPurchaseOrderLines.Where(x => x.ReceiptQuantity > 0).ToList();
        }

        private static (decimal receiptQuantity, decimal backOrder) CalculateDataForPurchaseOrderLine(PurchaseOrderLine purchaseOrderLine, List<PurchaseOrderReceipt> purchaseOrderReceipts, decimal remainingInvoiceReceiptQuantity)
        {
            var purchaseOrderReceipt = purchaseOrderReceipts
                .Find(receipt => receipt.PurchaseOrderCode == purchaseOrderLine.PurchaseOrderCode && receipt.Lines.Exists(line => line.ProductCode == purchaseOrderLine.ProductCode));

            var purchaseOrderReceiptLine = purchaseOrderReceipt?.Lines.Find(x => x.ProductCode == purchaseOrderLine.ProductCode);

            var quantityToReceipt = purchaseOrderReceiptLine == null ?
                purchaseOrderLine.PurchaseQuantity.GetValueOrDefault(0) :
                purchaseOrderReceiptLine.BackOrderQuantity.GetValueOrDefault(0);

            return CalculateReceiptQuantityAndBackOrder(quantityToReceipt, remainingInvoiceReceiptQuantity);
        }

        private static (decimal receiptQuantity, decimal backOrder) CalculateReceiptQuantityAndBackOrder(decimal quantityToReceipt, decimal remainingInvoiceReceiptQuantity)
        {
            var receiptQuantity = 0m;
            var backOrder = 0m;

            if (quantityToReceipt >= remainingInvoiceReceiptQuantity)
            {
                receiptQuantity = remainingInvoiceReceiptQuantity;
                backOrder = quantityToReceipt - receiptQuantity;
            }
            else
            {
                receiptQuantity = quantityToReceipt;
                backOrder = 0;
            }

            backOrder = Math.Max(0, backOrder);

            return (receiptQuantity, backOrder);
        }
    }
}