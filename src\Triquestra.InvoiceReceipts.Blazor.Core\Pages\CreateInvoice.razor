@page "/app/inventory/receipt-invoice/create-invoice"
@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Constants;
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Icons;
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Selectors.RadioGroup
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
@using Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Desktop
@using Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Mobile
@using Microsoft.AspNetCore.Authorization
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate
@using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate
@using Triquestra.InvoiceReceipts.Common.Constants

@inherits RazorBaseMvvm<CreateInvoiceViewModel>
@attribute [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]

<TriquestraMediaQuery Media="@UIControlConstants.MobileScreenMediaQuery" OnChange="((changed) => ViewModel.IsMobile = changed)" />

<div class="create-page-container" style="display: @(_isSelectPurchaseOrderDialogVisible && ViewModel.IsMobile ? "none" : "block")">
    <!-- header -->
    <div>
        <div class="create-page-header">
            <div class="header-group">
                <InfBackButton OnClick="ViewModel.RedirectToOverviewPage" />
                <div class="title">@Localizer.GetString(Translations.Invoice)</div>
            </div>
            <div class="buttons">
                <InfButton LabelText="@Localizer.GetString(ViewModel.IsMobile ? Translations.StartReceipt : Translations.StartInvoiceReceipt)"
                           FillMode="FillMode.Solid"
                           Enabled="ViewModel.IsDataValid"
                           ClickCommand="_startInvoiceReceiptCommand" />
            </div>
        </div>
    </div>

    <ExpandableCardContainer CanExpand="false">
        <TitleContent>
            <span class="@(ViewModel.IsMobile ? "fw-normal": string.Empty)">
                @Localizer.GetString(Translations.NewInvoiceReceiptDetails)
            </span>
        </TitleContent>
        <DescriptionContent>
            <div class="text-muted @(ViewModel.IsMobile ? "fs-14": "fs-16")">@Localizer.GetString(Translations.NewInvoiceReceiptDetails_Description)</div>
            @if (ViewModel.IsMobile)
            {
                <hr />
            }
        </DescriptionContent>
        <ExpandChildContent>
            <div class="pb-5">
                <div class="@(ViewModel.IsMobile ? "mt-2" : "mt-5")">
                    <label class="input-label">@Localizer.GetString(Translations.Site)</label>
                    <InfComboBox @bind-Value="@ViewModel.SelectedSiteCode"
                                 Data="@ViewModel.Sites"
                                 Class="input-form triquestra-combobox no-box-shadow"
                                 Width="100%"
                                 ClearButton="false"
                                 ValueField="@nameof(Site.SiteCode)"
                                 TextField="@nameof(Site.Name)"
                                 IsVirtualScrollMode="true"
                                 ItemHeight="45"
                                 PageSize="10"
                                 PopupHeight="250px"
                                 Enabled="@(ViewModel.Sites.Count > 1)">
                    </InfComboBox>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.Supplier)</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfComboBox @ref="_supplierDropdownRef"
                                         @bind-Value="@ViewModel.SelectedSupplierCode"
                                         Data="@ViewModel.Suppliers"
                                         Class="input-form triquestra-combobox no-box-shadow flex-grow-1"
                                         Width="100%"
                                         ClearButton="false"
                                         ValueField="@nameof(Supplier.SupplierCode)"
                                         TextField="@nameof(Supplier.SupplierName)"
                                         IsVirtualScrollMode="true"
                                         ItemHeight="45"
                                         PageSize="10"
                                         PopupHeight="250px"
                                         Enabled="@(ViewModel.Suppliers.Any())"
                                         Placeholder="@(ViewModel.IsLoadingSuppliers ? Localizer.GetString(Translations.LoadingSuppliersPlaceholder) : string.Empty)"
                                         OnOpen="OnSupplierDropdownOpenHandler">
                                <ItemTemplate>
                                    @context.SupplierName (@context.SupplierCode)
                                </ItemTemplate>
                            </InfComboBox>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.InvoiceNumber)</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfTextBox @bind-Value="ViewModel.InvoiceNumber" Width="100%" MaxLength="25" />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.InvoiceDate)</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfDatePicker @bind-Value="ViewModel.InvoiceDate" />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.GrossAmount)</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfCurrencyTextBox Value="ViewModel.GrossAmount"
                                                ValueChanged="GrossAmountChanged"
                                                Min="0"
                                                Format="0.00" />
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.TaxAmount)</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfCurrencyTextBox Value="@(ViewModel.InputTaxAmount ?? ViewModel.CalculatedTaxAmount)"
                                                ValueChanged="TaxAmountChanged"
                                                Format="0.00" />
                        </div>
                    </div>
                </div>
                <div class="mt-4 total-line">
                    <span>@Localizer.GetString(Translations.Total)</span>
                    <span>@ViewModel.GetTotalAmount().ToString("C")</span>
                </div>
                <div class="mt-4">
                    <div class="d-flex justify-content-between">
                        <label class="input-label">@Localizer.GetString(Translations.FreightExtra) (@Localizer.GetString(Translations.Optional))</label>
                    </div>
                    <div class="d-flex flex-row justify-content-between">
                        <div class="flex-grow-1">
                            <InfCurrencyTextBox @bind-Value="ViewModel.FreightExtra" Min="0" />
                        </div>
                    </div>
                </div>
                @if (!ViewModel.IsForeignCurrencySelectedSupplier)
                {
                    <div class="mt-4 match-po-line">
                        <div>
                            <InfCheckbox LabelText="@Localizer.GetString(Translations.MatchToPurchaseOrders)" @bind-Value="ViewModel.IsMatchToPurchaseOrders" Enabled="true" />
                        </div>
                        <div class="checkbox-subtitle">@Localizer.GetString(Translations.MatchToPurchaseOrders_Description)</div>
                    </div>
                }
            </div>
        </ExpandChildContent>
    </ExpandableCardContainer>
</div>

@if (ViewModel.IsMobile == true)
{
    <SelectPurchaseOrderDialogMobile @ref="_selectPurchaseOrderDialogMobileRef"
                                     @bind-Visible="_isSelectPurchaseOrderDialogVisible"
                                     ConfirmCommand="_selectPurchaseOrderConfirmCommand" />
}
else if (ViewModel.IsMobile == false)
{
    <SelectPurchaseOrderDialogDesktop @ref="_selectPurchaseOrderDialogDesktopRef"
                                      @bind-Visible="_isSelectPurchaseOrderDialogVisible"
                                      ConfirmCommand="_selectPurchaseOrderConfirmCommand" />
}

<style>
    /*** hide the left menu and top bar, require the css code here ****/
    .k-drawer {
        display: none;
    }

    .card-header {
        display: none;
    }
</style>