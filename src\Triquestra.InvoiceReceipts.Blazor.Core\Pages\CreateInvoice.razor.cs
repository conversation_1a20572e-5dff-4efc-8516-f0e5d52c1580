﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Constants;
using Infinity.Blazor.UIControls.Utillities;
using Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Desktop;
using Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Mobile;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Pages
{
    public partial class CreateInvoice
    {
        private SelectPurchaseOrderDialogDesktop _selectPurchaseOrderDialogDesktopRef;
        private SelectPurchaseOrderDialogMobile _selectPurchaseOrderDialogMobileRef;
        private InfComboBox<Supplier,string> _supplierDropdownRef;

        private bool _isSelectPurchaseOrderDialogVisible;

        private IRelayCommand<object>? _startInvoiceReceiptCommand;

        private IRelayCommand<object>? _selectPurchaseOrderConfirmCommand;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        public CreateInvoice()
        {
            _startInvoiceReceiptCommand = new AsyncRelayCommand<object>(StartInvoiceReceiptHandlerAsync);
            _selectPurchaseOrderConfirmCommand = new RelayCommand<object>(SelectPurchaseOrderConfirmHandler);
        }

        protected override async Task OnInitializedAsync()
        {
            await ViewModel.LoadAsync();
            await base.OnInitializedAsync();
        }

        private async Task StartInvoiceReceiptHandlerAsync(object? _)
        {
            if (ViewModel.IsMatchToPurchaseOrders)
            {
                _isSelectPurchaseOrderDialogVisible = true;
                StateHasChanged();

                if (ViewModel.IsMobile)
                {
                    await _selectPurchaseOrderDialogMobileRef.LoadDataAsync(ViewModel.SelectedSiteCode!.Value, ViewModel.SelectedSupplierCode!);
                }
                else
                {
                    await _selectPurchaseOrderDialogDesktopRef.LoadDataAsync(ViewModel.SelectedSiteCode!.Value, ViewModel.SelectedSupplierCode!);
                }
            }
            else
            {
                ViewModel.StartInvoiceReceipt();
            }
        }

        private void SelectPurchaseOrderConfirmHandler(object? _)
        {
            _isSelectPurchaseOrderDialogVisible = false;
            StateHasChanged();

            var purchaseOrders = ViewModel.IsMobile ? 
                _selectPurchaseOrderDialogMobileRef.SelectedItems : 
                _selectPurchaseOrderDialogDesktopRef.SelectedItems;

            ViewModel.StartInvoiceReceipt(purchaseOrders.ToList());
        }

        private void GrossAmountChanged(decimal? value)
        {
            ViewModel.GrossAmount = value;
            ViewModel.InputTaxAmount = null;
            ViewModel.CalculatedTaxAmount = ViewModel.GrossAmount - ViewModel.GetTotalAmount();
        }

        private void TaxAmountChanged(decimal? value)
        {
            ViewModel.InputTaxAmount = value ?? 0;
        }

        private async Task OnSupplierDropdownOpenHandler(object value)
        {
            if (ViewModel.IsMobile)
            {
                const int yOffsetSupplierDropdown = -110;
                await JSRuntime.InvokeVoidAsync(UIControlJsFunctions.ScrollIntoView, _supplierDropdownRef.GetElementId(), yOffsetSupplierDropdown);
            }
        }
    }
}