﻿using Infinity.InvoiceReceipts.Blazor.Helpers;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Common.Helpers;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;

namespace Infinity.InvoiceReceipts.Blazor.Mappers
{
    public class InvoiceReceiptModelMapper : IInvoiceReceiptModelMapper
    {
        public ProductLineViewModel MapToProductLineViewModel(Product product)
        {
            return new()
            {
                ProductCode = product.ProductCode,
                SKU = product.SKU,
                Description = product.Description,
                Cost = product.Cost ?? 0,
                SupplierProductCode = product.SupplierProductCode,
                SupplierCode = product.SupplierCode,
                SupplierName = product.SupplierName,
                TargetMarginPercentage = product.TargetMarginPercentage ?? 0,
                StandardSellingPrice = product.StandardSellingPrice,
            };
        }

        public ProductLineViewModel MapToProductLineViewModel(InvoiceReceiptLine invoiceReceiptLine)
        {
            var result = new ProductLineViewModel()
            {
                LineNumber = invoiceReceiptLine.LineNumber,
                PackSize = 1,
                PurchaseQuantity = invoiceReceiptLine.PurchaseQuantity ?? 0,
                ReceiptQuantity = invoiceReceiptLine.ReceiptQuantity ?? 0,
                IsRequestLabel = invoiceReceiptLine.RequestLabel == true,
                ProductCode = invoiceReceiptLine.ProductCode,
                Cost = invoiceReceiptLine.ReceiptCostPriceExcludingInputTax ?? 0,
                FreightExtras = invoiceReceiptLine.FreightDisbursmentAmount,
                StandardSellingPrice = invoiceReceiptLine.UpdateStandardSellingPrice,
                InvoiceCost = invoiceReceiptLine.InvoiceCostPriceExcludingInputTax ?? 0,
                InvoiceQuantity = invoiceReceiptLine.InvoiceQuantity ?? 0,
                IsCorruptedData = invoiceReceiptLine.IsCorruptedData,
                PurchaseOrderDisbursements = invoiceReceiptLine.PurchaseOrderDisbursements.ToList(),
            };

            if (invoiceReceiptLine.PurchaseOrderDisbursements.Count > 0)
            {
                result.PurchaseOrderNumber = invoiceReceiptLine.PurchaseOrderDisbursements[0].PurchaseOrderNumber;
                result.BackOrder = invoiceReceiptLine.PurchaseOrderDisbursements[0].BackOrderQuantity ?? 0;
            }

            return result;
        }

        public ProductLineViewModel MapToProductLineViewModel(PurchaseOrderLine line)
        {
            return new ProductLineViewModel
            {
                ProductCode = line.ProductCode,
                InvoiceCost = line.Cost ?? 0,
                Cost = line.Cost ?? 0,
                PurchaseQuantity = line.PurchaseQuantity ?? 0,
                InvoiceQuantity = line.BackOrder ?? line.PurchaseQuantity ?? 0,
                ReceiptQuantity = line.BackOrder ?? line.PurchaseQuantity ?? 0,
                BackOrder = 0,
                PurchaseOrderNumber = line.PurchaseOrderCode,
            };
        }

        public InvoiceSummaryModel MapToInvoiceSummaryModel(InvoiceReceipt invoiceReceipt)
        {
            return new InvoiceSummaryModel()
            {
                FreightExtra = invoiceReceipt.TotalFreight,
                InvoiceDate = invoiceReceipt.InvoiceDate,
                InvoiceNumber = invoiceReceipt.InvoiceCode,
                SelectedSite = new Site
                {
                    SiteCode = invoiceReceipt.SiteCode,
                    Name = invoiceReceipt.SiteName,
                },
                SelectedSupplier = new Supplier
                {
                    SupplierCode = invoiceReceipt.SupplierCode,
                    SupplierName = invoiceReceipt.SupplierName,
                },
                Tax = invoiceReceipt.TotalTax,
                TotalAmount = invoiceReceipt.TotalInvoiceValue ?? 0,
                IsMatchToPurchaseOrders = invoiceReceipt.IsMatchedToPurchaseOrders,
                HasReceiptedPurchaseOrder = invoiceReceipt.HasReceiptedPurchaseOrder,
            };
        }

        public InvoiceReceiptLineBffDto MapToInvoiceReceiptLineBffDto(ProductLineViewModel productLine, bool isMatchedToPurchaseOrders, bool isPurchaseOrderDisbursements)
        {
            var result = new InvoiceReceiptLineBffDto()
            {
                LineNumber = productLine.LineNumber,
                ProductCode = productLine.ProductCode!,
                ReceiptQuantity = productLine.ReceiptQuantity,
                RequestLabel = productLine.IsRequestLabel,
                ReceiptCostPriceExcludingInputTax = productLine.Cost,
                FreightDisbursmentAmount = productLine.FreightExtras,
                UpdateStandardSellingPrice = productLine.StandardSellingPrice,
                InvoiceQuantity = productLine.InvoiceQuantity,
                InvoiceCostPriceExcludingInputTax = productLine.InvoiceCost,
                PurchaseOrderDisbursements = new List<PurchaseOrderDisbursementBffDto>()
            };

            if (isPurchaseOrderDisbursements && productLine.PurchaseOrderDisbursements.Count > 0)
            {
                result.PurchaseOrderDisbursements = productLine.PurchaseOrderDisbursements.Select(MapToPurchaseOrderDisbursementBffDto).ToList();
            }
            else if (isMatchedToPurchaseOrders && !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(productLine.ProductCode!))
            {
                result.PurchaseOrderDisbursements.Add(
                    new()
                    {
                        Quantity = productLine.ReceiptQuantity,
                        BackOrderQuantity = productLine.BackOrder,
                        PurchaseOrderNumber = productLine.PurchaseOrderNumber,
                    }
                );
            }

            return result;
        }

        private static PurchaseOrderDisbursementBffDto MapToPurchaseOrderDisbursementBffDto(PurchaseOrderDisbursement dto)
        {
            return new()
            {
                BackOrderQuantity = dto.BackOrderQuantity,
                PurchaseOrderNumber = dto.PurchaseOrderNumber,
                Quantity = dto.Quantity,
            };
        }
    }
}