﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Utillities
@using Telerik.Blazor;
@using Telerik.Blazor.Components

@inject IStringLocalizer<Resources> Localizer

<div>
    <InfExpandablePanelBar Expanded="@IsExpanded"
                           ItemClickCommand="_panelBarClickCommand"
                           Class="@GetItemCardStyleClass()"
                           Key="@ProductLine.Key"
                           HideHeaderWhenExpanded="true"
                           @bind-ExpandedItems="ExpandedItems">
        <HeaderContent>
            <SwipeableProductItemHeader Product="ProductLine" DeleteCommand="DeleteCommand" IsSwiped="IsSwiped" IsSwipedChanged="IsSwipedChanged" />
        </HeaderContent>
        <HeaderButtonsContent>
            @if (DeleteCommand != null)
            {
                <span class="delete" @onclick:stopPropagation>
                    <InfButton ClickCommand="DeleteCommand" ClickCommandParameter="@ProductLine"
                               FillMode="FillMode.Outline">
                        <IconTemplate>
                            <DeleteIcon Color="#B00020" />
                        </IconTemplate>
                    </InfButton>
                </span>
            }
        </HeaderButtonsContent>
        <ChildContent>
            <CardBody>
                @{
                    var isFreightProduct = ProductLine.ProductCode == InvoiceReceiptDataConstants.FreightProductCode;
                    var isSpecialProduct = isFreightProduct || ProductLine.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode;
                }
                <div class="card-item k-d-flex">
                    <div class="card-item-description-col k-d-flex-col">
                        @if (IsMatchToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.PONumber)</label>
                                <div class="card-item-value">@ProductLine.PurchaseOrderNumber</div>
                            </div>
                        }
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ProductCode)</label>
                            <div class="card-item-value">@ProductLine.ProductCode</div>
                        </div>
                        @if (!IsMatchToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.ProductName)</label>
                                <span class="text-end">@ProductLine.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)</span>
                            </div>
                        }
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.SKU)</label>
                            <div class="card-item-value">@ProductLine.SKU</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.SupProductCode)</label>
                            <span class="text-end">@ProductLine.SupplierProductCode</span>
                        </div>
                        @if (IsMatchToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.ProductName)</label>
                                <span class="text-end">@ProductLine.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)</span>
                            </div>
                        }
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.OnHandOrder)</label>
                            @if (!isSpecialProduct)
                            {
                                <span class="text-end">@ProductLine.QtyOnHand.ToString(UIConstants.DecimalDisplayFormat) / @ProductLine.QtyOnOrder.ToString(UIConstants.DecimalDisplayFormat)</span>
                            }
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.TGPM)</label>
                            @if (!isSpecialProduct)
                            {
                                <span class="text-end">@ProductLine.TargetMarginPercentage.ToString(UIConstants.DecimalTwoPlacesDisplayFormat)</span>
                            }
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.CGPM)</label>
                            @if (!isSpecialProduct)
                            {
                                <span class="text-end">@CurrentProfitMargin?.ToString(UIConstants.DecimalTwoPlacesDisplayFormat)</span>
                            }
                        </div>
                        <hr class="mt-3 mb-2" />
                        @if (IsMatchToPurchaseOrder)
                        {
                            <div class="card-item-line">
                                <label class="card-item-label">@Localizer.GetString(Translations.InvoiceCost)</label>
                                <InfCurrencyTextBox Value="_invoiceCost"
                                                    ValueChanged="(value) => OnInvoiceCostChanged(value!.Value)"
                                                    Width="100%"
                                                    Format="@UIConstants.DecimalFourPlacesDisplayFormat"
                                                    Enabled="!isSpecialProduct"
                                                    Min="@UIConstants.MinNumericTextBoxValue" />
                            </div>
                            <div class="card-item-line">
                                <label class="card-item-label">@Localizer.GetString(Translations.InvoiceQuantity)</label>
                                <InfNumericTextBox Value="@_invoiceQuantity"
                                                   ValueChanged="(value) => OnInvoiceQuantityChanged(value!.Value)"
                                                   Decimals="2" Width="100%"
                                                   Focusable="true" Customizable="true" TextAlign="TextAlign.Center"
                                                   InputMode="Infinity.Blazor.UIControls.InputFields.NumericKeyboardType.Decimal"
                                                   Min="@UIConstants.MinReceiptQuantity"
                                                   Enabled="!isSpecialProduct" />
                            </div>
                        }
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.ReceiptQuantity)</label>
                            <InfNumericTextBox Value="@_receiptQuantity"
                                               ValueChanged="(value) => OnReceiptQuantityChanged(value!.Value)"
                                               Decimals="2" Width="100%"
                                               Focusable="true" Customizable="true" TextAlign="TextAlign.Center"
                                               InputMode="Infinity.Blazor.UIControls.InputFields.NumericKeyboardType.Decimal"
                                               Min="@UIConstants.MinReceiptQuantity"
                                               Enabled="!isSpecialProduct" />
                        </div>
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.ReceiptPack)</label>
                            <InfNumericTextBox Value="@_receiptPack"
                                               ValueChanged="(value) => OnReceiptPackChanged(value!.Value)"
                                               Decimals="2" Width="100%"
                                               Focusable="true" Customizable="true" TextAlign="TextAlign.Center"
                                               InputMode="Infinity.Blazor.UIControls.InputFields.NumericKeyboardType.Decimal"
                                               Min="@UIConstants.MinReceiptQuantity"
                                               Enabled="!isSpecialProduct" />
                        </div>
                        @if (IsMatchToPurchaseOrder)
                        {
                            <div class="card-item-line">
                                <label class="card-item-label">@Localizer.GetString(Translations.BackOrders)</label>
                                <InfNumericTextBox Value="@_backOrders"
                                                   ValueChanged="(value) => OnBackOrdersChanged(value!.Value)"
                                                   Decimals="2" Width="100%"
                                                   Focusable="true" Customizable="true" TextAlign="TextAlign.Center"
                                                   InputMode="Infinity.Blazor.UIControls.InputFields.NumericKeyboardType.Decimal"
                                                   Min="@UIConstants.MinNumericTextBoxValue"
                                                   Enabled="!isSpecialProduct" />
                            </div>
                        }
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.Cost)</label>
                            <InfCurrencyTextBox Value="@_cost"
                                                ValueChanged="(value) => OnCostChanged(value!.Value)"
                                                Width="100%"
                                                Format="@UIConstants.DecimalFourPlacesDisplayFormat"
                                                Enabled="!isSpecialProduct"
                                                Min="@UIConstants.MinNumericTextBoxValue" />
                        </div>
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.TotalCost)</label>
                            <InfCurrencyTextBox Value="@_totalCost"
                                                ValueChanged="(value) => OnTotalCostChanged(value!.Value)"
                                                Width="100%"
                                                Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                Min="@UIConstants.MinNumericTextBoxValue"
                                                Enabled="!isSpecialProduct" />
                        </div>
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.SellPrice)</label>
                            <InfCurrencyTextBox Value="@_sellPrice"
                                                ValueChanged="(value) => OnSellPriceChanged(value!.Value)"
                                                Width="100%"
                                                Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                Enabled="@(!isSpecialProduct && IsChangeSellPriceReceipt)"
                                                Min="@UIConstants.MinNumericTextBoxValue" />
                        </div>
                        <div class="card-item-line">
                            <label class="card-item-label">@Localizer.GetString(Translations.FreightExtra)</label>
                            <InfCurrencyTextBox Value="@_freightExtras"
                                                ValueChanged="(value) => OnFreightExtrasChanged(value!.Value)"
                                                Width="100%"
                                                Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                Enabled="IsFreightEditable"
                                                Min="@UIConstants.MinNumericTextBoxValue" />
                        </div>
                    </div>
                </div>
                <div class="transfer-update-button my-2">
                    <InfButton Id="@UpdateButtonId"
                               FillMode="FillMode.Outline"
                               LabelText="@Localizer.GetString(Translations.Update)"
                               ClickCommand="_updateButtonClickCommand"
                               Enabled="_isDataChanged">
                    </InfButton>
                </div>
            </CardBody>
        </ChildContent>
    </InfExpandablePanelBar>
</div>