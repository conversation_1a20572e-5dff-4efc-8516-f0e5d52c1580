﻿using CommunityToolkit.Mvvm.Messaging;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Enums;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;
using Telerik.DataSource.Extensions;
using Triquesta.InfinityWeb.Common.Base;
using Triquesta.InfinityWeb.Common.Constants;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
{
    public class InvoiceReceiptOverviewDataListViewModel : BaseViewModel
    {
        private readonly IInvoiceReceiptService _invoiceReceiptService;
        private readonly NavigationManager _navigation;

        private List<int> _userSiteCodes = new List<int>();

        public ObservableCollection<InvoiceReceipt> InvoiceReceipts { get; } = new();

        public ObservableCollection<InvoiceReceiptChangedMessage> InvoiceReceiptChangedMessages { get; private set; } = new();

        private bool _isLoadingData;

        public bool IsLoadingData
        {
            get => _isLoadingData;
            set => SetValue(ref _isLoadingData, value);
        }

        private bool _isMobile;

        public bool IsMobile
        {
            get => _isMobile;
            set => SetValue(ref _isMobile, value);
        }

        public InvoiceReceiptOverviewDataListViewModel(
            IInvoiceReceiptService invoiceReceiptService,
            NavigationManager navigation)
        {
            _invoiceReceiptService = invoiceReceiptService;
            _navigation = navigation;

            RegisterChangedMessages();
        }

        public async Task LoadDataAsync(string invoiceCode, DateTime startDate, DateTime endDate)
        {
            var hasItemToBeRemoved = InvoiceReceiptChangedMessages.Any(x => x.EventType == InvoiceReceiptChangedEventType.Delete);

            IsLoadingData = !hasItemToBeRemoved;

            var searchInvoiceReceiptsTask = SearchInvoiceReceiptsAsync(invoiceCode, startDate, endDate);
            var tasks = new List<Task> { searchInvoiceReceiptsTask };

            if (hasItemToBeRemoved)
            {
                tasks.Add(Task.Delay(CommonUIConstants.TimeToHighlightBackgroundMiliseconds));
            }

            await Task.WhenAll(tasks);

            var invoiceReceipts = await searchInvoiceReceiptsTask;

            InvoiceReceipts.Clear();
            InvoiceReceipts.AddRange(invoiceReceipts);

            IsLoadingData = false;

            _ = RemoveHighlightedItemsAsync();
        }

        private async Task<List<InvoiceReceipt>> SearchInvoiceReceiptsAsync(string invoiceCode, DateTime startDate, DateTime endDate)
        {
            const int SearchForInvoiceCodeMonthRangeLimit = 6;

            if (_userSiteCodes.Count == 0)
            {
                var sites = await _invoiceReceiptService.GetSitesAsync();
                _userSiteCodes = sites.Select(x => x.SiteCode!.Value).ToList();
            }

            var siteCodes = new List<int> { InvoiceReceiptConstants.SiteCodeHeadOffice };
            siteCodes.AddRange(_userSiteCodes);

            var searchRequest = new InvoiceReceiptSearchRequestBffDto
            {
                InvoiceCode = invoiceCode,
                SiteCodes = siteCodes,
                PageSize = ApiConstants.MaxPageSize,
                Offset = 0,
                ResponseType = ApiConstants.SimpleResponseType,
            };

            if (string.IsNullOrEmpty(invoiceCode))
            {
                searchRequest.StartDate = TimeZoneInfo.ConvertTimeToUtc(startDate, TimeZoneInfo.Local);
                searchRequest.EndDate = TimeZoneInfo.ConvertTimeToUtc(endDate, TimeZoneInfo.Local);
            }
            else
            {
                searchRequest.StartDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Today.AddMonths(-SearchForInvoiceCodeMonthRangeLimit), TimeZoneInfo.Local);
                searchRequest.EndDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.Today.AddDays(1).AddTicks(-1), TimeZoneInfo.Local);
            }

            return await _invoiceReceiptService.SearchInvoiceReceiptsAsync(searchRequest);
        }

        public void NavigateToInvoiceReceipt(InvoiceReceipt invoiceReceipt)
        {
            if (invoiceReceipt.InvoiceReceiptStatus == InvoiceReceiptStatus.Draft)
            {
                _navigation.NavigateTo(string.Format(InvoiceReceiptNavigationUrls.InvoiceReceiptDraft, invoiceReceipt.StockReceiptCode));
            }
            else if (invoiceReceipt.InvoiceReceiptStatus == InvoiceReceiptStatus.Complete)
            {
                _navigation.NavigateTo(string.Format(InvoiceReceiptNavigationUrls.CompletedInvoiceReceipt, invoiceReceipt.StockReceiptCode));
            }
        }

        private void RegisterChangedMessages()
        {
            WeakReferenceMessenger.Default.Register<InvoiceReceiptOverviewDataListViewModel, InvoiceReceiptChangedMessage>(this, (viewModel, message) =>
            {
                InvoiceReceiptChangedMessages.Add(message);
            });
        }

        private async Task RemoveHighlightedItemsAsync()
        {
            bool hasHighlightedItems = InvoiceReceiptChangedMessages.Any(x => x.EventType == InvoiceReceiptChangedEventType.Draft || x.EventType == InvoiceReceiptChangedEventType.Complete);

            if (hasHighlightedItems)
            {
                await Task.Delay(CommonUIConstants.TimeToHighlightBackgroundMiliseconds);
            }

            InvoiceReceiptChangedMessages.Clear();
        }
    }
}