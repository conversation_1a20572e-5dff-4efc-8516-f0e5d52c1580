﻿@using Infinity.Blazor.UIControls.Icons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
@using Triquesta.InfinityWeb.Common.Base;

@inherits RazorBaseMvvm<InvoiceReceiptOverviewDataListViewModel>

@inject IStringLocalizer<Resources> Localizer

<div class="receipt-details-grid">
    @if (ViewModel.IsLoadingData)
    {
        <div class="grid-empty-content-wrapper">
            <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
        </div>
    }
    else if (!ViewModel.InvoiceReceipts.Any())
    {
        <div class="grid-empty-content-wrapper">
            <div>
                <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
            </div>
            <label class="text-muted">@Localizer.GetString(Translations.NoInvoiceReceiptsFound)</label>
        </div>
    }
    else
    {
        foreach (var item in ViewModel.InvoiceReceipts)
        {
            <div class="details-grid-mobile-card k-card k-card-horizontal @GetItemHighlightClass(item.StockReceiptCode)">
                <div class="k-vbox k-flex-grow">
                    <div class="k-card-body">
                        <div class="d-flex">
                            <StatusTag Status="@item.InvoiceReceiptStatus" />
                            <span class="k-card-title text-nowrap ms-2">
                                #@item.InvoiceCode
                            </span>
                        </div>
                        <div class="k-card-description">
                            @Localizer.GetString(Translations.Site): @item.SiteName
                        </div>
                        <div class="k-card-description">
                            @Localizer.GetString(Translations.Supplier): @item.SupplierName
                        </div>
                    </div>
                </div>

                <div class="k-card-actions k-card-actions-horizontal k-card-actions-start p-3" @onclick="() => ViewModel.NavigateToInvoiceReceipt(item)">
                    <ChevronRightIcon Width="16" Height="16" Color="#000000" />
                </div>
            </div>
        }
    }
</div>