kind: ConfigMap 
apiVersion: v1 
metadata:
  name: invoicereceipts-bff-configmap 
data:
  triquestra_api_host: "https://infinityapp01-dev.australiaeast.cloudapp.azure.com:8056"
  triquestra_api_base_url: "/Triquestra/"
  infinity_apis_host: "http://infinitypdev01.triquestratest.com"
  domain_api_client_id: "InfinityCloudClient"
  domain_api_client_secret: "P@ssw0rd!012"
  invoicereceipts_bff_base_path: "/Triquestra/invoicereceipts_bff"
  invoicereceipts_blazor_host_url: "https://infinitypdev01.triquestratest.com/app/inventory/invoice-receipt"
  identity_blazor_client_id: "Triquestra.Infinity"
  identity_blazor_client_secret: "4siSxgMJY6^rZBo9vf4Old5w#XConYUK"
  identity_server_authority: "https://infinitypdev01.triquestratest.com"
  invoicereceipts_cache_expiration_in_minutes: "60"