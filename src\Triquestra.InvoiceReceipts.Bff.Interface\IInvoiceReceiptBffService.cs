﻿using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;

namespace Triquestra.InvoiceReceipts.Bff.Interface
{
    public interface IInvoiceReceiptBffService
    {
        Task<BffResultDto<List<SiteBffDto>>> GetSitesAsync();

        Task<BffResultDto<SupplierBffDto>> GetSupplierAsync(string supplierCode);

        Task<BffResultDto<List<SupplierBffDto>>> GetActiveSuppliersAsync();

        Task<BffResultDto<TaxedProductObjectBffDto>> GetProductAsync(string productCode, short? siteCode);

        Task<BffResultDto<List<TaxedProductObjectBffDto>>> GetProductsAsync(List<string> productCodes, short? siteCode);

        BffResultDto<StockReceiptResponseBffDto> CreateInvoiceReceipt(InvoiceReceiptCreateBffDto bffDto);

        Task<BffResultDto<List<InvoiceReceiptBffDto>>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto requestBffDto);

        Task<BffResultDto<InvoiceReceiptBffDto>> GetInvoiceReceiptsAsync(string stockReceiptCode);

        BffResultDto<StockReceiptResponseBffDto> UpdateInvoiceReceipt(InvoiceReceiptUpdateBffDto requestBffDto);

        BffResultDto<StockReceiptResponseBffDto> UpdateInvoiceReceiptStatus(InvoiceReceiptStatusUpdateBffDto requestBffDto);
    }
}