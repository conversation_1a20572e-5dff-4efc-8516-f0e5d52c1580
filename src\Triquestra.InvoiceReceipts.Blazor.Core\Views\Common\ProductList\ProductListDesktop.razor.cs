﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Grids;
using Infinity.Blazor.UIControls.Grids.Mapping;
using Infinity.Blazor.UIControls.Grids.Parameters;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Utilities;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Telerik.Blazor.Components;
using Triquesta.InfinityWeb.Common.Services;
using Triquesta.InfinityWeb.Common.ViewModels;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList
{
    public partial class ProductListDesktop
    {
        private readonly InfGridViewSelectionParameters<ProductLineViewModel> _selectionParameters = new()
        {
            SelectionMode = SelectionMode.None
        };

        private readonly InfGridViewPageParemeters _pageParameters = new()
        {
            PageSize = 50,
            PageSizes = new() { null, 5, 10, 20, 50 }
        };

        private InfGridView<ProductLineViewModel>? _gridRef;

        private ICommand _rowRenderCommand;
        private IRelayCommand<object?> _deleteCommand;
        private IRelayCommand<object?> _clearCommand;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IUserNotificationService UserNotificationService { get; set; }

        #region Properties

        [Parameter]
        public ObservableCollection<ProductLineViewModel> ProductLines { get; set; }

        [Parameter]
        public EventCallback DataChanged { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsChangeSellPriceReceipt { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsFreightEditable { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsMatchToPurchaseOrders { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsBackOrders { get; set; }

        #endregion Properties

        private bool IsAnyProductChecked => ProductLines.Any(x => x.IsChecked);

        public ProductListDesktop()
        {
            _rowRenderCommand = new RelayCommand<GridRowRenderEventArgs>(OnRowRenderHandler);
            _selectionParameters.SelectedItemsChangedCommand = new RelayCommand(StateHasChanged); // Enable/disable "Delete selected rows" button
            _deleteCommand = new AsyncRelayCommand<object?>(DeleteRowsHandlerAsync);
            _clearCommand = new AsyncRelayCommand<object?>(ClearTableHandlerAsync);
        }

        public async Task SetExpanedItemsAsync(IEnumerable<ProductLineViewModel> items)
        {
            if (_gridRef != null)
            {
                await _gridRef.SetExpanedItemsAsync(items);
            }
        }

        private async Task OnRequestLabelChangedAsync(ProductLineViewModel line, bool value)
        {
            line.IsRequestLabel = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnCostChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.Cost = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnInvoiceCostChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.InvoiceCost = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnTotalCostChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.TotalCost = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnSellPriceChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.StandardSellingPrice = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnFreightExtrasChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.FreightExtras = value;
            await DataChanged.InvokeAsync();
        }

        private async Task OnReceiptQuantityChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.ReceiptQuantity = value;
            UpdateBackOrders(line);
            await DataChanged.InvokeAsync();
        }

        private async Task OnReceiptPackChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.ReceiptPack = value;
            UpdateBackOrders(line);
            await DataChanged.InvokeAsync();
        }

        private async Task OnBackOrderChangedAsync(ProductLineViewModel line, decimal value)
        {
            if (line.ReceiptQuantity + value <= line.PurchaseQuantity)
            {
                line.BackOrder = value;
                await DataChanged.InvokeAsync();
            }
        }

        private async Task OnInvoiceQuantityChangedAsync(ProductLineViewModel line, decimal value)
        {
            line.InvoiceQuantity = value;

            if (line.ReceiptQuantity < line.InvoiceQuantity)
            {
                await OnReceiptQuantityChangedAsync(line, line.InvoiceQuantity);
            }

            await DataChanged.InvokeAsync();
        }

        private void UpdateBackOrders(ProductLineViewModel line)
        {
            line.BackOrder = IsBackOrders ? InvoiceReceiptCalculator.CalculateBackOrder(line.PurchaseQuantity, line.ReceiptQuantity) : 0;
        }

        private void OnRowRenderHandler(GridRowRenderEventArgs? args)
        {
            if (args?.Item is ProductLineViewModel item)
            {
                args.Class += $" item-{item.ProductCode.Trim()}-row";

                if (item.IsFlaggedForUpdatedState)
                {
                    args.Class += " productlist-item-blue-color";
                }

                if (item.CurrentProfitMargin < item.TargetMarginPercentage)
                {
                    args.Class += " text-red";
                }
            }
        }

        private async Task DeleteRowsHandlerAsync(object? _)
        {
            var answer = await UserNotificationService.ConfirmCancelAsync(
                Localizer.GetString(Translations.DeleteSelectedItemsConfirm_Message),
                Localizer.GetString(Translations.DeleteConfirmation),
                Localizer.GetString(Translations.YesDelete));

            if (answer == Answer.Yes)
            {
                var checkedProducts = ProductLines.Where(x => x.IsChecked).ToList();
                foreach (var item in checkedProducts)
                {
                    ProductLines.Remove(item);
                }

                await DataChanged.InvokeAsync();
            }
        }

        private async Task ClearTableHandlerAsync(object? _)
        {
            var answer = await UserNotificationService.ConfirmCancelAsync(
                Localizer.GetString(Translations.ClearTableConfirm_Message),
                Localizer.GetString(Translations.ClearTableConfirmation),
                Localizer.GetString(Translations.YesClear));

            if (answer == Answer.Yes)
            {
                ProductLines.Clear();
                await DataChanged.InvokeAsync();
            }
        }
    }
}