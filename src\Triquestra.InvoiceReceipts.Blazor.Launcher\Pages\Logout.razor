﻿@page "/app/inventory/logout"
@using Microsoft.AspNetCore.Components.Authorization
@using Triquestra.InvoiceReceipts.Blazor.Launcher.Shared
@inject NavigationManager Navigation

<AuthorizeView>
    <Authorized>
        <RedirectToLogout ReturnUrl="@context.User.FindFirst("bff:logout_url")?.Value" />
    </Authorized>
    <NotAuthorized>
        @{
            Navigation.NavigateTo(InvoiceReceiptNavigationUrls.InvoiceReceiptOverview, forceLoad: true);
        }
    </NotAuthorized>
</AuthorizeView>
