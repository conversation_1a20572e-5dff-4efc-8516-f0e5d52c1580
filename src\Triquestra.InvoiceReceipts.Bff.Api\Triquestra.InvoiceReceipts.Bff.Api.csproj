﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.8" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Triquestra.BffApi.Common" Version="1.2.3" />
    <PackageReference Include="Triquestra.Common.Authorization" Version="1.2.3" />
    <PackageReference Include="Triquestra.IdentityServer.BffApi.Authorization" Version="1.2.3" />
	<PackageReference Include="Triquestra.InfinityAPI.Common.Logging.AppInsights" Version="*******" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Bff.Interface\Triquestra.InvoiceReceipts.Bff.Interface.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Bff.Service\Triquestra.InvoiceReceipts.Bff.Service.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Common\Triquestra.InvoiceReceipts.Common.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.DTOs\Triquestra.InvoiceReceipts.DTOs.csproj" />
  </ItemGroup>

</Project>
