<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<PackageID>Infinity.InvoiceReceipts.Blazor</PackageID>
	<RootNamespace>Infinity.InvoiceReceipts.Blazor</RootNamespace>
	<AssemblyName>Infinity.InvoiceReceipts.Blazor</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Infinity.Blazor.UIControls" Version="1.2.93" />
    <PackageReference Include="Triquesta.InfinityWeb.Common" Version="1.2.26" />
    <PackageReference Include="Triquestra.Base.Common.Domain" Version="*******" />
	<PackageReference Include="Infinity.Blazor.Components" Version="1.2.91" />
  </ItemGroup>
	
	<ItemGroup>
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.BffClient\Triquestra.InvoiceReceipts.Blazor.BffClient.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Common\Triquestra.InvoiceReceipts.Blazor.Common.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Mappers\Triquestra.InvoiceReceipts.Blazor.Mappers.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Models\Triquestra.InvoiceReceipts.Blazor.Models.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Services\Triquestra.InvoiceReceipts.Blazor.Services.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.Common\Triquestra.InvoiceReceipts.Common.csproj" PrivateAssets="All" />
	  <ProjectReference Include="..\Triquestra.InvoiceReceipts.DTOs\Triquestra.InvoiceReceipts.DTOs.csproj" PrivateAssets="All" />
	</ItemGroup>
	
	<ItemGroup>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Blazor.BffClient.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Blazor.Common.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Blazor.Models.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Blazor.Services.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Blazor.Mappers.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.DTOs.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
		<_PackageFiles Include="$(OutputPath)\Triquestra.InvoiceReceipts.Common.dll">
			   <BuildAction>None</BuildAction>
			   <PackagePath>lib\net8.0\</PackagePath>
		</_PackageFiles>
	</ItemGroup>
	
	<ItemGroup>
	  <Content Update="Views\Common\ProductSearchPanel\ProductSearchPanelMobile.razor">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	  </Content>
	  <Content Update="Views\InvoiceReceiptDetails\InvoiceReceiptDetailsMobile.razor">
	    <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	  </Content>
	</ItemGroup>
</Project>
