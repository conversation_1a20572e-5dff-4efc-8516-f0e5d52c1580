﻿namespace Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts
{
    public class InvoiceReceiptSearchRequestBffDto
    {
        public List<int> SiteCodes { get; set; } = new();

        public string InvoiceCode { get; set; } = string.Empty;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public string InvoiceReceiptStatus { get; set; } = string.Empty;

        public int PageSize { get; set; }

        public int Offset { get; set; }

        public string ResponseType { get; set; } = string.Empty;
    }
}
