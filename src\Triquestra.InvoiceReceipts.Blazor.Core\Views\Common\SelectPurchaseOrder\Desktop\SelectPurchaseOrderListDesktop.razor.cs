using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Grids;
using Infinity.Blazor.UIControls.Grids.Mapping;
using Infinity.Blazor.UIControls.Grids.Parameters;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.SelectPurchaseOrder.Desktop
{
    public partial class SelectPurchaseOrderListDesktop
    {
        private static readonly string LoggedDateTimeFormat = "{0:dd/MM/yyyy hh:mm:ss tt}";

        private readonly InfGridViewSelectionParameters<PurchaseOrder> _gridSelectionParameters;

        private InfGridView<PurchaseOrder>? _gridRef;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public ObservableCollection<PurchaseOrder> PurchaseOrders { get; set; } = new();

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        public ICommand? SelectedItemsChangedCommand { get; set; }

        public IEnumerable<PurchaseOrder> SelectedItems => _gridRef?.SelectedItems ?? new List<PurchaseOrder>();

        public SelectPurchaseOrderListDesktop()
        {
            _gridSelectionParameters = new InfGridViewSelectionParameters<PurchaseOrder>
            {
                SelectionMode = SelectionMode.Multiple,
                SelectedItemsChangedCommand = new RelayCommand(SelectedItemsChangedHandler)
            };
        }

        private void SelectedItemsChangedHandler()
        {
            StateHasChanged();

            if (SelectedItemsChangedCommand?.CanExecute(null) == true)
            {
                SelectedItemsChangedCommand.Execute(null);
            }
        }
    }
}