﻿using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;

namespace Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate
{
    public class SystemConfigurations
    {
        public bool IsGoodsReceiptPrintLabel { get; set; }

        public bool IsChangeSellPriceReceipt { get; set; }

        public bool IsInvoiceDisbursementsToPurchaseOrder { get; set; }

        public FreightDisbursementType FreightDisbursementType { get; set; }
    }
}