﻿namespace Triquestra.InvoiceReceipts.Blazor.Common.Constants
{
    public static class InvoiceReceiptClientSettingConstants
    {
        public const string BffHttpClientName = "InvoiceReceiptsBffApiClient";
        public const string BffBasePath = "InvoiceReceiptsBffBasePath";
        public const string BffHostUrl = "InvoiceReceiptsBffHostUrl";

        public const string GetBaseSystemConfigurationsUrl = "Configuration/getsystemconfigurations";

        public const string GetSupplierUrl = "InvoiceReceipt/getsupplier";
        public const string GetActiveSuppliersUrl = "InvoiceReceipt/getactivesuppliers";
        public const string GetSitesUrl = "InvoiceReceipt/getsites";
        public const string GetTaxesUrl = "InvoiceReceipt/gettaxes";
        public const string GetTargetMarginPercentageUrl = "InvoiceReceipt/gettargetmarginpercentage";
        public const string CreateInvoiceReceiptUrl = "InvoiceReceipt/createinvoicereceipt";
        public const string SearchInvoiceReceiptsUrl = "InvoiceReceipt/searchinvoicereceipts";
        public const string GetInvoiceReceiptUrl = "InvoiceReceipt/getinvoicereceipt";
        public const string UpdateInvoiceReceiptUrl = "InvoiceReceipt/updateinvoicereceipt";
        public const string UpdateInvoiceReceiptStatusUrl = "InvoiceReceipt/updateinvoicereceiptstatus";

        public const string SearchPurchaseOrdersForDisbursementUrl = "PurchaseOrder/searchpurchaseordersfordisbursement";
        public const string SearchCompletedPurchaseOrderReceiptsUrl = "PurchaseOrder/searchcompletedpurchaseorderreceipts";
        public const string SearchOpenPurchaseOrdersUrl = "PurchaseOrder/searchopenpurchaseorders";

        public const string ScanProductUrl = "Product/scanproduct";
        public const string GetProductsUrl = "Product/getproducts";
        public const string GetProductInventoriesUrl = "Product/getproductinventories";
    }
}