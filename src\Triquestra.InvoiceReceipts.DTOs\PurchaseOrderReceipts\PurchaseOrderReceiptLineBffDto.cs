﻿namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts
{
    public class PurchaseOrderReceiptLineBffDto
    {
        public int? LineNumber { get; set; }

        public decimal? ReceiptQuantity { get; set; }

        public string ProductCode { get; set; } = string.Empty;

        public bool? RequestLabel { get; set; }

        public decimal? BackOrderQuantity { get; set; }

        public decimal? UnitCostPriceExcludingTax { get; set; }
    }
}