﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.Components.ProductSearchDropdown.View;
using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ProductSearchPanel
{
    public partial class ProductSearchPanelDesktop
    {
        private InfProductSearchDropdown _productSearchDropdownRef;

        private IRelayCommand<string>? _productDropdownInputValueChangedCommand;
        private IRelayCommand<ProductClientBffDto>? _productSelectFinalisedCommand;
        private bool _isExpressSearchEnabled;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IConfiguration Configuration { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        public bool IsProductNotFound { get; set; }

        [Parameter]
        public IRelayCommand<string>? SearchBarcodeCommand { get; set; }

        [Parameter]
        public short? SiteCode { get; set; }
        
        [Parameter]
        public string SupplierCode { get; set; }

        public bool IsByAnySupplier { get; private set; }

        private List<SupplierBffDto> PreselectedSuppliers => new List<SupplierBffDto>() { new SupplierBffDto { SupplierCode = SupplierCode } };

        public ProductSearchPanelDesktop()
        {
            _productDropdownInputValueChangedCommand = new RelayCommand<string>(ProductDropdownInputValueChangedHandler);
            _productSelectFinalisedCommand = new RelayCommand<ProductClientBffDto>(ProductSelectFinalisedHandler);
        }
        protected override Task OnInitializedAsync()
        {
            _isExpressSearchEnabled = Configuration.GetValue<bool>(AppSettingKeys.IsExpressSearchEnabled);
            return base.OnInitializedAsync();
        }

        public Task SetEnabledStatusAsync(bool isEnabled)
        {
            return _productSearchDropdownRef.SetElementEnabledStatusAsync(isEnabled);
        }

        public Task FocusAndSelectAllTextInSearchBoxAsync()
        {
            return _productSearchDropdownRef.FocusAndSelectAllTextInSearchBoxAsync();
        }

        private void ProductDropdownInputValueChangedHandler(string? barcode)
        {
            if (IsLoading || string.IsNullOrEmpty(barcode) || barcode.Trim().All(char.IsDigit) == false)
                return;

            TriggerSearchBarcodeCommand(barcode);
        }

        private void ProductSelectFinalisedHandler(ProductClientBffDto? product)
        {
            if (IsLoading || product == null)
                return;

            TriggerSearchBarcodeCommand(product.ProductCode);
        }

        private void TriggerSearchBarcodeCommand(string barcode)
        {
            if (!string.IsNullOrEmpty(barcode) && SearchBarcodeCommand?.CanExecute(barcode) == true)
            {
                SearchBarcodeCommand.Execute(barcode);
            }
        }
    }
}