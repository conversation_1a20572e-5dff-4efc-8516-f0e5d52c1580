@using Infinity.Blazor.Components.ProductSearchDropdown.View
@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Selectors
@using Triquestra.Common.Bff.DTOs.Bff

<div class="product-search-panel-container">
    <ExpandableCardContainer Title="@Localizer.GetString(Translations.AddProductsForReceipt)"
                             CanExpand="false"
                             Description="@Localizer.GetString(Translations.AddProductsForReceipt_Description)">
        <HeaderChildContent>
            <div class="d-flex">
                <InfButton LabelText="@Localizer.GetString(Translations.AddNewProduct)"
                           FillMode="FillMode.Outline"
                           Class="ms-2"
                           Visible="false">
                    <IconTemplate>
                        <AddIcon />
                    </IconTemplate>
                </InfButton>
            </div>
        </HeaderChildContent>
        <ExpandChildContent>
            <div class="separator mb-4" />
            <div class="scan-product-container">
                <div class="d-flex justify-content-between align-items-center">
                <div class="product-search-input-label">
                    @Localizer.GetString(Translations.Product)
                </div>
                <div class="mb-1">
                    <InfCheckbox LabelText="@Localizer.GetString(Translations.ByAnySupplier)" @bind-Value="IsByAnySupplier" />
                </div>
                </div>
                <div id="div-barcode" class="div-bottom tb-icon-container py-1">
                    <InfProductSearchDropdown @ref="_productSearchDropdownRef"
                                              Placeholder="@Localizer.GetString(Translations.ProductSearchInputPlaceholder)"
                                              Width="100%"
                                              Enabled="true"
                                              SiteCode="@SiteCode"
                                              PreselectedSuppliers="@(IsByAnySupplier ? new() : PreselectedSuppliers)"
                                              InputValueChangedCommand="_productDropdownInputValueChangedCommand"
                                              ProductSelectFinalisedCommand="_productSelectFinalisedCommand"
                                              MinimumFilterCharacters="2"
                                              AddButtonIconPosition="IconPosition.Left"
                                              IsRetailPriceInDropdownVisible="false" 
                                              IsSearchDropdownEnabled="_isExpressSearchEnabled"
                                              IsInactiveSuppliersProductsIncluded="false">
                        <AddButtonIconTemplate>
                            <AddIcon />
                        </AddButtonIconTemplate>
                        <IconTemplate>
                            <BarcodeScannerIcon Width="22" Height="18" />
                        </IconTemplate>
                    </InfProductSearchDropdown>
                    @if (IsProductNotFound)
                    {
                        <div class="error_message">
                            <ErrorIcon />
                            @Localizer.GetString(Translations.ScanProductNotFoundMessage)
                        </div>
                    }
                </div>
            </div>

        </ExpandChildContent>
    </ExpandableCardContainer>
</div>