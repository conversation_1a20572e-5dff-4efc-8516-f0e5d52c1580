﻿.product-search-panel-container ::deep .expand-cardcontainer .card-component {
    padding: 0;
}

.product-search-panel-container ::deep .expand-cardcontainer .k-card-body {
    border-radius: 12px 12px 0px 0px;
}

.scan-product-container {
    width: 100%;
    padding-bottom: 10px;
    max-height: 120px;
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .separator::before,
    .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

.product-search-input-label {
    font-weight: 500;
    font-size: 14px;
    line-height: 16.41px;
    color: #202020;
    margin-bottom: 5px;
}

/* enable absolute position for the icon */
.tb-icon-container {
    position: relative;
}
    /* add space to textbox without floating label */
    .tb-icon-container ::deep .k-textbox input.barcode,
    /* add space to floating label over the textbox */
    .k-floating-label-container.k-empty:not(.k-focus) .k-label {
        padding-right: 2em;
    }

.scan-product-container .error_message {
    margin-top: 10px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    color: #B00020;
}

::deep .triquestra-product-dropdown-container svg {
    z-index: 999;
}
