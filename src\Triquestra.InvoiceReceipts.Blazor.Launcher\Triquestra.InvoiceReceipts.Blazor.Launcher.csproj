﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
	<TargetFramework>net8.0</TargetFramework>
	<Nullable>enable</Nullable>
	<ImplicitUsings>enable</ImplicitUsings>
	<BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
	<UseBlazorWebAssembly>true</UseBlazorWebAssembly>
	<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	<GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Blazored.LocalStorage" Version="4.5.0" />
    <PackageReference Include="Infinity.Blazor.Components" Version="1.2.91" />
    <PackageReference Include="Infinity.Blazor.UIControls" Version="1.2.93" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.3" />
	<PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.3" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.3" />
    <PackageReference Include="Triquesta.InfinityWeb.Common" Version="1.2.26" />
    <PackageReference Include="Triquestra.Common.Bff.DTOs" Version="1.2.48" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Common\Triquestra.InvoiceReceipts.Blazor.Common.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Core\Triquestra.InvoiceReceipts.Blazor.Core.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Common\Triquestra.InvoiceReceipts.Common.csproj" />
  </ItemGroup>
</Project>
