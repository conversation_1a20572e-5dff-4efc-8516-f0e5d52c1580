﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddNewProduct" xml:space="preserve">
    <value>Add New Product</value>
  </data>
  <data name="AddProductsForReceipt" xml:space="preserve">
    <value>Add Products for Receipt</value>
  </data>
  <data name="AddProductsForReceipt_Description" xml:space="preserve">
    <value>Add products you want to receipt by scanning a product or searching for an individual product</value>
  </data>
  <data name="AdjustmentRequired" xml:space="preserve">
    <value>Adjustment Required</value>
  </data>
  <data name="AdjustmentRequired_Note" xml:space="preserve">
    <value>This is the difference between the invoice total and the running total of each item added to the receipt. If a difference exists, an adjustment must be manually inserted in order to complete the receipt</value>
  </data>
  <data name="BackOrder" xml:space="preserve">
    <value>Back Order</value>
  </data>
  <data name="BackOrders" xml:space="preserve">
    <value>Back Orders</value>
  </data>
  <data name="ByAnySupplier" xml:space="preserve">
    <value>by any supplier</value>
  </data>
  <data name="CGPM" xml:space="preserve">
    <value>CGPM</value>
  </data>
  <data name="ClearTable" xml:space="preserve">
    <value>Clear table</value>
  </data>
  <data name="ClearTableConfirmation" xml:space="preserve">
    <value>Clear table confirmation</value>
  </data>
  <data name="ClearTableConfirm_Message" xml:space="preserve">
    <value>Are you sure you want to clear table &amp; all items?</value>
  </data>
  <data name="Complete" xml:space="preserve">
    <value>Complete</value>
  </data>
  <data name="CompleteConfirmation" xml:space="preserve">
    <value>Complete confirmation</value>
  </data>
  <data name="CompleteConfirmation_Content" xml:space="preserve">
    <value>Are you sure you want to complete this receipt?</value>
  </data>
  <data name="CompletedBy" xml:space="preserve">
    <value>Completed By</value>
  </data>
  <data name="CompletedInvoice" xml:space="preserve">
    <value>Completed Invoice</value>
  </data>
  <data name="CompletedOn" xml:space="preserve">
    <value>Completed on</value>
  </data>
  <data name="CompleteFailed_Content" xml:space="preserve">
    <value>The Invoice receipt failed to complete. Please try again</value>
  </data>
  <data name="CompleteFailed_Title" xml:space="preserve">
    <value>Completing receipt failed</value>
  </data>
  <data name="CompleteReceipt" xml:space="preserve">
    <value>Complete Receipt</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="CreatedOn" xml:space="preserve">
    <value>Created on</value>
  </data>
  <data name="CreatedUpdated" xml:space="preserve">
    <value>Created / Updated</value>
  </data>
  <data name="CreateNewInvoice" xml:space="preserve">
    <value>Create New Invoice</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="DeleteConfirmation" xml:space="preserve">
    <value>Delete confirmation</value>
  </data>
  <data name="DeleteDraft" xml:space="preserve">
    <value>Delete Draft</value>
  </data>
  <data name="DeleteDraftConfirm_Content" xml:space="preserve">
    <value>Are you sure you want to delete this draft receipt?</value>
  </data>
  <data name="DeleteDraftFailed" xml:space="preserve">
    <value>Delete draft receipt failed</value>
  </data>
  <data name="DeleteDraftFailed_Content" xml:space="preserve">
    <value>The draft invoice receipt failed to delete. Please try again</value>
  </data>
  <data name="DeleteItemConfirm_Message" xml:space="preserve">
    <value>Are you sure you want to delete this item?</value>
  </data>
  <data name="DeleteSelectedItemsConfirm_Message" xml:space="preserve">
    <value>Are you sure you want to delete all selected items?</value>
  </data>
  <data name="DeleteSelectedRows" xml:space="preserve">
    <value>Delete selected rows</value>
  </data>
  <data name="Discard" xml:space="preserve">
    <value>Discard</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="DraftInvoice" xml:space="preserve">
    <value>Draft Invoice</value>
  </data>
  <data name="EnterInvoiceNumber" xml:space="preserve">
    <value>Enter Invoice Number</value>
  </data>
  <data name="FilterByDateRange" xml:space="preserve">
    <value>Filter by Date Range</value>
  </data>
  <data name="FreightDisbursementProblem_Content" xml:space="preserve">
    <value>The total of freight/extras on the line does not match the total freight/extras on the invoice. Please resolve</value>
  </data>
  <data name="FreightDisbursementProblem_Title" xml:space="preserve">
    <value>Freight/Extras disbursement problem</value>
  </data>
  <data name="FreightExtra" xml:space="preserve">
    <value>Freight/Extra</value>
  </data>
  <data name="FreightExtraDisbursement" xml:space="preserve">
    <value>Freight/Extra Disbursement</value>
  </data>
  <data name="FreightExtraDisbursement_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="FreightExtraDisbursement_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="FreightExtraDisbursement_PerLine" xml:space="preserve">
    <value>Per Line</value>
  </data>
  <data name="FreightExtraDisbursement_PerQty" xml:space="preserve">
    <value>Per Qty</value>
  </data>
  <data name="FreightExtras" xml:space="preserve">
    <value>Freight/Extras</value>
  </data>
  <data name="GrossAmount" xml:space="preserve">
    <value>Gross Amount</value>
  </data>
  <data name="Insert" xml:space="preserve">
    <value>Insert</value>
  </data>
  <data name="InsertAdjustmentConfirmation" xml:space="preserve">
    <value>Insert adjustment confirmation</value>
  </data>
  <data name="InsertAdjustmentMessage" xml:space="preserve">
    <value>Are you sure you want to add this adjustment to the receipt?</value>
  </data>
  <data name="InvCost" xml:space="preserve">
    <value>Inv. Cost</value>
  </data>
  <data name="InvNo" xml:space="preserve">
    <value>Inv no</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Invoice No</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Invoice Number</value>
  </data>
  <data name="InvoiceReceipt" xml:space="preserve">
    <value>Invoice Receipt</value>
  </data>
  <data name="InvoiceReceiptCompleted" xml:space="preserve">
    <value>Invoice Receipt Completed!</value>
  </data>
  <data name="InvoiceReceiptDeleted" xml:space="preserve">
    <value>Invoice Receipt Deleted!</value>
  </data>
  <data name="InvoiceReceiptDraftSaved" xml:space="preserve">
    <value>Invoice Receipt Draft Saved!</value>
  </data>
  <data name="InvoiceReceiptList" xml:space="preserve">
    <value>Invoice Receipt List</value>
  </data>
  <data name="InvoiceReceiptReport" xml:space="preserve">
    <value>Invoice Receipt Report</value>
  </data>
  <data name="InvoiceTotal" xml:space="preserve">
    <value>Invoice Total</value>
  </data>
  <data name="ItemsOnPO" xml:space="preserve">
    <value>Items on PO</value>
  </data>
  <data name="InvQty" xml:space="preserve">
    <value>Inv. Qty</value>
  </data>
  <data name="Label" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="LeaveConfirmation_Content" xml:space="preserve">
    <value>You have unsaved work, do you want to save it as a draft or just discard it?</value>
  </data>
  <data name="LeaveConfirmation_Title" xml:space="preserve">
    <value>Leave confirmation</value>
  </data>
  <data name="LoadingSuppliersPlaceholder" xml:space="preserve">
    <value>Loading suppliers. Please wait...</value>
  </data>
  <data name="LoadReceiptFailed" xml:space="preserve">
    <value>Loading receipt failed</value>
  </data>
  <data name="LoadReceiptFailed_Content" xml:space="preserve">
    <value>The Invoice receipt failed to load. Please try again</value>
  </data>
  <data name="Logged" xml:space="preserve">
    <value>Logged</value>
  </data>
  <data name="MatchToPurchaseOrders" xml:space="preserve">
    <value>Match to Purchase Order(s)</value>
  </data>
  <data name="MatchToPurchaseOrders_Description" xml:space="preserve">
    <value>When you select this option you will be prompted to select one or more purchase orders to match the invoice against</value>
  </data>
  <data name="Menu_InventoryManagement" xml:space="preserve">
    <value>Inventory Management</value>
  </data>
  <data name="Menu_InvoiceReceipt" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="Menu_Receipt" xml:space="preserve">
    <value>Receipt</value>
  </data>
  <data name="NewInvoiceReceipt" xml:space="preserve">
    <value>New Invoice Receipt</value>
  </data>
  <data name="NewInvoiceReceiptDetails" xml:space="preserve">
    <value>New Invoice Receipt Details</value>
  </data>
  <data name="NewInvoiceReceiptDetails_Description" xml:space="preserve">
    <value>Please enter details below to start the receipting of an invoice</value>
  </data>
  <data name="NoInvoiceReceiptsFound" xml:space="preserve">
    <value>No invoice receipts found.</value>
  </data>
  <data name="NoPurchaseOrdersToDisburse" xml:space="preserve">
    <value>No purchase orders available to disburse to</value>
  </data>
  <data name="NoPurchaseOrdersFound" xml:space="preserve">
    <value>No purchase orders found</value>
  </data>
  <data name="Note" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="Note_Placeholder" xml:space="preserve">
    <value>Additional note here</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="OnHandOrder" xml:space="preserve">
    <value>On Hand / Order</value>
  </data>
  <data name="Optional" xml:space="preserve">
    <value>Optional</value>
  </data>
  <data name="PONumber" xml:space="preserve">
    <value>PO Number</value>
  </data>
  <data name="PrintReceipt" xml:space="preserve">
    <value>Print Receipt</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProductCode" xml:space="preserve">
    <value>Product Code</value>
  </data>
  <data name="ProductList" xml:space="preserve">
    <value>Product List</value>
  </data>
  <data name="ProductList_NoData_Message" xml:space="preserve">
    <value>No products to receipt yet. Use the options above to add products</value>
  </data>
  <data name="ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="ProductSearchInputPlaceholder" xml:space="preserve">
    <value>Enter Product Description, Code, SKU or Scan barcode to populate product</value>
  </data>
  <data name="ProductSearchInputPlaceholderMobile" xml:space="preserve">
    <value>Enter Product Name, Code or SKU</value>
  </data>
  <data name="PurchaseOrderDisbursement" xml:space="preserve">
    <value>Purchase Order Disbursement</value>
  </data>
  <data name="ReceiptByInvoiceDescription" xml:space="preserve">
    <value>Create a new invoice to receipt or search for an existing invoice receipt</value>
  </data>
  <data name="ReceiptByInvoiceTitle" xml:space="preserve">
    <value>Receipt by Invoice</value>
  </data>
  <data name="ReceiptInformation" xml:space="preserve">
    <value>Receipt Information</value>
  </data>
  <data name="ReceiptNo" xml:space="preserve">
    <value>Receipt No</value>
  </data>
  <data name="ReceiptNote" xml:space="preserve">
    <value>Receipt Note</value>
  </data>
  <data name="ReceiptPack" xml:space="preserve">
    <value>Receipt Pack</value>
  </data>
  <data name="ReceiptQty" xml:space="preserve">
    <value>Receipt Qty</value>
  </data>
  <data name="ReceiptQuantity" xml:space="preserve">
    <value>Receipt Quantity</value>
  </data>
  <data name="ReceiptTotal" xml:space="preserve">
    <value>Receipt Total</value>
  </data>
  <data name="Receive" xml:space="preserve">
    <value>Receive</value>
  </data>
  <data name="RequestLabel" xml:space="preserve">
    <value>Request Label</value>
  </data>
  <data name="RunningTotal" xml:space="preserve">
    <value>Running Total</value>
  </data>
  <data name="SaveAsDraft" xml:space="preserve">
    <value>Save As Draft</value>
  </data>
  <data name="SaveDraftFailed_Content" xml:space="preserve">
    <value>The Invoice receipt failed to save to draft. Please try again</value>
  </data>
  <data name="SaveDraftFailed_Title" xml:space="preserve">
    <value>Save to draft failed</value>
  </data>
  <data name="SaveDraft_Confirm_Content" xml:space="preserve">
    <value>Are you sure you want to save this invoice receipt as a draft?</value>
  </data>
  <data name="SaveDraft_Confirm_Title" xml:space="preserve">
    <value>Save as draft confirmation</value>
  </data>
  <data name="ScanProductNotFoundMessage" xml:space="preserve">
    <value>Product not found. Please try another barcode.</value>
  </data>
  <data name="SearchByInvoiceNumber" xml:space="preserve">
    <value>Search by Invoice Number</value>
  </data>
  <data name="SearchForInvoiceNumber" xml:space="preserve">
    <value>Search for Invoice Number</value>
  </data>
  <data name="SelectPurchaseOrder" xml:space="preserve">
    <value>Select Purchase Order</value>
  </data>
  <data name="SellPrice" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>Site</value>
  </data>
  <data name="SKU" xml:space="preserve">
    <value>SKU</value>
  </data>
  <data name="StartedOn" xml:space="preserve">
    <value>Started on</value>
  </data>
  <data name="StartInvoiceReceipt" xml:space="preserve">
    <value>Start Invoice Receipt</value>
  </data>
  <data name="StartReceipt" xml:space="preserve">
    <value>Start Receipt</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupProductCode" xml:space="preserve">
    <value>Sup. Product Code</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="TaxAmount" xml:space="preserve">
    <value>Tax Amount</value>
  </data>
  <data name="TGPM" xml:space="preserve">
    <value>TGPM</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="TotalCost" xml:space="preserve">
    <value>Total Cost</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="UpdateDraft" xml:space="preserve">
    <value>Update Draft</value>
  </data>
  <data name="UpdateDraftConfirmation" xml:space="preserve">
    <value>Update draft confirmation</value>
  </data>
  <data name="UpdateDraftConfirmation_Content" xml:space="preserve">
    <value>Are you sure you want to update this invoice receipt draft?</value>
  </data>
  <data name="UpdateReceiptFailed" xml:space="preserve">
    <value>Update receipt failed</value>
  </data>
  <data name="UpdateReceiptFailed_Content" xml:space="preserve">
    <value>The invoice receipt failed to update. Please try again</value>
  </data>
  <data name="Variance" xml:space="preserve">
    <value>Variance</value>
  </data>
  <data name="YesClear" xml:space="preserve">
    <value>Yes, Clear</value>
  </data>
  <data name="YesComplete" xml:space="preserve">
    <value>Yes, Complete</value>
  </data>
  <data name="YesDelete" xml:space="preserve">
    <value>Yes, Delete</value>
  </data>
  <data name="YesInsert" xml:space="preserve">
    <value>Yes, Insert</value>
  </data>
  <data name="YesSaveAsDraft" xml:space="preserve">
    <value>Yes, Save As Draft</value>
  </data>
  <data name="YesUpdateDraft" xml:space="preserve">
    <value>Yes, Update Draft</value>
  </data>
  <data name="BackOrdersNote_Mobile" xml:space="preserve">
    <value>Place an order for (a product) that is temporarily out of stock</value>
  </data>
  <data name="InvoiceCost" xml:space="preserve">
    <value>Invoice Cost</value>
  </data>
  <data name="InvoiceQuantity" xml:space="preserve">
    <value>Invoice Quantity</value>
  </data>
  <data name="BackOrdersConfirmation" xml:space="preserve">
    <value>Back orders confirmation</value>
  </data>
  <data name="BackOrdersConfirmation_Content" xml:space="preserve">
    <value>Are you sure you want to change the back order flag?</value>
  </data>
  <data name="YesChange" xml:space="preserve">
    <value>Yes, Change</value>
  </data>
  <data name="ReceiptCost" xml:space="preserve">
    <value>Receipt Cost</value>
  </data>
  <data name="POMatchProblem" xml:space="preserve">
    <value>PO match problem</value>
  </data>
  <data name="POMatchProblemMessage" xml:space="preserve">
    <value>One or more of the matched purchase orders in this draft has since been receipted. You will need to delete this draft and start over.</value>
  </data>
</root>