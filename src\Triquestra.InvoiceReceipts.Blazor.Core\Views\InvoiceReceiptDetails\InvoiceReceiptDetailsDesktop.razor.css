﻿.page-header {
    background-color: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 72px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
}

    .page-header .title {
        align-self: center;
        font-weight: 400;
        font-size: 24px;
        line-height: 28.13px;
        color: #202020;
    }

    .page-header .header-group {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
    }

    .page-header ::deep .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 10px;
        height: 40px;
        margin-right: 2.5rem;
        align-self: center;
    }

    .page-header .buttons ::deep button {
        margin-left: 10px;
    }
/*****/
..page-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding-top: 24px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
    margin-top: 15px;
}

/*******/
..page-container ::deep .custom-tabs .k-splitter,
..page-container ::deep .k-card, .k-panelbar.customized-panelbar {
    border: none;
    margin-bottom: 3px;
}

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

.expand-cardcontainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .expand-cardcontainer .separator::before,
    .expand-cardcontainer .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

::deep .k-card.telerik-blazor.k-card-vertical {
    /* make datepicker popup overlaps its parent */
    overflow: visible;
}

.productlist-wrapper {
    padding: 0px 32px 32px 32px;
    margin-top: 30px;
}

.product-search-wrapper {
    margin: 16px 32px 0 32px;
}

    .product-search-wrapper ::deep > .k-card-body {
        border-radius: 12px 12px 0 0 !important;
        border-bottom: 1px solid #eee;
        box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.08) !important;
        position: relative;
    }