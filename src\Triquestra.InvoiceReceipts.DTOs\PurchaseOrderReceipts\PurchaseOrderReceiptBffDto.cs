﻿namespace Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts
{
    public class PurchaseOrderReceiptBffDto : PurchaseOrderReceiptBaseBffDto
    {
        public string StockReceiptCode { get; set; } = string.Empty;

        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? Created { get; set; }

        public string UpdatedBy { get; set; } = string.Empty;

        public DateTime? Updated { get; set; }

        public int NumberOfLines { get; set; }

        public List<PurchaseOrderReceiptLineBffDto> Lines { get; set; } = new();

        public DateTime? UpdatedCreated => Updated ?? Created;
    }
}