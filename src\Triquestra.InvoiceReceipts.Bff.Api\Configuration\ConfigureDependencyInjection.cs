﻿using NLog;
using NLog.Web;
using System.Reflection;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.Common.Bff.Mappers;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Bff.Service;
using Triquestra.InvoiceReceipts.Bff.Service.Mappers;
using Triquestra.InvoiceReceipts.DTOs;

namespace Triquestra.InvoiceReceipts.Bff.Api.Configuration
{
    public static class ConfigureDependencyInjection
    {
        public static AppConfigurationModel AddServiceDependencies(this WebApplicationBuilder builder, NLog.ILogger logger)
        {
            var configuration = AppConfigurationManager.RegisterTriquestraConfiguration(builder.Services, builder.Environment.IsDevelopment(),
                Assembly.GetExecutingAssembly().GetName().Name!, builder.Configuration, logger);

            builder.Services.AddSingleton(typeof(AuthenticationService));
            builder.Services.AddScoped<IBffDtoMapper, BffDtoMapperDefault>();
            builder.Services.AddScoped<IBffDataService, BffDataService>();
            builder.Services.AddScoped<IInvoiceReceiptsBffMapper, InvoiceReceiptsBffMapper>();
            builder.Services.AddScoped<IInvoiceReceiptBffService, InvoiceReceiptBffService>();
            builder.Services.AddScoped<IPurchaseOrderBffService, PurchaseOrderBffService>();
            builder.Services.AddScoped<IPurchaseOrderBffMapper, PurchaseOrderBffMapper>();

            return configuration;
        }

        public static void AddNLogDependencies(this WebApplicationBuilder builder)
        {
            builder.Logging.AddNLogWeb();
            var nlogContent = builder.Environment.IsDevelopment() ? builder.Configuration["NlogConfig"] : Environment.GetEnvironmentVariable("NLOG_CONFIG", EnvironmentVariableTarget.Process);
            if (!string.IsNullOrEmpty(nlogContent))
            {
                var xmlStream = new StringReader(nlogContent);
                var xmlReader = System.Xml.XmlReader.Create(xmlStream);
                LogManager.Configuration = new NLog.Config.XmlLoggingConfiguration(xmlReader, null);
            }
        }
    }
}