﻿@inject NavigationManager Navigation
@inject IConfiguration Configuration

@code {
    [Parameter]
    public string ReturnUrl { get; set; }

    protected override void OnInitialized()
    {
        var bffHost = Configuration["InvoiceReceiptsBffHostUrl"];
        var bffBasePath = Configuration["InvoiceReceiptsBffBasePath"];
        var returnPath = string.IsNullOrWhiteSpace(ReturnUrl) ? Navigation.ToBaseRelativePath(Navigation.Uri) : ReturnUrl;
        var returnUrl = Uri.EscapeDataString($"{bffBasePath}/AuthenticationRedirect/login?returnUrl=/" + returnPath);
                
        Navigation.NavigateTo($"{bffHost}{bffBasePath}/login?returnUrl={returnUrl}", forceLoad: true);
    }
}