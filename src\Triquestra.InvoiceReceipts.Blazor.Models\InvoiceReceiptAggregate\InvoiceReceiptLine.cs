﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate
{
    public class InvoiceReceiptLine : IAggregateRoot
    {
        public int LineNumber { get; set; }

        public string ProductCode { get; set; } = string.Empty;

        public decimal? PurchaseQuantity { get; set; }

        public decimal? ReceiptQuantity { get; set; }

        public decimal? InvoiceQuantity { get; set; }

        public decimal? ReceiptCostPriceExcludingInputTax { get; set; }

        public decimal? InvoiceCostPriceExcludingInputTax { get; set; }

        public decimal? FreightDisbursmentAmount { get; set; }

        public decimal? UpdateStandardSellingPrice { get; set; }

        public bool? RequestLabel { get; set; }

        public List<PurchaseOrderDisbursement> PurchaseOrderDisbursements { get; set; } = new();

        public bool IsCorruptedData { get; set; }
    }
}
