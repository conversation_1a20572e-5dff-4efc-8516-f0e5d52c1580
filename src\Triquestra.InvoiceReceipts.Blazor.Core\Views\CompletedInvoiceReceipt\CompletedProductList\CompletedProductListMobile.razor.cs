﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Constants;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System.Collections.ObjectModel;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList
{
    public partial class CompletedProductListMobile
    {
        private string? _expandedProductCode = null;

        public IRelayCommand<string> _selectProductCommand;

        [Inject]
        public IJSRuntime JSRuntime { get; set; }

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public ObservableCollection<ProductLineViewModel> ProductLines { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }

        [Parameter]
        public bool IsMatchedToPurchaseOrder { get; set; }

        public CompletedProductListMobile()
        {
            _selectProductCommand = new RelayCommand<string>(SelectProductChanged);
        }

        private bool IsCardExpand(string productCode)
        {
            return _expandedProductCode == productCode;
        }

        private void SelectProductChanged(string productCode)
        {
            if (string.IsNullOrEmpty(_expandedProductCode))
            {
                _expandedProductCode = productCode;
            }
            else
            {
                _expandedProductCode = productCode == _expandedProductCode ? null : productCode;
            }
            
            if (!string.IsNullOrWhiteSpace(_expandedProductCode))
            {
                _ = ScrollToElementBottomAsync(GetProductItemElementId(_expandedProductCode));
            }

            StateHasChanged();
        }

        private static string GetProductItemElementId(string productCode)
        {
            const string elementIdPrefix = "ProductItem_";
            return elementIdPrefix + productCode;
        }

        private async Task ScrollToElementBottomAsync(string elementId)
        {
            const int yOffset = 40;
            await Task.Delay(UIConstants.DelayTimeBeforeScrolling);
            await JSRuntime.InvokeVoidAsync(UIControlJsFunctions.ScrollElementToBottomIfOutOfView, elementId, yOffset);
        }
    }
}