﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.Components.Navigation.Models;
using Microsoft.AspNetCore.Components;
using Telerik.Blazor.Components;
using Triquestra.Common.Authorization.Authentication;

namespace Triquestra.InvoiceReceipts.Blazor.Launcher.Shared
{
    public partial class MobileLayout
    {
        private const int TimeToFocusOnTelerikComponent = 300;

        [Inject]
        public AuthenticationService AuthenticationService { get; set; }

        [Parameter]
        public RenderFragment? ChildContent { get; set; }

        [Parameter]
        public string UserName { get; set; } = string.Empty;

        public bool IsDrawerOpen { get; set; } = false;

        public bool IsUserAccountOpen { get; set; } = false;

        public string DrawerClass

        { get { return IsDrawerOpen ? "k-drawer-open" : "k-drawer-close"; } }

        public TelerikDrawer<MenuItemModel> Drawer { get; set; }

        [Parameter]
        public IEnumerable<MenuItemModel> MenuItems { get; set; }

        private string _pageTitle = "Infinity";

        private IRelayCommand<MenuItemModel> _menuItemSelectCommand;

        private IRelayCommand<object?> _toggleDrawerCommand;
        
        public MobileLayout()
        {
            _menuItemSelectCommand = new RelayCommand<MenuItemModel>(MenuItemSelectHandler);
            _toggleDrawerCommand = new AsyncRelayCommand<object?>(ToggleDrawerHandler);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Task.Delay(TimeToFocusOnTelerikComponent);
                StateHasChanged();
            }
            await base.OnAfterRenderAsync(firstRender);
        }

        private async Task ToggleDrawerHandler(object? _)
        {
            await Drawer.ToggleAsync();
        }

        private void ToggleUserAccount()
        {
            IsUserAccountOpen = !IsUserAccountOpen;
        }

        private void MenuItemSelectHandler(MenuItemModel model)
        {
            _pageTitle = model.Text;
        }

        private void ExpandedChangedHandler()
        {
            IsDrawerOpen = !IsDrawerOpen;

            if (IsDrawerOpen)
            {
                IsUserAccountOpen = false;
            }
        }
    }
}