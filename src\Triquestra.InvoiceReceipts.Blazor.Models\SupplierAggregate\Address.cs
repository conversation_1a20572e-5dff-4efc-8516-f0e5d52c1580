﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate
{
    public class Address : IAggregateRoot
    {
        public string Address1 { get; set; } = string.Empty;

        public string Address2 { get; set; } = string.Empty;

        public string Address3 { get; set; } = string.Empty;

        public string City { get; set; } = string.Empty;

        public string Postcode { get; set; } = string.Empty;

        public string State { get; set; } = string.Empty;

        public string Country { get; set; } = string.Empty;
    }
}