﻿using System.Collections;
using Triquestra.BffApi.Common.Configuration;
using Triquestra.InvoiceReceipts.DTOs;

namespace Triquestra.InvoiceReceipts.Bff.Api.Configuration
{
    public class TriquestraApplicationBuilder : TriquestraApplicationBuilderBase<AppConfigurationModel>
    {
        public TriquestraApplicationBuilder(
            bool isDevelopment, 
            string userAgent, 
            ConfigurationManager configurationManager, 
            NLog.ILogger logger)
            : base(isDevelopment, userAgent, configurationManager, logger)
        {
        }

        protected override AppConfigurationModel LoadAppSettings()
        {
            return new()
            {
                AccessDeniedPath = "/app/inventory",
                ExecutingAssemblyName = UserAgent,
                DomainApiClientId = GetConfigurationValue("DomainApi.ClientId"),
                DomainApiClientSecret = GetConfigurationValue("DomainApi.ClientSecret"),
                TriquestraApiApiRoot = GetConfigurationValue("TriquestraApi.ApiRoot"),
                TriquestraApiBaseUrl = GetConfigurationValue("TriquestraApi.BaseUrl"),

                InfinityApiApiRoot = GetConfigurationValue("InfinityApi.ApiRoot"),

                BffBaseUrl = GetConfigurationValue("InvoiceReceiptsBffBasePath"),
                BlazorHostUrl = GetConfigurationValue("InvoiceReceiptsBlazorHostUrl"),
                CacheExpirationInMinutes = int.Parse(GetConfigurationValue("InvoiceReceiptsCacheExpirationInMinutes", "0")),

                IdentityServerConnectionString = GetConfigurationValue("IdentityServerDatabaseConnection"),
                IdentityServerClientId = GetConfigurationValue("IdentityServer.ClientId"),
                IdentityServerClientSecret = GetConfigurationValue("IdentityServer.ClientSecret"),
                IdentityServerAuthority = GetConfigurationValue("IdentityServer.Authority")
            };
        }

        protected override AppConfigurationModel LoadEnvironmentVariables(IDictionary systemVariables)
        {
            try
            {
                return new()
                {
                    AccessDeniedPath = "/app/inventory",
                    ExecutingAssemblyName = UserAgent,
                    DomainApiClientId = GetEnvironmentVariableValue(systemVariables, "DomainApiClientId"),
                    DomainApiClientSecret = GetEnvironmentVariableValue(systemVariables, "DomainApiClientSecret"),
                    TriquestraApiApiRoot = GetEnvironmentVariableValue(systemVariables, "TriquestraApiApiRoot"),
                    TriquestraApiBaseUrl = GetEnvironmentVariableValue(systemVariables, "TriquestraApiBaseUrl"),
                    InfinityApiApiRoot = GetEnvironmentVariableValue(systemVariables, "InfinityApiApiRoot"),
                    BffBaseUrl = GetEnvironmentVariableValue(systemVariables, "InvoiceReceiptsBffBasePath"),
                    BlazorHostUrl = GetEnvironmentVariableValue(systemVariables, "InvoiceReceiptsBlazorHostUrl"),
                    CacheExpirationInMinutes = int.Parse(GetEnvironmentVariableValue(systemVariables, "InvoiceReceiptsCacheExpirationInMinutes", "0")),

                    IdentityServerConnectionString =
                        GetEnvironmentVariableValue(systemVariables, "IdentityServerDatabaseConnection"),
                    IdentityServerClientId = GetEnvironmentVariableValue(systemVariables, "IdentityServerClientId"),
                    IdentityServerClientSecret =
                        GetEnvironmentVariableValue(systemVariables, "IdentityServerClientSecret"),
                    IdentityServerAuthority = GetEnvironmentVariableValue(systemVariables, "IdentityServerAuthority")
                };
            }
            catch (KeyNotFoundException exception)
            {
                throw new KeyNotFoundException("Need to add variable for CICD: " + exception.Message);
            }
            catch (Exception exception)
            {
                throw new ArgumentException($"LoadEnvironmentVariables failed: {exception.Message}\n\n{exception.StackTrace}");
            }
        }
    }
}