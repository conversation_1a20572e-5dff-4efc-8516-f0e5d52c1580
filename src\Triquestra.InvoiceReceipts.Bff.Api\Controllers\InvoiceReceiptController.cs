﻿using Duende.Bff;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Taxes;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InvoiceReceipts.Bff.Api.Configuration;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;

namespace Triquestra.InvoiceReceipts.Bff.Api.Controllers
{
    [ApiController]
    [DynamicRoute("[controller]")]
    [BffApi]
    [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]
    public class InvoiceReceiptController : ControllerBase
    {
        private readonly ILogger<InvoiceReceiptController> _logger;
        private readonly IInvoiceReceiptBffService _invoiceReceiptBffService;
        private readonly IBffDataService _bffDataService;
        private readonly AppConfigurationModel _configurationModel;
        private readonly AuthenticationService _authenticationService;

        public InvoiceReceiptController(
            ILogger<InvoiceReceiptController> logger,
            IInvoiceReceiptBffService invoiceReceiptBffService,
            IBffDataService bffDataService,
            AppConfigurationModel configurationModel,
            AuthenticationService authenticationService)
        {
            _logger = logger;
            _invoiceReceiptBffService = invoiceReceiptBffService;
            _bffDataService = bffDataService;
            _configurationModel = configurationModel;
            _authenticationService = authenticationService;
        }

        [HttpGet]
        [Route("getsupplier/{supplierCode}")]
        public async Task<BffResultDto<SupplierBffDto>> GetSupplierAsync(string supplierCode)
        {
            try
            {
                return await _invoiceReceiptBffService.GetSupplierAsync(supplierCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get supplier failed.");
                return new() { Data = new(), StatusCode = 500, ErrorMsg = ex.Message };
            }
        }

        [HttpGet]
        [Route("getactivesuppliers")]
        public async Task<BffResultDto<List<SupplierBffDto>>> GetActiveSuppliersAsync()
        {
            try
            {
                return await _invoiceReceiptBffService.GetActiveSuppliersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get active suppliers failed.");
                return new() { Data = new List<SupplierBffDto>(), StatusCode = 500, ErrorMsg = ex.Message };
            }
        }

        [HttpGet]
        [Route("getsites")]
        public async Task<BffResultDto<List<SiteBffDto>>> GetSitesAsync()
        {
            try
            {
                return await _invoiceReceiptBffService.GetSitesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetSites failed.");
                return new() { Data = new List<SiteBffDto>(), StatusCode = 500, ErrorMsg = ex.Message };
            }
        }

        [HttpGet]
        [Route("gettaxes")]
        public BffResultDto<List<TaxBffDto>> GetTaxesAsync()
        {
            try
            {
                return _bffDataService.GetTaxes(_configurationModel.CacheExpirationInMinutes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get taxes failed.");
                return new() { Data = new(), StatusCode = 500, ErrorMsg = ex.Message };
            }
        }

        [HttpPost]
        [Route("createinvoicereceipt")]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status500InternalServerError)]
        public IActionResult CreateInvoiceReceipt(InvoiceReceiptCreateBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { requestBffDto.SiteCode });
                if (!valid)
                    return Unauthorized();

                var response = _invoiceReceiptBffService.CreateInvoiceReceipt(requestBffDto);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Create invoice receipt failed.");
                return Ok(new BffResultDto<StockReceiptResponseBffDto> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpPost]
        [Route("searchinvoicereceipts")]
        [ProducesResponseType<BffResultDto<List<InvoiceReceiptBffDto>>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<List<InvoiceReceiptBffDto>>>(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchInvoiceReceipts(InvoiceReceiptSearchRequestBffDto requestBffDto)
        {
            try
            {
                var result = await _invoiceReceiptBffService.SearchInvoiceReceiptsAsync(requestBffDto);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Search invoice receipts failed.");
                return Ok(new BffResultDto<List<InvoiceReceiptBffDto>> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpGet]
        [Route("getinvoicereceipt/{stockReceiptCode}")]
        [ProducesResponseType<BffResultDto<InvoiceReceiptBffDto>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<InvoiceReceiptBffDto>>(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetInvoiceReceipt(string stockReceiptCode)
        {
            try
            {
                var result = await _invoiceReceiptBffService.GetInvoiceReceiptsAsync(stockReceiptCode);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get invoice receipt failed.");
                return Ok(new BffResultDto<InvoiceReceiptBffDto> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpPut]
        [Route("updateinvoicereceipt")]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateInvoiceReceipt(InvoiceReceiptUpdateBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { requestBffDto.SiteCode });
                if (!valid)
                    return Unauthorized();

                var response = _invoiceReceiptBffService.UpdateInvoiceReceipt(requestBffDto);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update invoice receipt failed.");
                return Ok(new BffResultDto<StockReceiptResponseBffDto> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpPut]
        [Route("updateinvoicereceiptstatus")]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<StockReceiptResponseBffDto>>(StatusCodes.Status500InternalServerError)]
        public IActionResult UpdateInvoiceReceiptStatus(InvoiceReceiptStatusUpdateBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { requestBffDto.SiteCode });
                if (!valid)
                    return Unauthorized();

                var response = _invoiceReceiptBffService.UpdateInvoiceReceiptStatus(requestBffDto);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Update invoice receipt status failed.");
                return Ok(new BffResultDto<StockReceiptResponseBffDto> { Data = new(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }
    }
}