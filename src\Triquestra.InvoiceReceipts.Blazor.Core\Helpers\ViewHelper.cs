﻿using Infinity.InvoiceReceipts.Blazor.Enums;
using Triquesta.InfinityWeb.Common.Constants;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations.Constants;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;

namespace Infinity.InvoiceReceipts.Blazor.Helpers
{
    public static class ViewHelper
    {
        /// <summary>
        /// The InvoiceReceipt API receives a value that differs from the system configuration ("per_qty")
        /// </summary>
        private const string InvoiceReceiptPerQuantityType = "per_quantity";

        private static readonly Dictionary<InvoiceReceiptChangedEventType, string> ChangedEventTypeToCssClassMap = new Dictionary<InvoiceReceiptChangedEventType, string>()
        {
            { InvoiceReceiptChangedEventType.Draft, CommonCssClasses.HighlightSaveAsDraftClass},
            { InvoiceReceiptChangedEventType.Complete, CommonCssClasses.HighlightCompleteClass},
            { InvoiceReceiptChangedEventType.Delete, CommonCssClasses.HighlightDeletedClass},
        };

        public static string GetChangedEventHighlightClass(InvoiceReceiptChangedEventType eventType)
        {
            return ChangedEventTypeToCssClassMap.TryGetValue(eventType, out string? value) ? (value ?? string.Empty) : string.Empty;
        }

        public static FreightDisbursementType ConvertStringToFreightDisbursementType(string freightDisbursement)
        {
            return freightDisbursement switch
            {
                FreightDisbursementValues.PerLine => FreightDisbursementType.PerLine,
                FreightDisbursementValues.PerQuantity => FreightDisbursementType.PerQuantity,
                InvoiceReceiptPerQuantityType => FreightDisbursementType.PerQuantity,
                FreightDisbursementValues.Manual => FreightDisbursementType.Manual,
                FreightDisbursementValues.UserSelect => FreightDisbursementType.UserSelect,
                _ => FreightDisbursementType.None,
            };
        }

        public static string ConvertFreightDisbursementTypeToString(FreightDisbursementType type)
        {
            return type switch
            {
                FreightDisbursementType.PerLine => FreightDisbursementValues.PerLine,
                FreightDisbursementType.PerQuantity => InvoiceReceiptPerQuantityType,
                FreightDisbursementType.Manual => FreightDisbursementValues.Manual,
                _ => FreightDisbursementValues.None
            };
        }
    }
}