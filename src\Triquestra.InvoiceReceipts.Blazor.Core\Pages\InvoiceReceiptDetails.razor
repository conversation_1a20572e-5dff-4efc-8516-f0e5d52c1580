﻿@page "/app/inventory/receipt-invoice/create-invoice/new"
@page "/app/inventory/receipt-invoice/{stockReceiptCode}/draft"

@using Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptDetails
@using Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Desktop
@using Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Mobile
@using Microsoft.AspNetCore.Authorization
@using Triquestra.InvoiceReceipts.Common.Constants

@attribute [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]

<TriquestraMediaQuery Media="@UIControlConstants.MobileScreenMediaQuery" OnChange="((changed) => _isMobile = changed)" />

@if (_isMobile == true)
{
    <InvoiceReceiptDetailsMobile StockReceiptCode="@StockReceiptCode" />
}
else if (_isMobile == false)
{
    <InvoiceReceiptDetailsDesktop StockReceiptCode="@StockReceiptCode" />
}

<style>
    /*** hide the left menu and top bar, require the css code here ****/
    .k-drawer {
        display: none;
    }

    .card-header {
        display: none;
    }
</style>