using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Triquesta.InfinityWeb.Common;
using Triquestra.InvoiceReceipts.Blazor.Launcher;
using Infinity.InvoiceReceipts.Blazor.Configuration;
using Infinity.Blazor.Components;
using Triquestra.InvoiceReceipts.Blazor.Common;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddOptions();
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("cookie", policyBuilder =>
    {
        policyBuilder.AddAuthenticationSchemes("cookie")
            .RequireAuthenticatedUser();
    });
    options.AddInvoiceReceiptPolicies();
});

builder.Services.ConfigurationBffAuthentication(builder.Configuration);

builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddTelerikBlazor();

builder.Services.AddInfinityWebCommon<Infinity.InvoiceReceipts.Blazor.Resources>();
builder.Services.AddBlazorComponents();
builder.Services.AddInvoiceReceiptDependencies();

builder.AddBlazorComponentsHttpClient();
builder.AddInvoiceReceiptHttpClient();

builder.Configuration.SetDefaultCurrentCulture();

var host = builder.Build();

await host.RunAsync();