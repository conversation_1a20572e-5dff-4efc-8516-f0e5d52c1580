using Triquestra.InfinityAPI.Transactions.PurchaseOrders.Models.DTOs;
using Triquestra.InfinityAPI.Transactions.StockReceipt.Models.DTOs;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Service.Mappers
{
    public class PurchaseOrderBffMapper : IPurchaseOrderBffMapper
    {
        public SearchParametersDto ToSearchParametersDto(PurchaseOrderSearchRequestBffDto bffDto)
        {
            return new SearchParametersDto
            {
                DestinationSiteCode = bffDto.DestinationSiteCodes,
                Offset = bffDto.Offset,
                PageSize = bffDto.PageSize,
                ProductCodes = bffDto.ProductCodes,
                PurchaseOrderStatus = bffDto.PurchaseOrderStatus,
                ResponseType = bffDto.ResponseType,
                SiteCode = bffDto.SiteCodes,
                SupplierCodes = bffDto.SupplierCodes,
                PurchaseOrderReference = bffDto.PurchaseOrderReference,
            };
        }

        public PurchaseOrderBffDto ToPurchaseOrderBffDto(PurchaseOrderDto dto)
        {
            return new PurchaseOrderBffDto
            {
                Archived = dto.Archived,
                AuthorisedByPersonCode = dto.AuthorisedByPersonCode,
                BillOfLading = dto.BillOfLading,
                Created = dto.Created,
                DestinationSiteCode = dto.DestinationSiteCode,
                EstimatedDeliveryDate = dto.EstimatedDeliveryDate,
                ExtendedCostPriceExcludingInputTax = dto.ExtendedCostPriceExcludingInputTax,
                ExternalPurchaseOrderCode = dto.ExternalPurchaseOrderCode,
                Lines = dto.Lines?.Select(ToPurchaseOrderLineBffDto).ToList() ?? new(),
                Note = dto.Note,
                NumberOfLines = dto.NumberOfLines,
                PurchaseOrderCode = dto.PurchaseOrderCode,
                PurchaseOrderStatus = dto.PurchaseOrderStatus,
                PurchasingPersonCode = dto.PurchasingPersonCode,
                SalesOrderCode = dto.SalesOrderCode,
                SiteCode = dto.SiteCode,
                SupplierCode = dto.SupplierCode,
                TerminalId = dto.TerminalId,
                TotalInputTax = dto.TotalInputTax,
                Updated = dto.Updated,
            };
        }

        public PurchaseOrderReceiptBffDto ToPurchaseOrderReceiptBffDto(PurchaseOrderReceiptDto dto)
        {
            return new()
            {
                Created = dto.Created,
                CreatedBy = dto.CreatedBy,
                ExternalReference = dto.ExternalReference,
                Lines = dto.Lines?.Select(ToPurchaseOrderReceiptLineBffDto).ToList() ?? new(),
                NumberOfLines = dto.NumberOfLines,
                PurchaseOrderCode = dto.PurchaseOrderCode,
                PurchaseOrderReceiptStatus = dto.PurchaseOrderReceiptStatus,
                SiteCode = dto.SiteCode,
                StockReceiptCode = dto.StockReceiptCode,
                Updated = dto.Updated,
                UpdatedBy = dto.UpdatedBy,
                ReceiptDate = dto.ReceiptDate,
                SupplierReference = dto.SupplierReference,
            };
        }

        private static PurchaseOrderReceiptLineBffDto ToPurchaseOrderReceiptLineBffDto(PurchaseOrderReceiptLineDto dto)
        {
            return new()
            {
                BackOrderQuantity = dto.BackOrderQuantity,
                LineNumber = dto.LineNumber,
                ProductCode = dto.ProductCode,
                ReceiptQuantity = dto.ReceiptQuantity,
                RequestLabel = dto.RequestLabel,
                UnitCostPriceExcludingTax = dto.UnitCostPriceExcludingTax,
            };
        }

        private static PurchaseOrderLineBffDto ToPurchaseOrderLineBffDto(PurchaseOrderLineDto dto)
        {
            return new PurchaseOrderLineBffDto
            {
                ExtendedCostPriceExcludingInputTax = dto.ExtendedCostPriceExcludingInputTax,
                LineNumber = dto.LineNumber,
                LineStatus = dto.LineStatus,
                ProductCode = dto.ProductCode,
                PurchaseQuantity = dto.PurchaseQuantity,
                PurchaseUnit = dto.PurchaseUnit,
                SupplierProductCode = dto.SupplierProductCode,
                TotalInputTax = dto.TotalInputTax,
                UnitCostPriceExcludingInputTax = dto.UnitCostPriceExcludingInputTax,
            };
        }
    }
}