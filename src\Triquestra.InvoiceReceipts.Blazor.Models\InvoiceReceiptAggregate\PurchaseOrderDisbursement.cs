﻿using Triquestra.Base.Common.Domain;

namespace Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate
{
    public class PurchaseOrderDisbursement : IAggregateRoot
    {
        public string PurchaseOrderNumber { get; set; } = string.Empty;

        public decimal? BackOrderQuantity { get; set; }

        public decimal? Quantity { get; set; }

        #region Extra Properties

        public decimal PurchaseQuantity { get; set; }

        #endregion Extra Properties
    }
}