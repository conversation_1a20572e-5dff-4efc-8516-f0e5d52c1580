﻿using Triquestra.InvoiceReceipts.DTOs;

namespace Triquestra.InvoiceReceipts.Bff.Service.ApiWrapper
{
    public class InvoiceReceiptApiWrapper
    {
        public InfinityApi.ApiWrapper.Wrapper ApiWrapper { get; }

        public InvoiceReceiptApiWrapper(AppConfigurationModel config, string userAgent)
        {
            ApiWrapper = new InfinityApi.ApiWrapper.Wrapper(config.TriquestraApiApiRoot, config.TriquestraApiBaseUrl,
                config.DomainApiClientId, config.DomainApiClientSecret, null, null, userAgent);
        }
    }
}