﻿using Infinity.InvoiceReceipts.Blazor.Helpers;
using System.Globalization;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Mobile
{
    public partial class InvoiceReceiptOverviewMobileListView : IDisposable
    {
        protected override Task OnInitializedAsync()
        {
            ViewModel.InvoiceReceiptChangedMessages.CollectionChanged += InvoiceReceiptChangedMessages_CollectionChanged; ;
            return base.OnInitializedAsync();
        }

        private void InvoiceReceiptChangedMessages_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        public async Task LoadDataAsync(string invoiceCode, DateTime startDate, DateTime endDate)
        {
            await ViewModel.LoadDataAsync(invoiceCode, startDate, endDate);
            StateHasChanged();
        }

        private string GetItemHighlightClass(string stockReceiptCode)
        {
            var message = ViewModel.InvoiceReceiptChangedMessages.FirstOrDefault(x => x.Value == stockReceiptCode);
            return message == null ? string.Empty : ViewHelper.GetChangedEventHighlightClass(message.EventType);
        }

        public override void Dispose()
        {
            ViewModel.InvoiceReceiptChangedMessages.CollectionChanged -= InvoiceReceiptChangedMessages_CollectionChanged;
            ViewModel.InvoiceReceiptChangedMessages.Clear();
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}