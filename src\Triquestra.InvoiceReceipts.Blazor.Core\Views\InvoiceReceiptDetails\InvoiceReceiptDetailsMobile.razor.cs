﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Constants;
using Infinity.Blazor.UIControls.QRCodeScanner;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList;
using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductSearchPanel;
using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Desktop;
using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System.Windows.Input;
using Triquesta.InfinityWeb.Common.Constants;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptDetails
{
    public partial class InvoiceReceiptDetailsMobile : IDisposable
    {
        private IEnumerable<ProductClientBffDto> _selectedProducts = new List<ProductClientBffDto>();

        private ProductListMobile _productListMobileRef;
        private ProductSearchPanelMobile? _productSearchPanelRef;
        private InfQRCodeScanner? _qrCodeScannerRef;

        private IRelayCommand<string>? _productDropdownSearchBarcodeCommand;
        private ICommand? _dataChangedCommand;

        private IRelayCommand<object>? _cameraActiveCommand;
        private IRelayCommand<string>? _codeScannedCommand;
        private IRelayCommand<string>? _scanFailedCommand;
        private IRelayCommand<object>? _completeReceiptCommand;

        private bool _isDisbursementDialogVisible;
        private DisbursementDialogMobile _disbursementDialogRef;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IJSRuntime JsRuntime { get; set; }

        [Inject]
        public IUserNotificationService NotificationService { get; set; }

        [Parameter]
        public string StockReceiptCode { get; set; } = string.Empty;

        [Parameter]
        [SupplyParameterFromQuery]
        public string ReturnUrl { get; set; } = string.Empty;

        public InvoiceReceiptDetailsMobile()
        {
            _productDropdownSearchBarcodeCommand = new AsyncRelayCommand<string>(ProductDropdownSearchBarcodeHandlerAsync);
            _dataChangedCommand = new RelayCommand(ProductListDataChangedHandler);

            _cameraActiveCommand = new AsyncRelayCommand<object?>(CameraHandlerAsync);
            _codeScannedCommand = new AsyncRelayCommand<string?>(QRCodeScannedHandlerAsync);
            _scanFailedCommand = new RelayCommand<string?>(ScanFailedHandler);
            _completeReceiptCommand = new AsyncRelayCommand<object?>(CompleteReceiptHandlerAsync);
        }

        protected override async Task OnInitializedAsync()
        {
            ViewModel.ReturnUrl = ReturnUrl;
            ViewModel.IsMobile = true;
            ViewModel.ProductLines.CollectionChanged += ViewModel_CollectionChanged;
            ViewModel.DataChanged = new EventCallback(this, StateHasChanged);
            await ViewModel.InitializeAsync(StockReceiptCode);
            await base.OnInitializedAsync();
        }

        private async Task CompleteReceiptHandlerAsync(object? _)
        {
            if (!(await ViewModel.ValidateFreightDisbursementAsync()))
            {
                return;
            }

            if (ViewModel.ShouldShowPurchaseOrderDisbursement())
            {
                _isDisbursementDialogVisible = true;
                StateHasChanged();

                await _disbursementDialogRef.LoadDataAsync(
                    ViewModel.InvoiceSummaryModel.SelectedSite!.SiteCode!.Value,
                    ViewModel.ProductLines.ToList());
            }
            else
            {
                await ViewModel.ConfirmAndCompleteAsync();
            }
        }

        private void ProductListDataChangedHandler()
        {
            StateHasChanged();
            ViewModel.IsDataChanged = true;

            if (ViewModel.FreightDisbursementType != FreightDisbursementType.Manual)
            {
                ViewModel.CalculateFreights();
            }
        }

        private void OnFreightDisbursementTypeChanged(FreightDisbursementType value)
        {
            ViewModel.OnFreightDisbursementTypeChanged(value); 
            _productListMobileRef.Refresh();
        }

        private void OnIsBackOrdersChanged(bool value)
        {
            _productListMobileRef.ResetState();
            ViewModel.OnIsBackOrdersChanged(value);
        }

        private Task CameraHandlerAsync(object? _)
        {
            return _qrCodeScannerRef!.StartScannerAsync();
        }

        public async Task QRCodeScannedHandlerAsync(string? qrcode)
        {
            if (ViewModel.IsLoading || string.IsNullOrEmpty(qrcode))
                return;

            await Task.Delay(CommonUIConstants.TimeToFocusOnTelerikComponent);
            _productSearchPanelRef.SetSearchText(qrcode);
            StateHasChanged();

            await SearchBarcodeAsync(qrcode);
        }

        public void ScanFailedHandler(string? message)
        {
            if (!string.IsNullOrEmpty(message))
            {
                NotificationService.Error(message, 0);
            }
        }

        private void ViewModel_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        private async Task ProductDropdownSearchBarcodeHandlerAsync(string barcode)
        {
            await _productSearchPanelRef.SetEnabledStatusAsync(false);
            var productCode = await ViewModel.ScanProductAsync(barcode, _productSearchPanelRef!.IsByAnySupplier);
            await _productSearchPanelRef.SetEnabledStatusAsync(true);

            if (!string.IsNullOrEmpty(productCode))
            {
                _productListMobileRef!.ResetState();
                await ScrollToFirstProductItemAsync();
            }

            StateHasChanged();
            await _productSearchPanelRef!.FocusAndSelectAllTextInSearchBoxAsync();
        }

        private async Task SearchBarcodeAsync(string barcode)
        {
            //the barcode will be empty if input barcode and hit enter too fast
            if (!string.IsNullOrEmpty(barcode))
            {
                var productCode = await ViewModel.ScanProductAsync(barcode, _productSearchPanelRef!.IsByAnySupplier);

                if (!string.IsNullOrEmpty(productCode))
                {
                    _productListMobileRef!.ResetState();
                    await ScrollToFirstProductItemAsync();
                }

                await _productSearchPanelRef.FocusAndSelectAllTextInSearchBoxAsync();

                StateHasChanged();
            }
        }

        public override void Dispose()
        {
            ViewModel.Dispose();
            ViewModel.ProductLines.CollectionChanged -= ViewModel_CollectionChanged;
            base.Dispose();
            GC.SuppressFinalize(this);
        }

        private async Task ScrollToFirstProductItemAsync()
        {
            await Task.Delay(CommonUIConstants.TimeToFocusOnTelerikComponent);
            const int yOffsetSupplierDropdown = 100;
            await JsRuntime.InvokeVoidAsync(UIControlJsFunctions.ScrollElementToBottomIfOutOfView, UIConstants.ProductListLabelId, yOffsetSupplierDropdown);
        }
    }
}