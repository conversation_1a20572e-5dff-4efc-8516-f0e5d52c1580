﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.CalendarPickers
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
@using Microsoft.JSInterop;
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Base;

@inherits RazorBaseMvvm<InvoiceReceiptOverviewViewModel>

@inject IStringLocalizer<Resources> Localizer

<div class="receipt-overview-tabs">
    <CardBody Class="receipt-overview-tabs-actions">
        <div class="text-darkest fs-16">@Localizer.GetString(Translations.ReceiptByInvoiceTitle)</div>
        <div class="text-muted fs-14">@Localizer.GetString(Translations.ReceiptByInvoiceDescription)</div>
        <InfButton Enabled="true"
                   FillMode="FillMode.Solid"
                   LabelText="@Localizer.GetString(Translations.CreateNewInvoice)"
                   Class="w-100 mt-3"
                   ClickCommand="ViewModel.CreateNewInvoiceCommand">
            <IconTemplate>
                <AddIcon />
            </IconTemplate>
        </InfButton>
    </CardBody>

    <div class="h6 my-3 text-darkest">@Localizer.GetString(Translations.InvoiceReceiptList)</div>

    <div class="d-flex flex-row w-100">
        <div class="flex-grow-1 me-2">
            <InfTextBox Width="100%" 
                        IconPosition="Infinity.Blazor.UIControls.Icons.IconPosition.Left" 
                        PlaceHolder="@Localizer.GetString(Translations.SearchForInvoiceNumber)"
                        @bind-Value="@_currentInvoiceCode"
                        ChangeCommand="_searchBoxChangedCommand">
                <IconTemplate>
                    <Infinity.Blazor.Components.Icons.SearchIcon />
                </IconTemplate>
            </InfTextBox>
        </div>
        <div>
            @RenderDateRangeFilter()
        </div>
    </div>
    
    <InvoiceReceiptOverviewMobileListView @ref="_gridViewMobileRef" />
</div>

@code {
    private RenderFragment RenderDateRangeFilter()
    {
        return __builder =>
        {
            <InfCalendarPicker @ref="_calendarPickerRef"
                               SetDatesCommand="@_setDatesCommand"
                               DefaultDateRange="@Infinity.Blazor.UIControls.CalendarPickers.TriquestraDateRangeType.ThisMonth"
                               Enabled="_isCalendarEnabled" />
        };
    }
}