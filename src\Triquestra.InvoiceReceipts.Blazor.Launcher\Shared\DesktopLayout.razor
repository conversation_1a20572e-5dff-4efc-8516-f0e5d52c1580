﻿@using Infinity.Blazor.Components.Navigation.View
@using Infinity.Blazor.Components.UserAccountDetails.Views
@using Infinity.Blazor.UIControls.Icons
@using Telerik.Blazor.Components

<TelerikDrawer @ref="MenuDrawer"
                Expanded="@Expanded"
                ExpandedChanged="ExpandedChangedHandler"
                Width="auto" Data="MenuItems" Mode="DrawerMode.Push" Position="DrawerPosition.Start" MiniMode="true">
    <Template Context="menuContext">
        <div class="custom-drawer-items">
            @{
                if (Expanded)
                {
                    <div class="menu-bar">
                        <InfIcon ImageUrl="app-img/infinity-unified-commerce-logo.png" Class="large-image"></InfIcon>
                        <TelerikButton Class="menu-button" FillMode="flat" OnClick="ToggleMenuDrawer">
                            <span class="material-icons icon">
                                arrow_back
                            </span>
                        </TelerikButton>
                    </div>
                }
                else
                {
                    <div class="menu-bar-mini">
                        <TelerikButton Class="menu-button" FillMode="flat" OnClick="ToggleMenuDrawer">
                            <span class="material-icons icon">
                                arrow_forward
                            </span>
                        </TelerikButton>
                        <InfIcon ImageUrl="app-img/infinity-unified-commerce-logo.png" Class="small-image"></InfIcon>
                    </div>
                }
            }
            <NavMenuList Data="menuContext" Expanded="@Expanded" />
        </div>
    </Template>
    <DrawerContent>
        @if (authenticationService.PermissionLoadCompleted)
        {
            <div class="card-header">
                <div class="nav-container">
                    <UserAccountDetailsDesktop />
                </div>
            </div>
            @ChildContent
        }
        else
        {
            <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
        }
    </DrawerContent>
</TelerikDrawer>