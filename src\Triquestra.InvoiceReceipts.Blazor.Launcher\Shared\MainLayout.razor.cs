﻿using Infinity.Blazor.Components.Navigation.Models;
using Infinity.InvoiceReceipts.Blazor;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.InvoiceReceipts.Common.Constants;

namespace Triquestra.InvoiceReceipts.Blazor.Launcher.Shared;

public partial class MainLayout : LayoutComponentBase
{
    // avoid loading components before the screen size is determined, otherwise telerik will throw a js error.
    private bool? _isMobile = null;

    [Inject]
    public IStringLocalizer<Resources> Localizer { get; set; }

    [Inject]
    public IUserNotificationService UserNotificationService { get; set; }

    [Inject]
    public ITriquestraToastNotificationService ToastNotificationService { get; set; }

    /// <summary>
    /// MenuItems should be a variable so that the TelerikPanelBar can highlight the selected item.
    /// </summary>
    private IEnumerable<MenuItemModel> _menuItems = new List<MenuItemModel>();

    private void MediaQueryChangeHandler(bool isMobile)
    {
        _isMobile = isMobile;
        _menuItems = new List<MenuItemModel>
        {
            new()
            {
                Text = Localizer["Menu_InventoryManagement"],
                Icon = "shopping_basket",
                Level = 1,
                Id = 1,
                RequiredUserPermissions = new List<int>(){ InvoiceReceiptPermissions.BO_HO_ACCESS, InvoiceReceiptPermissions.INVENTORY },
                Children = new List<MenuItemModel>
                {
                    new()
                    {
                        Text = Localizer["Menu_Receipt"],
                        Level = 2,
                        Id = 16,
                        RequiredUserPermissions = new(),
                        Children = new List<MenuItemModel>
                        {
                            new()
                            {
                                Text = Localizer["Menu_InvoiceReceipt"],
                                Level = 3,
                                Id = 17,
                                RequiredUserPermissions = new List<int>(),
                            }
                        }
                    }
                }
            }
        };
    }
}