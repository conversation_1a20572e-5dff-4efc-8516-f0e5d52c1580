﻿using Infinity.InvoiceReceipts.Blazor.Utilities;
using Triquesta.InfinityWeb.Common.Base;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.Common
{
    public class ProductLineViewModel : BaseViewModel
    {
        public string Key { get; private set; } = Guid.NewGuid().ToString();

        private int _lineNumber;

        public int LineNumber
        {
            get => _lineNumber;
            set => SetValue(ref _lineNumber, value);
        }

        private string? _productCode;

        public string? ProductCode
        {
            get => _productCode;
            set => SetValue(ref _productCode, value);
        }

        private string? _sku;

        public string? SKU
        {
            get => _sku;
            set => SetValue(ref _sku, value);
        }

        private string? _description;

        public string? Description
        {
            get => _description;
            set => SetValue(ref _description, value);
        }

        private string? _supplierProductCode;

        public string? SupplierProductCode
        {
            get => _supplierProductCode;
            set => SetValue(ref _supplierProductCode, value);
        }

        private decimal _cost;

        public decimal Cost
        {
            get => _cost;
            set
            {
                if (SetValue(ref _cost, value))
                {
                    TotalCost = InvoiceReceiptCalculator.CalculateTotalCost(ReceiptQuantity, Cost);
                }
            }
        }

        private decimal _qtyOnHand;

        public decimal QtyOnHand
        {
            get => _qtyOnHand;
            set => SetValue(ref _qtyOnHand, value);
        }

        private decimal _qtyOnOrder;

        public decimal QtyOnOrder
        {
            get => _qtyOnOrder;
            set => SetValue(ref _qtyOnOrder, value);
        }

        private decimal _targetMarginPercentage;

        public decimal TargetMarginPercentage
        {
            get => _targetMarginPercentage;
            set => SetValue(ref _targetMarginPercentage, value);
        }

        private bool _isFlaggedForUpdatedState;

        /// <summary>
        /// Set this property to true to highlight the newly added or updated item.
        /// </summary>
        public bool IsFlaggedForUpdatedState
        {
            get => _isFlaggedForUpdatedState;
            set => SetValue(ref _isFlaggedForUpdatedState, value);
        }

        private bool _isRequestLabel;

        public bool IsRequestLabel
        {
            get => _isRequestLabel;
            set => SetValue(ref _isRequestLabel, value);
        }

        private string _supplierCode = string.Empty;

        public string SupplierCode
        {
            get => _supplierCode;
            set => SetValue(ref _supplierCode, value);
        }

        private string _supplierName = string.Empty;

        public string SupplierName
        {
            get => _supplierName;
            set => SetValue(ref _supplierName, value);
        }

        private decimal _purchaseQuantity;

        public decimal PurchaseQuantity
        {
            get => _purchaseQuantity;
            set => SetValue(ref _purchaseQuantity, value);
        }

        private decimal _receiptQuantity;

        public decimal ReceiptQuantity
        {
            get => _receiptQuantity;
            set
            {
                if (SetValue(ref _receiptQuantity, value))
                {
                    ReceiptPack = InvoiceReceiptCalculator.CalculateReceiptPack(ReceiptQuantity, PackSize);
                    TotalCost = InvoiceReceiptCalculator.CalculateTotalCost(ReceiptQuantity, Cost);
                }
            }
        }

        private decimal _packSize;

        public decimal PackSize
        {
            get => _packSize;
            set
            {
                if (SetValue(ref _packSize, value))
                {
                    ReceiptPack = InvoiceReceiptCalculator.CalculateReceiptPack(ReceiptQuantity, _packSize);
                }
            }
        }

        private decimal _receiptPack;

        public decimal ReceiptPack
        {
            get => _receiptPack;
            set
            {
                if (SetValue(ref _receiptPack, value))
                {
                    ReceiptQuantity = InvoiceReceiptCalculator.CalculateReceiptQuantity(ReceiptPack, PackSize);
                }
            }
        }

        private decimal? _standardSellingPrice;

        public decimal? StandardSellingPrice
        {
            get => _standardSellingPrice;
            set => SetValue(ref _standardSellingPrice, value);
        }

        private decimal? _freightExtras = 0;

        public decimal? FreightExtras
        {
            get => _freightExtras;
            set => SetValue(ref _freightExtras, value);
        }

        private bool _isChecked;

        public bool IsChecked
        {
            get => _isChecked;
            set => SetValue(ref _isChecked, value);
        }

        private decimal _taxRate;

        public decimal TaxRate
        {
            get => _taxRate;
            set => SetValue(ref _taxRate, value);
        }

        private decimal _totalCost;

        public decimal TotalCost
        {
            get => _totalCost;
            set
            {
                if (SetValue(ref _totalCost, value))
                {
                    Cost = InvoiceReceiptCalculator.CalculateCost(TotalCost, ReceiptQuantity);
                }
            }
        }

        public decimal? CurrentProfitMargin => InvoiceReceiptCalculator.CalculateCurrentProfitMargin(StandardSellingPrice, TaxRate, Cost);


        #region Match To Purchase Order(s) Properties

        private string _purchaseOrderNumber = string.Empty;

        public string PurchaseOrderNumber
        {
            get => _purchaseOrderNumber;
            set => SetValue(ref _purchaseOrderNumber, value);
        }

        private decimal _invoiceQuantity;

        public decimal InvoiceQuantity
        {
            get => _invoiceQuantity;
            set => SetValue(ref _invoiceQuantity, value);
        }

        private decimal _invoiceCost;

        public decimal InvoiceCost
        {
            get => _invoiceCost;
            set => SetValue(ref _invoiceCost, value);
        }

        private decimal _backOrder;

        public decimal BackOrder
        {
            get => _backOrder;
            set => SetValue(ref _backOrder, value);
        }

        #endregion Match To Purchase Order(s) Properties

        public bool IsCorruptedData { get; set; }

        public List<PurchaseOrderDisbursement> PurchaseOrderDisbursements { get; set; } = new();
    }
}
