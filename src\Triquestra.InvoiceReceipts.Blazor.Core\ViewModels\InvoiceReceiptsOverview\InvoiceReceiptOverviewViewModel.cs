﻿using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Enums;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Triquesta.InfinityWeb.Common.Base;
using Triquesta.InfinityWeb.Common.Models;
using Triquesta.InfinityWeb.Common.Services;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
{
    public class InvoiceReceiptOverviewViewModel : BaseViewModel
    {
        private readonly NavigationManager _navigationManager;

        private readonly IStringLocalizer<Resources> _localization;

        private readonly ITriquestraToastNotificationService _toastNotificationService;

        private readonly Queue<TriquestraToastNotificationItem> _notificationsQueue = new();

        public IRelayCommand<object>? CreateNewInvoiceCommand { get; set; }

        public InvoiceReceiptOverviewViewModel(
            NavigationManager navigationManager,
            IStringLocalizer<Resources> localization,
            ITriquestraToastNotificationService toastNotificationService)
        {
            _navigationManager = navigationManager;
            _localization = localization;
            _toastNotificationService = toastNotificationService;

            CreateNewInvoiceCommand = new RelayCommand<object?>(CreateNewInvoice);
            
            RegisterChangedMessages();
        }

        private void CreateNewInvoice(object? _)
        {
            _navigationManager.NavigateTo(InvoiceReceiptNavigationUrls.CreateInvoice);
        }

        public void ShowNotifications()
        {
            while (_notificationsQueue.Count > 0)
            {
                var notification = _notificationsQueue.Dequeue();
                _toastNotificationService.ShowMessage(notification.Message, notification.NotificationType);
            }
        }

        public void ClearNotifications()
        {
            _notificationsQueue.Clear();
        }

        public void RegisterChangedMessages()
        {
            WeakReferenceMessenger.Default.Register<InvoiceReceiptOverviewViewModel, InvoiceReceiptChangedMessage>(this, (viewModel, message) =>
            {
                if (message.EventType == InvoiceReceiptChangedEventType.Draft)
                {
                    _notificationsQueue.Enqueue(TriquestraToastNotificationItem.Success(_localization.GetString(Translations.InvoiceReceiptDraftSaved)));
                }
                else if (message.EventType == InvoiceReceiptChangedEventType.Complete)
                {
                    _notificationsQueue.Enqueue(TriquestraToastNotificationItem.Success(_localization.GetString(Translations.InvoiceReceiptCompleted)));
                }
                else if (message.EventType == InvoiceReceiptChangedEventType.Delete)
                {
                    _notificationsQueue.Enqueue(TriquestraToastNotificationItem.Failure(_localization.GetString(Translations.InvoiceReceiptDeleted)));
                }
            });
        }
    }
}