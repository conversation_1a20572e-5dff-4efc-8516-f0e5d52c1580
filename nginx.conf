events { }
http {
    include mime.types;
     
    server {
        listen 80;
        index index.html;
         
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html =404;
			add_header "Strict-Transport-Security" "max-age=31536000";
			add_header "X-Content-Type-Options" "nosniff";
			add_header "X-XSS-Protection" "1;mode=block";
			add_header "X-Frame-Options" "SAMEORIGIN";
        }
    }
}