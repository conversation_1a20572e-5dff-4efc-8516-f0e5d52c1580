﻿.summary-panel-container {
    margin-top: 80px;
}

    .summary-panel-container ::deep .card-component {
        padding: 0px 16px !important;
        gap: 38px;
        display: flex;
        flex-direction: column;
    }

    .summary-panel-container ::deep .k-card-body {
        display: flex;
        background-color: #FFFFFF;
        padding-top: 24px;
        padding-left: 24px;
        padding-right: 24px;
        padding-bottom: 12px;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
        gap: 24px;
        border-radius: 12px;
        margin-top: 15px;
    }

    .summary-panel-container ::deep .k-card, .k-panelbar.customized-panelbar {
        border: none;
        margin-bottom: 3px;
    }

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

::deep .triquestra-input-text-container textarea {
    height: 100px;
    padding: 15px;
    margin-top: 5px;
}

.page-summary-info {
    border-left-color: #ffffff;
    border-right-color: #ffffff;
    border-bottom-color: #ffffff;
    border-top: 1px;
    border-top-style: solid;
    border-top-color: #DCDCDC;
    padding-top: 15px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

    .triquestra-field-box {
        min-height: 40px;
        gap: 5px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

.expand-cardcontainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .expand-cardcontainer .separator::before,
    .expand-cardcontainer .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

.total-value {
    font-size: 20px;
    font-weight: 500;
}