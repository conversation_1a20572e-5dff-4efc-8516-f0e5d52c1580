using System.Collections.ObjectModel;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Telerik.DataSource.Extensions;
using Triquesta.InfinityWeb.Common.Base;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.Common
{
    public class SelectPurchaseOrderViewModel : BaseViewModel
    {
        private readonly IInvoiceReceiptService _invoiceReceiptService;

        private List<int> _userSiteCodes = new List<int>();

        private bool _isLoading;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetValue(ref _isLoading, value);
        }

        private ObservableCollection<PurchaseOrder> _purchaseOrders = new();

        public ObservableCollection<PurchaseOrder> PurchaseOrders
        {
            get => _purchaseOrders;
            set => SetValue(ref _purchaseOrders, value);
        }

        public SelectPurchaseOrderViewModel(IInvoiceReceiptService invoiceReceiptService)
        {
            _invoiceReceiptService = invoiceReceiptService;
        }

        public async Task InitializeAsync(int siteCode, string supplierCode)
        {
            IsLoading = true;
            
            PurchaseOrders.Clear();

            if (_userSiteCodes.Count == 0)
            {
                var sites = await _invoiceReceiptService.GetSitesAsync();
                _userSiteCodes = sites.Select(x => x.SiteCode!.Value).ToList();
            }

            var siteCodes = new List<int> { InvoiceReceiptConstants.SiteCodeHeadOffice };
            siteCodes.AddRange(_userSiteCodes);

            var purchaseOrders = await _invoiceReceiptService.SearchOpenPurchaseOrdersAsync(new PurchaseOrderSearchRequestBffDto
            {
                SiteCodes = siteCodes,
                DestinationSiteCodes = new() { siteCode, InvoiceReceiptConstants.SiteCodeZero },
                SupplierCodes = new List<string> { supplierCode },
                PageSize = ApiConstants.MaxPageSize,
                Offset = 0,
                ResponseType = ApiConstants.FullResponseType,
            });

            IsLoading = false;

            if (purchaseOrders.Count == 0)
            {
                return;
            }

            PurchaseOrders.AddRange(purchaseOrders.OrderBy(x => x.UpdatedOrCreated));
        }
    }
}