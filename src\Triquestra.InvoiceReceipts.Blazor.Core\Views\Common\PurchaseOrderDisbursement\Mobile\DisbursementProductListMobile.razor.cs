﻿using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile
{
    public partial class DisbursementProductListMobile
    {
        private string? _expandedKey = null;

        public IRelayCommand<string> _selectProductCommand;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IUserNotificationService UserNotificationService { get; set; }

        [Parameter]
        public ObservableCollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }

        [Parameter]
        public ICommand? DataChangedCommand { get; set; }

        [Parameter]
        public int SiteCode { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }

        public DisbursementProductListMobile()
        {
            _selectProductCommand = new RelayCommand<string>(SelectProductChanged);
        }

        private bool IsCardExpand(string key)
        {
            return _expandedKey == key;
        }

        private void SelectProductChanged(string key)
        {
            if (string.IsNullOrEmpty(_expandedKey))
            {
                _expandedKey = key;
            }
            else
            {
                _expandedKey = key == _expandedKey ? null : key;
            }

            StateHasChanged();
        }
    }
}