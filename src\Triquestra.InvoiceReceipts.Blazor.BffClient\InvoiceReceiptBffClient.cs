﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations;
using Triquestra.Common.Bff.DTOs.Bff.Taxes;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Blazor.BffClient
{
    public class InvoiceReceiptBffClient : BaseBffClientService, IInvoiceReceiptBffClient
    {
        private const string JsonMediaType = "application/json";

        private readonly string _baseUrl;

        public InvoiceReceiptBffClient(
            ILogger<InvoiceReceiptBffClient> logger,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration)
            : base(logger, httpClientFactory, InvoiceReceiptClientSettingConstants.BffHttpClientName)
        {
            _baseUrl = configuration[InvoiceReceiptClientSettingConstants.BffBasePath]!;
        }

        public async Task<SystemConfigurationsBffDto> GetSystemConfigurationsAsync(int? siteCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetBaseSystemConfigurationsUrl}";
            if (siteCode.HasValue)
            {
                requestUrl += $"?siteCode={siteCode}";
            }

            var results = await GetDataFromApi<SystemConfigurationsBffDto>(requestUrl);
            return results ?? new();
        }

        public async Task<SupplierBffDto> GetSupplierAsync(string supplierCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetSupplierUrl}/{supplierCode}";
            var results = await GetDataFromApi<SupplierBffDto>(requestUrl);
            return results ?? new();
        }

        public async Task<List<SupplierBffDto>> GetActiveSuppliersAsync()
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetActiveSuppliersUrl}";
            var results = await GetDataFromApi<List<SupplierBffDto>>(requestUrl);
            return results ?? new();
        }

        public async Task<List<SiteBffDto>> GetSitesAsync()
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetSitesUrl}";
            var results = await GetDataFromApi<List<SiteBffDto>>(requestUrl);
            return results ?? new();
        }

        public async Task<List<TaxBffDto>> GetTaxesAsync()
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetTaxesUrl}";
            var results = await GetDataFromApi<List<TaxBffDto>>(requestUrl);
            return results ?? new();
        }

        public async Task<ScannedProductBffDto> ScanProductAsync(string barcode, int? siteCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.ScanProductUrl}?barcode={barcode}";

            if (siteCode.HasValue)
            {
                requestUrl += $"&siteCode={siteCode}";
            }

            var results = await GetDataFromApi<ScannedProductBffDto> (requestUrl);
            return results ?? new();
        }

        public async Task<List<TaxedProductObjectBffDto>> GetProductsAsync(List<string> productCodes, int? siteCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetProductsUrl}?productCodes={string.Join(',', productCodes)}";

            if (siteCode.HasValue)
            {
                requestUrl += $"&siteCode={siteCode}";
            }

            var results = await GetDataFromApi<List<TaxedProductObjectBffDto>>(requestUrl);
            return results ?? new();
        }

        public async Task<List<ProductInventoryBffDto>> GetProductInventoriesAsync(IEnumerable<string> productCodes, int? siteCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetProductInventoriesUrl}?productCodes={string.Join(',', productCodes)}";
            if (siteCode.HasValue)
            {
                requestUrl += $"&siteCode={siteCode}";
            }

            var results = await GetDataFromApi<List<ProductInventoryBffDto>>(requestUrl);
            return results ?? new();
        }

        public async Task<StockReceiptResponseBffDto> CreateInvoiceReceiptAsync(InvoiceReceiptCreateBffDto requestBffDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.CreateInvoiceReceiptUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(requestBffDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PostAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<StockReceiptResponseBffDto>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<List<InvoiceReceiptBffDto>> SearchInvoiceReceiptsAsync(InvoiceReceiptSearchRequestBffDto searchDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.SearchInvoiceReceiptsUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(searchDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PostAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<List<InvoiceReceiptBffDto>>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<InvoiceReceiptBffDto> GetInvoiceReceiptsAsync(string stockReceiptCode)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.GetInvoiceReceiptUrl}/{stockReceiptCode}";

            var results = await GetDataFromApi<InvoiceReceiptBffDto>(requestUrl);
            return results ?? new();
        }

        public async Task<StockReceiptResponseBffDto> UpdateInvoiceReceiptAsync(InvoiceReceiptUpdateBffDto requestBffDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.UpdateInvoiceReceiptUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(requestBffDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PutAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<StockReceiptResponseBffDto>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<StockReceiptResponseBffDto> UpdateInvoiceReceiptStatusAsync(InvoiceReceiptStatusUpdateBffDto requestBffDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.UpdateInvoiceReceiptStatusUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(requestBffDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PutAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<StockReceiptResponseBffDto>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<List<PurchaseOrderBffDto>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto searchDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.SearchPurchaseOrdersForDisbursementUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(searchDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PostAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<List<PurchaseOrderBffDto>>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<List<PurchaseOrderReceiptBffDto>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto searchDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.SearchCompletedPurchaseOrderReceiptsUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(searchDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PostAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<List<PurchaseOrderReceiptBffDto>>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }

        public async Task<List<PurchaseOrderBffDto>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto searchDto)
        {
            var requestUrl = $"{_baseUrl}/{InvoiceReceiptClientSettingConstants.SearchOpenPurchaseOrdersUrl}";
            var itemJson = new StringContent(JsonSerializer.Serialize(searchDto), Encoding.UTF8, JsonMediaType);
            var response = await HttpRequestHandler(() => HttpClient.PostAsync(requestUrl, itemJson));
            if (response == null)
            {
                return new();
            }

            var result = DeserializeResponseHandler<List<PurchaseOrderBffDto>>(await response.Content.ReadAsStringAsync());
            return result?.Data ?? new();
        }
    }
}