﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.CalendarPickers;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Microsoft.AspNetCore.Components;
using System.Windows.Input;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Mobile
{
    public partial class InvoiceReceiptOverviewMobile
    {
        [Inject]
        public NavigationManager NavigationManager { get; set; }

        private InvoiceReceiptOverviewMobileListView _gridViewMobileRef;
        private InfCalendarPicker _calendarPickerRef;

        private InfCalendarDateRanges? _currentDateRanges;
        private string _currentInvoiceCode = string.Empty;
        private string _lastSearchedInvoiceCode = string.Empty;

        private bool _isCalendarEnabled = true;

        private ICommand _setDatesCommand;
        private ICommand _searchBoxChangedCommand;

        public InvoiceReceiptOverviewMobile()
        {
            _setDatesCommand = new AsyncRelayCommand<InfCalendarDateRanges>(SetDateRangesHandler);
            _searchBoxChangedCommand = new AsyncRelayCommand<string>(SearchBoxChangedHandler);
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);

            if (firstRender)
            {
                await Task.Delay(UIConstants.DelayBeforeExecutingPageLoadedAction);
                ViewModel.ShowNotifications();
            }
        }

        private async Task SetDateRangesHandler(InfCalendarDateRanges? dateRanges)
        {
            _currentDateRanges = dateRanges;
            await LoadDataAsync();
        }

        private async Task SearchBoxChangedHandler(string? _)
        {
            if (_currentInvoiceCode != _lastSearchedInvoiceCode)
            {
                _lastSearchedInvoiceCode = _currentInvoiceCode;

                _isCalendarEnabled = string.IsNullOrEmpty(_currentInvoiceCode);

                await LoadDataAsync();
                StateHasChanged();
            }
        }

        private async Task LoadDataAsync()
        {
            await _gridViewMobileRef.LoadDataAsync(_currentInvoiceCode, _currentDateRanges!.RangeStart, _currentDateRanges!.RangeEnd);
        }

        public override void Dispose()
        {
            ViewModel.ClearNotifications();
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}