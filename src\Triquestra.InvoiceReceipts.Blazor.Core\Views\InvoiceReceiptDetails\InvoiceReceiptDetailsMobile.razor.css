﻿.receipt-details-container label {
    font-weight: 500;
    padding: 10px 0;
}

.page-container {
    margin-top: 80px;
}

.page-container ::deep .card-component {
    padding: 0px 16px !important;
    gap: 38px;
    display: flex;
    flex-direction: column;
}

.page-header {
    background-color: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 72px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
}

    .page-header .title {
        align-self: center;
        font-weight: 400;
        font-size: 24px;
        line-height: 28.13px;
        color: #202020;
    }

    .page-header .header-group {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
    }

    .page-header ::deep .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 10px;
        height: 40px;
        margin-right: 2.5rem;
        align-self: center;
    }

    .page-header .buttons ::deep button {
        margin-left: 10px;
        padding: 8px 15px;
    }
/*****/
.page-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding-top: 16px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
    margin-top: 15px;
}

/*******/
.page-container ::deep .custom-tabs .k-splitter,
.page-container ::deep .k-card, .k-panelbar.customized-panelbar {
    border: none;
    margin-bottom: 3px;
}

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

.additional-info-note ::deep .triquestra-input-text-container {
    width: 100% !important;
}

.additional-info-note ::deep textarea {
    height: 120px !important;
    padding: 15px !important;
    margin-top: 5px;
}

.page-summary-info {
    border-left-color: #ffffff;
    border-right-color: #ffffff;
    border-bottom-color: #ffffff;
    border-top: 1px;
    border-top-style: solid;
    border-top-color: #DCDCDC;
    padding-top: 15px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

    .page-summary-info label {
        font-weight: 600;
        font-size: 14px;
    }

    .page-summary-info .triquestra-field-box {
        height: 70px;
        display: flex;
        flex-direction: row;
    }

    .page-summary-info .div-left {
        font-weight: 500;
        font-size: 14px;
        line-height: 18.75px;
        color: #202020;
        width: 40%;
    }

    .page-summary-info .div-right {
        font-weight: 400;
        font-size: 14px;
        line-height: 16.41px;
        margin-bottom: 30px;
        padding-right: 10px;
        width: 90%;
        text-align: right;
    }

    .page-summary-info .note-container {
        height: 140px;
    }

    .page-summary-info textarea {
        background-color: #F9F9F9;
        border-color: #DCDCDC;
        color: #898989;
        height: 120px;
    }

.product-search-wrapper {
    padding: 0 16px;
    margin-top: 30px;
}

@media (max-width: 640px) {
    .page-header {
        justify-content: space-between;
    }

        .page-header .title {
            align-self: center;
            font-weight: 700;
            font-size: 18px;
            line-height: 25px;
            color: #111827;
        }

    .back-button ::deep .triquestra-button {
        width: 24px;
        height: 24px;
    }

    .page-header .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 0;
        height: 40px;
        align-self: center;
        margin-right: 10px;
    }

        .page-header .buttons button {
            margin-left: 0;
        }

    .page-summary-info .triquestra-field-box {
        flex-direction: column;
        height: 85px;
    }

    .page-summary-info .note-container {
        height: 150px;
    }

    .page-summary-info {
        flex-direction: column;
    }

        .page-summary-info .additional-info-left {
            display: flex;
            flex-direction: column;
            width: 100%;
            padding-left: 0px;
        }

        .page-summary-info .div-right {
            width: 100%;
        }

        .page-summary-info .additional-info-right {
            display: flex;
            flex-direction: column;
            width: 100%;
            border-left: none;
            padding-left: 0;
        }

    .mobile-delete-draft-container {
        position: absolute;
        right: -10px;
        top: 0;
    }
}

@media (max-width: 410px) {
    .mobile-delete-draft-container {
        margin-top: 10px;
        position: unset;
    }
}

::deep .triquestra-product-dropdown-container svg {
    z-index: 999;
}

.expand-cardcontainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .expand-cardcontainer .separator::before,
    .expand-cardcontainer .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

.additional-info-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-size: 14px;
    margin-top: 20px;
    margin-bottom: 10px;
    gap: 10px;
}

    .additional-info-content > div {
        display: flex;
        justify-content: space-between;
        min-height: 30px;
    }

.mobile-delete-draft-container {
    position: absolute;
    right: -10px;
    top: 0;
}

@media (max-width: 410px) {
    .mobile-delete-draft-container {
        margin-top: 10px;
        position: unset;
    }
}

::deep .k-card.telerik-blazor.k-card-vertical {
    /* make datepicker popup overlaps its parent */
    overflow: visible;
}

.supplier-details-icon {
    position: relative;
    top: 8px;
    left: 5px;
}

.delete-po-wrapper {
    position: absolute;
    right: -35px;
    top: 10px;
}

.total-cost-card ::deep .k-card-body {
    padding-top: 0;
    gap: 0;
}

.total-cost {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 10px;
    font-weight: 600;
    font-size: 20px;
}

.product-list-header {
    padding-left: 16px;
}

.order-information-header {
    font-size: 16px;
    display: flex;
    justify-content: space-between;
}

    .order-information-header ::deep .delete-draft-button {
        font-weight: 400;
        padding: 0;
    }
