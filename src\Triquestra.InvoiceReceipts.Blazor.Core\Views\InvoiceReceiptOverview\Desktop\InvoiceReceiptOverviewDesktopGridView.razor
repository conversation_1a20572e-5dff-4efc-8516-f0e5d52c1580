﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Grids
@using Infinity.Blazor.UIControls.Grids.Mapping;
@using Infinity.Blazor.UIControls.Icons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
@using Telerik.Blazor.Components
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate

@inherits RazorBaseMvvm<InvoiceReceiptOverviewDataListViewModel>
@inject IStringLocalizer<Resources> Localizer

<div class="receipts-details-grid">
    @if (ViewModel.IsLoadingData)
    {
        <div class="grid-empty-content-wrapper">
            <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
        </div>
    }
    else if (!ViewModel.InvoiceReceipts.Any())
    {
        <div class="grid-empty-content-wrapper">
            <div>
                <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
            </div>
            <label class="text-muted">@Localizer.GetString(Translations.NoInvoiceReceiptsFound)</label>
        </div>
    }
    else 
    {
        <InfGridView Data="@ViewModel.InvoiceReceipts"
                 Class="custom-grid no-scroll "
                 Reorderable="true"
                 Sortable="true"
                 RowHeight="50"
                 EnableLoaderContainer="true"
                 FilterMode="@FilterMode.FilterMenu"
                 PageParameters="@_pageParameters"
                 RowRenderCommand="_rowRenderCommand">
            <ColumnsTemplate>
                <GridColumn Width="260px" Field="@nameof(InvoiceReceipt.InvoiceCode)" Title="@Localizer.GetString(Translations.InvoiceNumber)" Filterable="true" />
                <GridColumn Field="@nameof(InvoiceReceipt.SiteName)" Title="@Localizer.GetString(Translations.Site)" />
                <GridColumn Field="@nameof(InvoiceReceipt.SupplierName)" Title="@Localizer.GetString(Translations.Supplier)" />
                <GridColumn Width="12%" Field="@nameof(InvoiceReceipt.UpdatedOrCreated)" Title="@Localizer.GetString(Translations.CreatedUpdated)" DisplayFormat="@GridDateFormat" Filterable="false" />
                <GridColumn Width="12%" 
                            Field="@nameof(InvoiceReceipt.TotalInvoiceValue)" 
                            Title="@Localizer.GetString(Translations.TotalCost)"
                            DisplayFormat="@UIConstants.DataGridCurrencyDisplayFormat"
                            TextAlign="Telerik.Blazor.ColumnTextAlign.Right" 
                            HeaderClass="text-end"
                            Filterable="false" />
                <GridColumn Width="150px" Field="@nameof(InvoiceReceipt.InvoiceReceiptStatus)" Title="@Localizer.GetString(Translations.Status)" Filterable="false">
                    <Template Context="context">
                        @{
                            var item = context as InvoiceReceipt;
                            <StatusTag Status="@item.InvoiceReceiptStatus" />
                        }
                    </Template>
                </GridColumn>
                <GridColumn Width="70px" Filterable=false Editable=false Field="@nameof(InvoiceReceipt.InvoiceCode)" Title="">
                    <Template Context="context">
                        @{
                            <InfButton FillMode="FillMode.Outline">
                                <IconTemplate>
                                    <ChevronRightIcon Width="16" Height="16" Color="#000000" OnClick="() => ViewModel.NavigateToInvoiceReceipt(context as InvoiceReceipt)" />
                                </IconTemplate>
                            </InfButton>
                        }
                    </Template>
                </GridColumn>
            </ColumnsTemplate>
        </InfGridView>
    }
</div>
