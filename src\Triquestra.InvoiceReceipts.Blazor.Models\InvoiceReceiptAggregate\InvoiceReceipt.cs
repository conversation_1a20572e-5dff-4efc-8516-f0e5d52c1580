﻿using Triquestra.Base.Common.Domain;
using Triquestra.InvoiceReceipts.Common.Helpers;

namespace Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate
{
    public class InvoiceReceipt : IAggregateRoot
    {
        public string StockReceiptCode { get; set; } = string.Empty;

        public int? SiteCode { get; set; }

        public string InvoiceCode { get; set; } = string.Empty;

        public string SupplierCode { get; set; } = string.Empty;

        public DateTime InvoiceDate { get; set; }

        public string FreightDisbursmentType { get; set; } = string.Empty;

        public decimal? TotalInvoiceValue { get; set; }

        public decimal? TotalTax { get; set; }

        public decimal? TotalFreight { get; set; }

        public string CreatedBy { get; set; } = string.Empty;

        public DateTime? Created { get; set; }

        public DateTime? Updated { get; set; }

        public string InvoiceReceiptStatus { get; set; } = string.Empty;

        public string SiteName { get; set; } = string.Empty;

        public string SupplierName { get; set; } = string.Empty;

        public DateTime? UpdatedOrCreated => Updated ?? Created;

        public string Note { get; set; } = string.Empty;

        public string UpdatedBy { get; set; } = string.Empty;

        public List<InvoiceReceiptLine> Lines { get; set; } = new();

        public bool HasReceiptedPurchaseOrder { get; set; }

        public decimal ReceiptTotal => Lines.Sum(x => x.ReceiptCostPriceExcludingInputTax * x.ReceiptQuantity) ?? 0;

        public bool IsMatchedToPurchaseOrders => Lines.TrueForAll(x => x.InvoiceQuantity != 0 || InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x.ProductCode));
    }
}
