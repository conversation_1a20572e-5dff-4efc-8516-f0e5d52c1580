using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Interface
{
    public interface IPurchaseOrderBffService
    {
        Task<BffResultDto<List<PurchaseOrderBffDto>>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto requestBffDto);

        Task<BffResultDto<List<PurchaseOrderReceiptBffDto>>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto requestBffDto);

        Task<BffResultDto<List<PurchaseOrderBffDto>>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto requestBffDto);

        BffResultDto<PurchaseOrderBffDto> GetPurchaseOrder(string purchaseOrderCode);
        Task<List<PurchaseOrderBffDto>> GetPurchaseOrdersAsync(List<string> purchaseOrderCodes);
    }
}