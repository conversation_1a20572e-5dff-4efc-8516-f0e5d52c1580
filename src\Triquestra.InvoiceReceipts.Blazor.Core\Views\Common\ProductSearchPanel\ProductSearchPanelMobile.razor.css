﻿.product-search-panel-container ::deep .expand-cardcontainer .card-component {
    padding: 0;
}

.product-search-panel-container ::deep .expand-cardcontainer .k-card-body {
    border-radius: 12px 12px 0px 0px;
}

.scan-product-container {
    width: 100%;
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .separator::before,
    .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

.product-search-input-label {
    font-weight: 500;
    font-size: 14px;
    line-height: 16.41px;
    color: #202020;
    margin-bottom: 5px;
}

.camera ::deep .k-button.triquestra-button.k-button-flat .k-button-text {
    border: none;
}

.camera ::deep .k-button.triquestra-button.k-button-flat {
    color: transparent !important;
}

.tb-icon-container.with-label .camera .triquestra-button {
    color: transparent;
}

.tb-icon-container.with-label .camera .k-button-text {
    border: none;
}

.camera ::deep .k-button-text svg {
    width: 24px;
    height: 24px;
    margin-left: -5px;
}

.scan-product-container .error_message {
    margin-top: 10px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    color: #B00020;
}