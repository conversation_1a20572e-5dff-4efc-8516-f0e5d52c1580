<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>Triquesta Infinity</title>
    <base href="/" />

    <link href='https://fonts.googleapis.com/css?family=Roboto:100,200,300,400,500,600,700,800' rel='stylesheet' />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@48,400,1,0" />
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined|Material+Symbols+Outlined" rel="stylesheet">

    <link href="_content/Triquesta.InfinityWeb.Common/css/bootstrap/bootstrap.min.css" rel="stylesheet" />

    <link href="_content/Triquesta.InfinityWeb.Common/css/triquestra-theme.css" rel="stylesheet" />
    <link href="_content/Triquesta.InfinityWeb.Common/css/app.css" rel="stylesheet" />

    <link href="_content/Infinity.Blazor.UIControls/css/infinity-blazor-uicontrols.css" rel="stylesheet" />
    <link href="Triquestra.InvoiceReceipts.Blazor.Launcher.styles.css" rel="stylesheet" />

    <script src="https://blazor.cdn.telerik.com/blazor/5.1.1/telerik-blazor.min.js" defer></script>
    <script src="_content/Telerik.UI.for.Blazor/js/telerik-blazor.js"></script>
    <script src="https://unpkg.com/html5-qrcode" integrity="sha384-c9d8RFSL+u3exBOJ4Yp3HUJXS4znl9f+z66d1y54ig+ea249SpqR+w1wyvXz/lk+" crossorigin="anonymous"></script>
</head>

<body>
    <app id="app">
        <div class="k-loader-container k-loader-container-md k-loader-end container-size">
            <div class="k-loader-container-overlay k-overlay-light"></div>
            <div class="k-loader-container-inner">
                <div class="k-loader k-loader-pulsing-2 k-loader-lg k-loader-info">
                    <div class="k-loader-canvas">
                        <span class="k-loader-segment"></span>
                        <span class="k-loader-segment"></span>
                    </div>
                </div>
            </div>
        </div>
    </app>

    <script src="_framework/blazor.webassembly.js"></script>
    <script src="_content/Infinity.Blazor.UIControls/js/infinity-blazor-uicontrols-scripts.js"></script>
    <script>
        window.blazorCulture = {
            get: () => localStorage['BlazorCulture'],
            set: (value) => localStorage['BlazorCulture'] = value
        };
    </script>
    <script>
        window.blazorTimezone = {
            get: () => Intl.DateTimeFormat().resolvedOptions().timeZone
        };
        window.triggerFileDownload = (url) => {
            const anchorElement = document.createElement('a');
            anchorElement.href = url;
            anchorElement.download = '';
            anchorElement.click();
            anchorElement.remove();
        }
    </script>
</body>

</html>
