﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.Components.MobileSwipeableProductContainer

@inject IStringLocalizer<Resources> Localizer
@inject NavigationManager Navigation

<div>
    <SwipeableProductContainer Enabled="false">
        <span class="k-icon k-panelbar-expand k-i-arrow-chevron-down"></span>
        <div class="product-description p-1">
            @DataItem.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)
        </div>
        <div class="d-flex flex-column">
            <label class="receipt-quantity-label">@Localizer.GetString(Translations.Receive)</label>
            <span class="receipt-quantity">@DataItem.ReceiptQuantity.ToString(UIConstants.DecimalDisplayFormat)</span>
        </div>
        <div class="d-flex flex-column">
            <label class="receipt-quantity-label">@Localizer.GetString(Translations.BackOrder)</label>
            <span class="receipt-quantity">@DataItem.BackOrder?.ToString(UIConstants.DecimalDisplayFormat)</span>
        </div>
    </SwipeableProductContainer>
</div>