﻿.text-darkest {
    color: #202020;
}

.receipt-overview-tabs ::deep .k-card-body.receipt-overview-tabs-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 12px;
}

.receipt-overview-tabs ::deep .triquestra-input-text-container .triquestra-input-text {
    width: 100% !important;
}

.receipt-overview-tabs ::deep .triquestra-input-text-container .triquestra-input-text-icon-left svg {
    top: 0;
}

.receipt-overview-tabs ::deep .triquestra-input-text-container .k-input-inner {
    background-color: #fff;
    height: 48px;
}

.receipt-overview-tabs ::deep #triquestra-calendar-selection {
    background: #fff;
    width: 50px;
}

    .receipt-overview-tabs ::deep #triquestra-calendar-container .selected-value {
        display: none;
    }

    .receipt-overview-tabs ::deep #triquestra-calendar-container #triquestra-calendar-popup {
        right: 0 !important;
        left: unset !important;
    }

.receipt-overview-tabs ::deep .border-top {
    border-radius: 0 0 12px 12px;
}

::deep #triquestra-calendar-selection {
    width: 100%;
}

::deep .receipt-details-grid {
    margin-top: 20px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.k-tabstrip-content {
    background-color: transparent;
}