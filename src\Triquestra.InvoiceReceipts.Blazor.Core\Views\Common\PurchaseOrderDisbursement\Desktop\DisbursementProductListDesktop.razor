﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Grids
@using Infinity.Blazor.UIControls.Grids.Mapping
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor
@using Telerik.Blazor.Components
@using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate

<div class="productlist-container">
    <CardContainer>
        <CardBody Class="p-0 align-items-start">
            @if (IsLoading)
            {
                <div class="w-100 text-center p-5">
                    <Infinity.Blazor.UIControls.Loaders.InfLoadingBox Height="300px" />
                </div>
            }
            else if (ProductLines.Count == 0)
            {
                <div class="w-100 text-center p-5 mt-5">
                    <div>
                        <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                    </div>
                    <div class="text-muted">@Localizer.GetString(Translations.NoPurchaseOrdersToDisburse)</div>
                </div>
            }
            else
            {
                <InfGridView @ref="_gridRef"
                             Data="@ProductLines"
                             TItem="PurchaseOrderLine"
                             Class="custom-grid" 
                             Reorderable="true"
                             Sortable="true" 
                             RowHeight="50" 
                             Height="400px"
                             EnableLoaderContainer="true" 
                             FilterMode="@FilterMode.FilterMenu">
                    <ColumnsTemplate>
                        <GridColumn Width="250px" 
                                    Field="@nameof(PurchaseOrderLine.Description)"
                                    Title="@Localizer.GetString(Translations.Product)">
                            <Template Context="context">
                                @{
                                    var item = context as PurchaseOrderLine;
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="product_description">
                                                @item.Description
                                            </div>
                                            <label class="product_code">@item.ProductCode</label>
                                        </div>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="160px" Field="@nameof(PurchaseOrderLine.Sku)" Title="@Localizer.GetString(Translations.SKU)" />
                        <GridColumn Width="160px" Field="@nameof(PurchaseOrderLine.PurchaseOrderCode)" Title="@Localizer.GetString(Translations.PONumber)" />
                        <GridColumn Width="120px" Field="@nameof(PurchaseOrderLine.Logged)" Title="@Localizer.GetString(Translations.Logged)" DisplayFormat="@GridDateFormat" Filterable="false" />
                        <GridColumn Width="120px"
                                    Field="@nameof(PurchaseOrderLine.ReceiptQuantity)"
                                    Title="@Localizer.GetString(Translations.ReceiptQty)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat" />
                        <GridColumn Width="160px" Field="@nameof(PurchaseOrderLine.BackOrder)" Title="@Localizer.GetString(Translations.BackOrder)" Filterable="false">
                            <Template>
                                <span>
                                    @{
                                        var item = context as PurchaseOrderLine;
                                    }
                                    <InfNumericTextBox @bind-Value="item.BackOrder"
                                                       Customizable="true"
                                                       Focusable="true"
                                                       Min="@UIConstants.MinNumericTextBoxValue"
                                                       Max="item.MaxBackOrder"
                                                       Decimals="2"
                                                       Width="100%"
                                                       TextAlign="TextAlign.Center"
                                                       InputMode="NumericKeyboardType.Decimal"
                                                       Format="@UIConstants.DecimalDisplayFormat"/>
                                </span>
                            </Template>
                        </GridColumn>
                    </ColumnsTemplate>
                </InfGridView>
            }
        </CardBody>
    </CardContainer>
</div>