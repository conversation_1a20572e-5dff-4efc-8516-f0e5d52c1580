﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
@using Telerik.Blazor;
@using Telerik.Blazor.Components

@inject IStringLocalizer<Resources> Localizer

<div>
    <InfExpandablePanelBar Expanded="@IsExpanded"
                           ItemClickCommand="_panelBarClickCommand"
                           Class="product-item-wrapper"
                           Key="@ProductLine.ProductCode"
                           HideHeaderWhenExpanded="true"
                           @bind-ExpandedItems="ExpandedItems">
        <HeaderContent>
            <SwipeableProductItemHeader Product="ProductLine" IsValidationVisible="false" />
        </HeaderContent>
        <ChildContent>
            <CardBody>
                <div class="card-item k-d-flex" id="@ElementId">
                    <div class="card-item-description-col k-d-flex-col">
                        @if (IsMatchedToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.PONumber)</label>
                                <div class="card-item-value">@ProductLine.PurchaseOrderNumber</div>
                            </div>
                        }
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ProductCode)</label>
                            <div class="card-item-value">@ProductLine.ProductCode</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ProductName)</label>
                            <span class="text-end">@ProductLine.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)</span>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.SKU)</label>
                            <div class="card-item-value">@ProductLine.SKU</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.SupProductCode)</label>
                            <span class="text-end">@ProductLine.SupplierProductCode</span>
                        </div>

                        <hr class="mt-3 mb-2" />

                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.ReceiptQuantity)</label>
                            <div class="text-end">@ProductLine.ReceiptQuantity.ToString(UIConstants.DecimalDisplayFormat)</div>
                        </div>
                        @if (!IsMatchedToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.ReceiptPack)</label>
                                <div class="text-end">@ProductLine.ReceiptPack.ToString(UIConstants.DecimalDisplayFormat)</div>
                            </div>
                        }
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(IsMatchedToPurchaseOrder ? Translations.ReceiptCost : Translations.Cost)</label>
                            <div class="text-end">@ProductLine.Cost.ToString("C")</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.TotalCost)</label>
                            <div class="text-end">@ProductLine.TotalCost.ToString("C")</div>
                        </div>
                        <div class="card-item-line d-flex justify-content-between">
                            <label class="card-item-label">@Localizer.GetString(Translations.FreightExtra)</label>
                            <div class="text-end">@ProductLine.FreightExtras?.ToString("C")</div>
                        </div>
                        @if (IsMatchedToPurchaseOrder)
                        {
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.InvoiceCost)</label>
                                <div class="text-end">@ProductLine.InvoiceCost.ToString("C")</div>
                            </div>
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.InvoiceQuantity)</label>
                                <div class="text-end">@ProductLine.InvoiceQuantity.ToString(UIConstants.DecimalDisplayFormat)</div>
                            </div>
                            <div class="card-item-line d-flex justify-content-between">
                                <label class="card-item-label">@Localizer.GetString(Translations.BackOrders)</label>
                                <div class="text-end">@ProductLine.BackOrder.ToString(UIConstants.DecimalDisplayFormat)</div>
                            </div>
                        }
                    </div>
                </div>
            </CardBody>
        </ChildContent>
    </InfExpandablePanelBar>
</div>