﻿using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Desktop
{
    public partial class DisbursementDialogDesktop
    {
        private IRelayCommand<object>? _okButtonClickCommand;

        [Parameter]
        public bool Visible { get; set; }

        [Parameter]
        public EventCallback<bool> VisibleChanged { get; set; }

        [Parameter]
        [EditorRequired]
        public IRelayCommand<IList<PurchaseOrderLine>> ConfirmCommand { get; set; }

        public DisbursementDialogDesktop()
        {
            _okButtonClickCommand = new AsyncRelayCommand<object>(OkButtonClickHandlerAsync);
        }

        public Task LoadDataAsync(int siteCode, List<ProductLineViewModel> productLines)
        {
            return ViewModel.LoadDataAsync(siteCode, productLines);
        }

        private async Task OkButtonClickHandlerAsync(object? _)
        {
            if (ConfirmCommand.CanExecute(ViewModel.PurchaseOrderLines))
            {
                ConfirmCommand.Execute(ViewModel.PurchaseOrderLines);
            }

            await CloseDialogAsync();
        }

        private async Task CloseDialogAsync()
        {
            Visible = false;
            if (VisibleChanged.HasDelegate)
            {
                await VisibleChanged.InvokeAsync(Visible);
            }
        }
    }
}
