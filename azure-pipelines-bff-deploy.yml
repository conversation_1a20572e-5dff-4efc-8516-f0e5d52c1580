# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
- none

pool:
  vmImage: ubuntu-latest


steps:
- task: DownloadBuildArtifacts@1
  inputs:
    buildType: 'specific'
    project: 'fa1284e8-11e7-413e-8783-83093061feef'
    pipeline: '120'
    buildVersionToDownload: 'latest'
    downloadType: 'single'
    artifactName: 'manifests'
    downloadPath: '$(Pipeline.Workspace)'

- task: KubernetesManifest@0
  displayName: create secret for deployment
  inputs:
    action: 'createSecret'
    kubernetesServiceConnection: 'Kubernetes'
    secretType: 'dockerRegistry'
    secretName: 'infinitycr7632-auth'
    dockerRegistryEndpoint: 'infinityCR'

- task: KubernetesManifest@0
  displayName: deploy bff
  inputs:
    action: 'deploy'
    kubernetesServiceConnection: 'Kubernetes'
    namespace: 'default'
    manifests: |
      $(Pipeline.Workspace)/manifests/configmap-bff.yml
      $(Pipeline.Workspace)/manifests/deployment-bff.yml
      $(Pipeline.Workspace)/manifests/service-bff.yml
      $(Pipeline.Workspace)/manifests/ingress-bff.yml
