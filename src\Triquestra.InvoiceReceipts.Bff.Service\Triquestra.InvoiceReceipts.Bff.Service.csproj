﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Triquestra.Common.Authorization" Version="1.2.3" />
	<PackageReference Include="Triquestra.InfinityApi.ApiWrapper" Version="********" />
    <PackageReference Include="Triquestra.Common.Bff.DTOs" Version="1.2.48" />
	<PackageReference Include="Triquestra.Common.Bff.Mappers" Version="1.2.25" />
	<PackageReference Include="Triquestra.Common.Bff.Services" Version="1.2.35" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Bff.Interface\Triquestra.InvoiceReceipts.Bff.Interface.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Blazor.Common\Triquestra.InvoiceReceipts.Blazor.Common.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.Common\Triquestra.InvoiceReceipts.Common.csproj" />
    <ProjectReference Include="..\Triquestra.InvoiceReceipts.DTOs\Triquestra.InvoiceReceipts.DTOs.csproj" />
  </ItemGroup>

</Project>
