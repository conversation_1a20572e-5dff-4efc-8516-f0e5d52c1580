# Install ingress controller if we want to enable Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: invoicereceipts-bff-ingress
  annotations:
    kubernetes.io/ingress.class: azure/application-gateway
    appgw.ingress.kubernetes.io/appgw-ssl-certificate: Infinity
    appgw.ingress.kubernetes.io/backend-protocol: http
spec:
  rules:
  - http:
      paths:
      - path: /Triquestra/invoicereceipts_bff/*
        pathType: Prefix
        backend:
          service: 
            name: invoicereceipts-bff
            port: 
              number: 80