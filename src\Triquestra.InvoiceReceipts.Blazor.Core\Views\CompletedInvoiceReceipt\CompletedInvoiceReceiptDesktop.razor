@using Infinity.Blazor.UIControls.Buttons
@using Infinity.InvoiceReceipts.Blazor.ViewModels.CompletedInvoiceReceipt
@using Infinity.InvoiceReceipts.Blazor.Views.Common.Report
@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList
@using Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedReceiptSummaryPanel
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization;
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums

@inherits RazorBaseMvvm<CompletedInvoiceReceiptViewModel>
<div class="page-container">
    <!-- header -->
    <div>
        <div class="page-header">
            <div class="header-group">
                <InfBackButton OnClick="ViewModel.RedirectToPreviousPage" />
                <div class="title">
                    @Localizer.GetString(Translations.InvoiceReceipt)
                </div>
            </div>
            <div class="buttons">
                <PrintReportButton StockReceiptCode="@StockReceiptCode" />
            </div>
        </div>
    </div>

    <CompletedReceiptSummaryPanelDesktop InvoiceReceipt="ViewModel.CurrentInvoiceReceipt" />
</div>

<div class="productlist-wrapper">
    @if (ViewModel.CurrentInvoiceReceipt.IsMatchedToPurchaseOrders)
    {
        <CompletedMatchPurchaseOrderProductListDesktop ProductLines="ViewModel.ProductLines" IsLoading="ViewModel.IsLoading" />
    }
    else
    {
        <CompletedProductListDesktop ProductLines="ViewModel.ProductLines" IsLoading="ViewModel.IsLoading" />
    }
</div>

<InvoiceReceiptReportDialog @bind-Visible="_isReportDialogVisible" StockReceiptCode="@StockReceiptCode" />