﻿.product-description {
    flex: 0 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    min-width: 110px;
    width: calc(100vw - 230px);
    font-size: 14px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #202020;
}

.product-code {
    color: #202020;
}

.receipt-quantity-label {
    font-size: 14px;
    white-space: nowrap;
    color: #898989;
}

.receipt-quantity {
    text-align: right;
}

@media only screen and (max-width: 380px) {
    .product-description {
        min-width: 90px;
    }
}

::deep .swipe-content {
    position: absolute;
    gap: 5px;
}