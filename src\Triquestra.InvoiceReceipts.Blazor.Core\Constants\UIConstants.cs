﻿namespace Infinity.InvoiceReceipts.Blazor.Constants
{
    internal static class UIConstants
    {
        public const int ProductDescriptionMaxLengthMobile = 37;

        public const string DecimalDisplayFormat = "0.##";
        public const string DecimalTwoPlacesDisplayFormat = "0.00";
        public const string DecimalFourPlacesDisplayFormat = "0.0000";
        public const string DataGridDecimalTwoPlacesDisplayFormat = "{0:0.00}";
        public const string DataGridDecimalDisplayFormat = "{0:0.##}";
        public const string DataGridCurrencyDisplayFormat = "{0:C}";

        public const decimal MinNumericTextBoxValue = 0m;
        public const decimal MinReceiptQuantity = 0.01m;

        public const int DelaySendingMessageAfterRedirecting = 1000;
        public const int DelayBeforeExecutingPageLoadedAction = 1000;

        public const string ReportIframeWidth = "850px";

        public const string TriggerFileDownloadFunctionName = "triggerFileDownload";

        public const int RoundingDecimalPlaces = 2;

        public const string ProductListItemUpdateButtonIdPrefix = "UpdateButton_";
        public const string ProductSearchWrapperId = "div-barcode";
        public const int DelayTimeBeforeScrolling = 500;
        public const string ProductListLabelId = "product-list-label";
    }
}