{
  "TimeZone.Country": "en-NZ",

  // configuration for bff authentication using Triquesta.InfinityWeb.Common package
  "BffHostUrl": "https://infinitypdev01.triquestratest.com",
  "BffUri": "/Triquestra/invoicereceipts_bff",
  "InvoiceReceiptsBffHostUrl": "https://infinitypdev01.triquestratest.com",
  "InvoiceReceiptsBffBasePath": "/Triquestra/invoicereceipts_bff",
  "BlazorComponentsBffBaseUrl": "https://infinitypdev01.triquestratest.com",
  "ComponentsBffBasePath": "/Triquestra/blazor_components_bff",
  "InvoiceReceiptReportUrl": "https://infinitypdev01.triquestratest.com/Triquestra/invoicereceiptsreports_api/InvoiceReceiptReport/",
  "IsExpressSearchEnabled": "true"
}