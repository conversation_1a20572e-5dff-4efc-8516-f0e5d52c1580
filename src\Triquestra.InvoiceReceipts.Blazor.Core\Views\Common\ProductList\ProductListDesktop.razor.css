﻿label {
    font-weight: 500;
    font-size: 14px;
    padding: 5px 0;
}

.productlist-container {
    margin-top: -15px;
}

.productlist-container ::deep .card-component {
    padding: 0px;
}

.productlist-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 48px 24px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.product_description {
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    color: #202020;
}

.product_code {
    font-weight: 400;
    font-size: 12px;
    line-height: 14.06px;
    color: #898989;
}

.productlist-container ::deep .buttons {
    display: flex;
    width: 100%;
    flex-direction: row;
    margin-bottom: 20px;
}

.productlist-container ::deep .k-checkbox {
    height: 20px;
    width: 20px;
}

.productlist-container ::deep .buttons img {
    margin-right: 5px;
}

    .productlist-container ::deep .buttons button {
        border-color: #DCDCDC !important;
        margin-right: 15px;
    }

        .productlist-container ::deep .buttons button:nth-child(even) {
            color: #B00020 !important;
        }


.action-column-button ::deep .triquestra-button {
    margin: auto;
    padding: 12px;
}

.action-column-button.delete ::deep .triquestra-button {
    border-color: #B00020 !important;
}

.circle {
    display: inline-block;
    width: 12px;
    height: 12px;
}

    .circle:after {
        content: '';
        display: table;
        width: 12px;
        height: 12px;
        background: #D9D9D9;
        border-radius: 50%;
    }

::deep .productlist-item-blue-color,
::deep .productlist-item-blue-color + .k-detail-row {
    background-color: #E5EFFF !important;
}

::deep .k-table-row.text-red td,
::deep .k-table-row.text-red .product_description,
::deep .k-table-row.text-red .product_code {
    color: red;
}

.productlist-container-actions {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.productlist-container .warning {
    justify-content: flex-end;
    display: flex;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    align-items: start;
}

    .productlist-container .warning .warning-icon {
        width: 24px;
        height: 24px;
        margin-right: 5px;
    }

.productlist-container ::deep .cost-value-box {
    display:block;
    border-radius: 8px;
    background-color: #F9F9F9;
    padding: 0 10px;
    border: 1px solid #DCDCDC;
    height: 48px;
    line-height: 48px;
    text-align: right;
}

.productlist-container ::deep .checkbox-header {
    color: #898989;
}

.productlist-container ::deep .manage-columns-button {
    height: 28px;
    width: 155px;
    text-transform: none;
    color: #000;
    font-weight: 400;
    padding: 6px 12px;
    border-radius: 5px;
    border: 1px solid #dcdcdc;
}

    .productlist-container ::deep .manage-columns-button .triquestra-svg-stroke-icon path {
        stroke: rgba(0, 0, 0, 0.6) !important;
    }

.productlist-container ::deep .triquestra-checkbox-container {
    width: 100%;
}

.productlist-container ::deep .triquestra-gridview {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}