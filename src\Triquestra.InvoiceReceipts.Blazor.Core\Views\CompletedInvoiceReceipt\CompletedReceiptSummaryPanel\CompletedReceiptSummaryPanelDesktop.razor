@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors.RadioGroup
@using Infinity.Blazor.UIControls.Tooltips
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums
@using Triquestra.InvoiceReceipts.Common.Constants

<div class="summary-panel-container">
    <ExpandableCardContainer>
        <TitleContent>
            <div class="position-relative">
                <div>
                    <StatusTag Status="@InvoiceReceiptStatus.Complete" />
                </div>
            </div>
        </TitleContent>
        <DescriptionContent>
            @Localizer.GetString(Translations.CompletedOn)
            <strong>&nbsp;@InvoiceReceipt.UpdatedOrCreated?.ToString("dd MMMM yyyy")</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.CompletedBy)
            <span> - </span>
            <strong>&nbsp;@InvoiceReceipt.UpdatedBy</strong>
            <span class="circle mx-2" />
            @Localizer.GetString(Translations.ReceiptNo)
            <span> - </span>
            <strong>&nbsp;@InvoiceReceipt.StockReceiptCode</strong>
        </DescriptionContent>
        <ExpandChildContent>
            <h5 class="pt-3 border-top">@Localizer.GetString(Translations.ReceiptInformation)</h5>
            <div class="page-summary-info">
                <div class="additional-info-left">
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.Site)
                        </div>
                        <div class="div-right">
                            @InvoiceReceipt.SiteName
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.Supplier)
                        </div>
                        <div class="div-right">
                            @InvoiceReceipt.SupplierName
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.InvoiceNo) 
                        </div>
                        <div class="div-right">
                            @InvoiceReceipt.InvoiceCode
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.Note)
                        </div>
                        <div class="div-right">
                            @InvoiceReceipt.Note
                        </div>
                    </div>
                </div>
                <div class="additional-info-right">
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.FreightExtra)
                        </div>
                        <div class="div-right d-block">
                            @InvoiceReceipt.TotalFreight?.ToString("C")
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.Tax)
                        </div>
                        <div class="div-right d-block">
                            @InvoiceReceipt.TotalTax?.ToString("C")
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.InvoiceTotal)
                        </div>
                        <div class="div-right d-block">
                            @InvoiceReceipt.TotalInvoiceValue?.ToString("C")
                        </div>
                    </div>
                    <div class="triquestra-field-box">
                        <div class="div-left">
                            @Localizer.GetString(Translations.ReceiptTotal)
                        </div>
                        <div class="div-right d-block receipt-total-value">
                            @InvoiceReceipt.ReceiptTotal.ToString("C")
                        </div>
                    </div>
                </div>
            </div>
        </ExpandChildContent>
    </ExpandableCardContainer>
</div>