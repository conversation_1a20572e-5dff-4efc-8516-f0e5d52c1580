﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons;
@using Infinity.Blazor.UIControls.Utillities;

@inject IStringLocalizer<Resources> Localizer

<span>
    <InfButton LabelText="@Localizer.GetString(Translations.PrintReceipt)"
               ButtonType="Infinity.Blazor.UIControls.Buttons.ButtonType.Button"
               FillMode="FillMode.Outline"
               Class="print-receipt-button"
               ClickCommand="_printReportCommand">
        <IconTemplate>
            <PrintIcon />
        </IconTemplate>
    </InfButton>
</span>
