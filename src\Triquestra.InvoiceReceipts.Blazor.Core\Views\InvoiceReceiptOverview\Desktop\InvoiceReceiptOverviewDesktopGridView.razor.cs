﻿using CommunityToolkit.Mvvm.Input;
using Infinity.Blazor.UIControls.Grids.Parameters;
using Infinity.InvoiceReceipts.Blazor.Helpers;
using System.Globalization;
using System.Windows.Input;
using Telerik.Blazor.Components;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.InvoiceReceiptOverview.Desktop
{
    public partial class InvoiceReceiptOverviewDesktopGridView : IDisposable
    {
        private static readonly string GridDateFormat = "{0:" + CultureInfo.DefaultThreadCurrentCulture!.DateTimeFormat.ShortDatePattern + "}";

        private ICommand? _rowRenderCommand;

        private readonly InfGridViewPageParemeters _pageParameters = new()
        {
            PageSize = 50,
            PageSizes = new() { null, 5, 10, 20, 50 }
        };

        public InvoiceReceiptOverviewDesktopGridView()
        {
            _rowRenderCommand = new RelayCommand<GridRowRenderEventArgs>(RowRenderHandler);
        }

        protected override Task OnInitializedAsync()
        {
            ViewModel.InvoiceReceiptChangedMessages.CollectionChanged += InvoiceReceiptChangedMessages_CollectionChanged;
            return base.OnInitializedAsync();
        }

        private void InvoiceReceiptChangedMessages_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            StateHasChanged();
        }

        public async Task<bool> LoadDataAsync(string invoiceCode, DateTime startDate, DateTime endDate)
        {
            await ViewModel.LoadDataAsync(invoiceCode, startDate, endDate);
            return ViewModel.InvoiceReceipts.Count > 0;
        }

        private void RowRenderHandler(GridRowRenderEventArgs? args)
        {
            var invoiceReceipt = args?.Item as InvoiceReceipt;
            args!.Class = GetItemHighlightClass(invoiceReceipt!.StockReceiptCode);
        }

        private string GetItemHighlightClass(string stockReceiptCode)
        {
            var message = ViewModel.InvoiceReceiptChangedMessages.FirstOrDefault(x => x.Value == stockReceiptCode);
            return message == null ? string.Empty : ViewHelper.GetChangedEventHighlightClass(message.EventType);
        }

        public override void Dispose()
        {
            ViewModel.InvoiceReceiptChangedMessages.CollectionChanged -= InvoiceReceiptChangedMessages_CollectionChanged;
            ViewModel.InvoiceReceiptChangedMessages.Clear();
            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}