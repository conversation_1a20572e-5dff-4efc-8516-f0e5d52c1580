﻿using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Blazor.Mappers
{
    public interface IInvoiceReceiptMapper
    {
        SystemConfigurations MapToSystemConfigurations(SystemConfigurationsBffDto bffDto);
        Site MapToSite(SiteBffDto bffDto);
        Supplier MapToSupplier(SupplierBffDto bffDto);
        Product MapToProduct(ScannedProductBffDto bffDto);
        Product MapToProduct(TaxedProductObjectBffDto bffDto);
        ProductInventoryDetail MapToProductInventoryDetail(ProductInventoryBffDto bffDto);
        InvoiceReceipt MapToInvoiceReceipt(InvoiceReceiptBffDto bffDto);
        PurchaseOrder MapToPurchaseOrder(PurchaseOrderBffDto dto);
        PurchaseOrderReceipt MapToPurchaseOrderReceipt(PurchaseOrderReceiptBffDto bffDto);
    }
}