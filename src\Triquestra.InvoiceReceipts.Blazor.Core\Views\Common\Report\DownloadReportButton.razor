﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons;
@using Infinity.Blazor.UIControls.Utillities;

@inject IStringLocalizer<Resources> Localizer

<span>
    <InfButton LabelText="@Localizer.GetString(Translations.Download)"
               ButtonType="Infinity.Blazor.UIControls.Buttons.ButtonType.Button"
               FillMode="FillMode.Outline"
               IconPosition="IconPosition.Left"
               Class="download-report-button w-100"
               ClickCommand="_downloadReportCommand">
        <IconTemplate>
            <PrintIcon />
        </IconTemplate>
    </InfButton>
</span>
