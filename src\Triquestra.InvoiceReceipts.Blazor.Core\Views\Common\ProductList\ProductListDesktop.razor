@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Grids
@using Infinity.Blazor.UIControls.Grids.Mapping
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor
@using Telerik.Blazor.Components
@using Triquestra.InvoiceReceipts.Common.Helpers

<div class="productlist-container">
    <CardContainer>
        <CardBody Class="p-0 align-items-start">
            @if (IsLoading)
            {
                <div class="w-100 text-center p-5">
                    <Infinity.Blazor.UIControls.Loaders.InfLoadingBox Height="200px" />
                </div>
            }
            else if (!ProductLines.Any())
            {
                <div class="w-100 text-center p-5">
                    <div>
                        <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                    </div>
                    <span class="text-muted">@Localizer.GetString(Translations.ProductList_NoData_Message)</span>
                </div>
            }
            else
            {
                @if (!IsMatchToPurchaseOrders)
                {
                    <div class="productlist-actions px-4 pt-4">
                        <div class="buttons">
                            <InfButton Enabled="IsAnyProductChecked"
                                       LabelText="@Localizer.GetString(Translations.DeleteSelectedRows)"
                                       FillMode="FillMode.Outline"
                                       Class="text-transform-none"
                                       ClickCommand="_deleteCommand">
                                <IconTemplate>
                                    <PrefixedIcon IconImageUrl="img/small-delete.png"></PrefixedIcon>
                                </IconTemplate>
                            </InfButton>
                            <InfButton Enabled="@(ProductLines.Count > 0)"
                                       LabelText="@Localizer.GetString(Translations.ClearTable)"
                                       FillMode="FillMode.Outline"
                                       Class="text-transform-none"
                                       ClickCommand="_clearCommand">
                                <IconTemplate>
                                    <PrefixedIcon IconImageUrl="img/delete-x.png"></PrefixedIcon>
                                </IconTemplate>
                            </InfButton>
                        </div>
                    </div>
                }
                <InfGridView @ref="_gridRef"
                             Data="@ProductLines"
                             TItem="ProductLineViewModel"
                             Class="custom-grid no-scroll"
                             Reorderable="true"
                             Sortable="true"
                             RowHeight="50"
                             EnableLoaderContainer="true"
                             FilterMode="@FilterMode.FilterMenu"
                             PageParameters="@_pageParameters"
                             SelectionParameters="@_selectionParameters"
                             RowRenderCommand="@_rowRenderCommand">
                    <ColumnsTemplate>
                        <GridColumn Width="200px" Field="@nameof(ProductLineViewModel.PurchaseOrderNumber)" Title="@Localizer.GetString(Translations.PONumber)" Visible="IsMatchToPurchaseOrders" />
                        <GridColumn Field="@nameof(ProductLineViewModel.Description)"
                                    Title="@Localizer.GetString(Translations.Product)">
                            <Template Context="context">
                                @{
                                    var item = context as ProductLineViewModel;
                                    <div class="w-100">
                                        <div class="product_description text-truncate">
                                            @item.Description
                                        </div>
                                        <label class="product_code">@item.ProductCode</label>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="200px" Field="@nameof(ProductLineViewModel.SKU)" Title="@Localizer.GetString(Translations.SKU)" />
                        <GridColumn Width="200px" Field="@nameof(ProductLineViewModel.SupplierProductCode)" Title="@Localizer.GetString(Translations.SupProductCode)" />
                        <GridColumn Width="180px" Field="@nameof(ProductLineViewModel.InvoiceCost)" Title="@Localizer.GetString(Translations.InvCost)" Visible="IsMatchToPurchaseOrders" Filterable="false">
                            <Template>
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <InfCurrencyTextBox Value="item.InvoiceCost"
                                                            ValueChanged="@(async (value) => await OnInvoiceCostChangedAsync(item, value ?? 0))"
                                                            Format="@UIConstants.DecimalFourPlacesDisplayFormat"
                                                            Enabled="true" />
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="180px" Field="@nameof(ProductLineViewModel.InvoiceQuantity)" Title="@Localizer.GetString(Translations.InvQty)" Visible="IsMatchToPurchaseOrders" Filterable="false">
                            <Template>
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <InfNumericTextBox Value="item.InvoiceQuantity"
                                                           ValueChanged="@(async (value) => await OnInvoiceQuantityChangedAsync(item, value ?? 0))"
                                                           Customizable="true"
                                                           Focusable="true"
                                                           Min="@UIConstants.MinReceiptQuantity"
                                                           Decimals="2"
                                                           Width="100%"
                                                           TextAlign="TextAlign.Center"
                                                           InputMode="NumericKeyboardType.Decimal"
                                                           Enabled="true" />
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="150px" Editable=false Field="@nameof(ProductLineViewModel.QtyOnHand)"
                                    Title="@Localizer.GetString(Translations.OnHandOrder)"
                                    TextAlign="ColumnTextAlign.Right"
                                    HeaderClass="text-end"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat"
                                    Filterable="false">
                            <Template>
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <span>@item.QtyOnHand.ToString(UIConstants.DecimalDisplayFormat) / @item.QtyOnOrder.ToString(UIConstants.DecimalDisplayFormat)</span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="@(IsMatchToPurchaseOrders ? "100px" : "150px")" Editable=false Field="@nameof(ProductLineViewModel.TargetMarginPercentage)"
                                    Title="@Localizer.GetString(Translations.TGPM)"
                                    TextAlign="ColumnTextAlign.Right"
                                    HeaderClass="text-end"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat"
                                    Filterable="false">
                            <Template>
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <span>@item.TargetMarginPercentage.ToString(UIConstants.DecimalDisplayFormat)</span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="@(IsMatchToPurchaseOrders ? "100px" : "150px")" Editable=false Field="@nameof(ProductLineViewModel.CurrentProfitMargin)"
                                    Title="@Localizer.GetString(Translations.CGPM)"
                                    TextAlign="ColumnTextAlign.Right"
                                    HeaderClass="text-end"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat"
                                    Filterable="false">
                            <Template>
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <span>@item.CurrentProfitMargin?.ToString(UIConstants.DecimalDisplayFormat)</span>
                                    }
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="@(IsMatchToPurchaseOrders ? "70px" : "120px")" Field="@nameof(ProductLineViewModel.IsRequestLabel)"
                                    Title="@Localizer.GetString(IsMatchToPurchaseOrders ? Translations.Label : Translations.RequestLabel)"
                                    TextAlign="ColumnTextAlign.Center"
                                    Filterable="false">
                            <Template Context="context">
                                @{
                                    var item = context as ProductLineViewModel;
                                    if (!InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(item.ProductCode))
                                    {
                                        <InfCheckbox Value="item.IsRequestLabel"
                                                     ValueChanged="@(async value => await OnRequestLabelChangedAsync(item, value))"
                                                     TabIndex="-1" />
                                    }
                                }
                            </Template>
                        </GridColumn>
                    </ColumnsTemplate>
                    <ChildrenTemplate>
                        <div class="d-flex justify-content-around">
                            @{
                                var isFreightProduct = context.ProductCode == InvoiceReceiptDataConstants.FreightProductCode;
                                var isSpecialProduct = isFreightProduct || context.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode;
                            }

                            @if (!IsMatchToPurchaseOrders)
                            {
                                <span class="px-3">
                                    <label class="text-muted">@Localizer.GetString(Translations.Delete)</label>
                                    <InfCheckbox @bind-Value="context.IsChecked" />
                                </span>
                            }
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.ReceiptQuantity)</label>
                                <InfNumericTextBox Value="context.ReceiptQuantity"
                                                   ValueChanged="@(async (value) => await OnReceiptQuantityChangedAsync(context, value ?? 0))"
                                                   Customizable="true"
                                                   Focusable="true"
                                                   Min="@UIConstants.MinReceiptQuantity"
                                                   Decimals="2"
                                                   Width="100%"
                                                   TextAlign="TextAlign.Center"
                                                   InputMode="NumericKeyboardType.Decimal"
                                                   Enabled="!isSpecialProduct" />
                            </span>
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.ReceiptPack)</label>
                                <InfNumericTextBox Value="context.ReceiptPack"
                                                   ValueChanged="@(async (value) => await OnReceiptPackChangedAsync(context, value ?? 0))"
                                                   Customizable="true"
                                                   Focusable="true"
                                                   Min="@UIConstants.MinReceiptQuantity"
                                                   Decimals="2"
                                                   Width="100%"
                                                   TextAlign="TextAlign.Center"
                                                   InputMode="NumericKeyboardType.Decimal"
                                                   Enabled="!isSpecialProduct" />
                            </span>
                            @if (IsMatchToPurchaseOrders)
                            {
                                <span class="px-3 pb-3">
                                    <label class="text-muted">@Localizer.GetString(Translations.BackOrder)</label>
                                    <InfNumericTextBox Value="context.BackOrder"
                                                       ValueChanged="@(async (value) => await OnBackOrderChangedAsync(context, value ?? 0))"
                                                       Customizable="true"
                                                       Focusable="true"
                                                       Min="@UIConstants.MinNumericTextBoxValue"
                                                       Decimals="2"
                                                       Width="100%"
                                                       TextAlign="TextAlign.Center"
                                                       InputMode="NumericKeyboardType.Decimal"
                                                       Enabled="!isFreightProduct" />
                                </span>
                            }
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.Cost)</label>
                                <InfCurrencyTextBox Value="context.Cost"
                                                    ValueChanged="@(async (value) => await OnCostChangedAsync(context, value ?? 0))"
                                                    Format="@UIConstants.DecimalFourPlacesDisplayFormat"
                                                    Enabled="!isSpecialProduct" />
                            </span>
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.TotalCost)</label>
                                <InfCurrencyTextBox Value="context.TotalCost"
                                                    ValueChanged="@(async (value) => await OnTotalCostChangedAsync(context, value ?? 0))"
                                                    Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                    Enabled="!isSpecialProduct" />
                            </span>
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.SellPrice)</label>
                                <InfCurrencyTextBox Value="context.StandardSellingPrice"
                                                    ValueChanged="@(async (value) => await OnSellPriceChangedAsync(context, value ?? 0))"
                                                    Min="@UIConstants.MinNumericTextBoxValue"
                                                    Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                    Enabled="@(!isSpecialProduct && IsChangeSellPriceReceipt)" />
                            </span>
                            <span class="px-3 pb-3">
                                <label class="text-muted">@Localizer.GetString(Translations.FreightExtras)</label>
                                <InfCurrencyTextBox Value="@context.FreightExtras"
                                                    ValueChanged="@(async (value) => await OnFreightExtrasChangedAsync(context, value ?? 0))"
                                                    Format="@UIConstants.DecimalTwoPlacesDisplayFormat"
                                                    Enabled="IsFreightEditable" />
                            </span>
                        </div>
                    </ChildrenTemplate>
                </InfGridView>
            }
        </CardBody>
    </CardContainer>
</div>