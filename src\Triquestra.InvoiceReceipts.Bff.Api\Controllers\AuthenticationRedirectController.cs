﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triquestra.InvoiceReceipts.DTOs;

namespace Triquestra.InvoiceReceipts.Bff.Api.Controllers
{
    [ApiController]
    [AllowAnonymous]
    [IgnoreAntiforgeryToken]
    [Route("/Triquestra/invoicereceipts_bff/")]
    public class AuthenticationRedirectController : ControllerBase
    {
        private readonly AppConfigurationModel _configuration;

        public AuthenticationRedirectController(AppConfigurationModel configuration)
        {
            _configuration = configuration;
        }

        [HttpGet]
        [Route("AuthenticationRedirect/login")]
        public IActionResult GetLogin([FromQuery] string returnUrl)
        {
            return Redirect($"{_configuration.BlazorHostUrl}{returnUrl}");
        }

        [HttpGet]
        [Route("AuthenticationRedirect/logout")]
        public IActionResult GetLogout([FromQuery] string returnFullUrl)
        {
            return Redirect(returnFullUrl);
        }
    }
}