﻿@using Infinity.Blazor.UIControls.Layouts
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
@using Telerik.Blazor.Components

<div class="productlist-container-mobile">
    @if (IsLoading)
    {
        <Infinity.Blazor.UIControls.Loaders.InfLoadingBox />
    }
    else if (!ProductLines.Any())
    {
        <CardContainer>
            <CardBody Class="no-data-body">
                <div>
                    <PrefixedIcon IconImageUrl="img/file.png"></PrefixedIcon>
                </div>
                <label class="text-center text-muted">@Localizer.GetString(Translations.ProductList_NoData_Message)</label>
            </CardBody>
        </CardContainer>
    }
    else
    {
        <label class="product-list-label" id="@UIConstants.ProductListLabelId">@Localizer.GetString(Translations.ProductList)</label>

        @foreach (var item in ProductLines)
        {
            <ProductListItemMobile @key="@item.Key"
                                   @ref="_productListItemMobileRefDictionary[item.Key]"
                                   ProductLine="@item"
                                   SiteCode="@SiteCode"
                                   IsExpanded="@IsCardExpand(item.Key!)"
                                   SelectProductCommand="_selectProductCommand"
                                   DeleteCommand="_deleteCommand"
                                   DataChangedCommand="DataChangedCommand"
                                   IsChangeSellPriceReceipt="IsChangeSellPriceReceipt"
                                   IsFreightEditable="IsFreightEditable"
                                   IsMatchToPurchaseOrder="IsMatchToPurchaseOrder"
                                   IsBackOrders="IsBackOrders"
                                   IsSwiped="IsCardSwiped(item.Key!)"
                                   IsSwipedChanged="@(isSwiped => IsSwipedChanged(item.Key!, isSwiped))" />
        }
    }
</div>