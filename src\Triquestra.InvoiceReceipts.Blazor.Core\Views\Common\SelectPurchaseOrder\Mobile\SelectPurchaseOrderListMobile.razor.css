﻿.k-listview-header {
    padding: 20px 28px;
    text-transform: uppercase;
}

.k-card {
    box-shadow: none;
    border-radius: 0.8rem;
    margin-bottom: 1rem;
    border-width: 1px;
    padding: 0.55rem;
}

    .k-card .k-card-body,
    .k-card .k-card-actions {
        padding: 0 8px;
    }

    .k-card .k-card-image {
        max-width: unset;
    }

.k-card-horizontal > .k-card-image:last-child {
    border-radius: 0;
}

.k-card .k-card-title {
    padding: 0;
    font-size: 1.285em;
    font-weight: bold;
    line-height: normal;
}

.k-card-subtitle {
    font-size: 14px;
    font-weight: 500;
    line-height: 22.4px;
    color: #323B49;
    margin: 0;
    padding-top: 0.4rem;
}

.k-card .k-card-description {
    font-weight: 400;
    font-size: 10px;
    color: #979797;
    line-height: 16px;
    display: flex;
    margin-top: -0.15rem;
}

.k-count {
    padding-top: 0.7rem;
    color: #323B49;
    font-size: 18px;
    font-weight: 500;
}

.k-count-item-card ::deep .k-animation-container {
    width: 100%;
}

/***********************/
.productlist-container-mobile {
    text-align: left;
    margin-top: 20px;
}

.productlist-container-mobile ::deep .card-component {
    padding: 0px 16px 16px 16px;
}

.productlist-container-mobile ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding: 0px 20px;
    align-items: center;
    border-radius: 12px;
    flex-direction: column;
    justify-content: center;
}

    .productlist-container-mobile ::deep .k-card-body.no-data-body {
        height: 200px;
    }

    .productlist-container-mobile ::deep .k-card-body .card-item {
        width: 100%;
        margin-bottom: 10px;
    }

.productlist-container-mobile ::deep .k-panelbar {
    display: flex;
    background-color: #FFFFFF;
    margin: 0px 16px 16px 16px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
}

.productlist-container-mobile ::deep .k-panelbar-header {
    width: 100%;
}

.productlist-container-mobile ::deep .card-item-description-col {
    width: 100%;
}

::deep .triquestra-button:hover {
    background-color: #76C3B1 !important;
}

.items-adjustment-title {
    margin: 0px 30px 16px 30px;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #202020;
}

.productlist-container-mobile .custom-header div:first-child {
    min-width: 50vw;
    width: 50vw;
}

::deep .k-panelbar-toggle.k-i-arrow-chevron-down {
    display: none;
}

.k-dialog-wrapper .k-window-wrapper {
    width: 90% !important;
}

    .k-dialog-wrapper .k-window-wrapper .k-stack-layout {
        width: 100% !important;
    }

::deep .product-item-wrapper {
    overflow: hidden;
}

::deep .triquestra-panelbar .k-state-expanded .k-link {
    padding: 10px 20px 0px 20px !important;
}

.product-list-label {
    font-size: 16px;
    font-weight: 500;
    margin-top: 10px;
    padding-left: 16px;
    padding-bottom: 10px;
}
