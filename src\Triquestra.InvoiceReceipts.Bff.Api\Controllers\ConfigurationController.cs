﻿using Duende.Bff;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InvoiceReceipts.Bff.Api.Configuration;
using Triquestra.InvoiceReceipts.Common.Constants;

namespace Triquestra.InvoiceReceipts.Bff.Api.Controllers
{
    [ApiController]
    [DynamicRoute("[controller]")]
    [BffApi]
    [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]
    public class ConfigurationController : ControllerBase
    {
        private readonly ILogger<ConfigurationController> _logger;
        private readonly AuthenticationService _authenticationService;
        private readonly IBffDataService _bffDataService;

        public ConfigurationController(
            ILogger<ConfigurationController> logger,
            AuthenticationService authenticationService,
            IBffDataService bffDataService)
        {
            _logger = logger;
            _authenticationService = authenticationService;
            _bffDataService = bffDataService;
        }

        [HttpGet]
        [Route("getsystemconfigurations")]
        public async Task<IActionResult> GetSystemConfigurationsAsync(int? siteCode)
        {
            try
            {
                if (siteCode.HasValue)
                {
                    var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, new List<int?> { siteCode });
                    if (!valid)
                        return Unauthorized();
                }

                var systemConfigurations = await _bffDataService.GetSystemConfigurationsAsync(siteCode);
                return Ok(systemConfigurations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get system configurations failed");
                return Ok(new BffResultDto<SystemConfigurationsBffDto>() { Data = new(), StatusCode = 500, ErrorMsg = ex.ToString() });
            }
        }
    }
}