@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.Blazor.UIControls.Selectors.RadioGroup
@using Infinity.Blazor.UIControls.Tooltips
@using Infinity.Blazor.UIControls.Tooltips.Enums
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums
@using Triquestra.InvoiceReceipts.Common.Constants

<div class="summary-panel-container">
    <ExpandableCardContainer>
        <TitleContent>
            <div class="position-relative">
                <div class="fw-normal">
                    @if (IsDraft)
                    {
                        <div class="d-flex justify-content-between align-items-center">
                            <StatusTag Status="@InvoiceReceiptStatus.Draft" />

                            <InfButton Enabled="true"
                                       LabelText="@Localizer.GetString(Translations.DeleteDraft)"
                                       FillMode="FillMode.Flat"
                                       Class="delete-draft-button px-0"
                                       ClickCommand="DeleteDraftCommand" />
                        </div>
                    }
                    else
                    {
                        <div class="py-1">
                            @Localizer.GetString(Translations.NewInvoiceReceipt)
                        </div>
                    }
                </div>
            </div>
            <hr class="mt-2 mb-2" />
        </TitleContent>
        <DescriptionContent>
            <div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(IsDraft ? Translations.CreatedOn : Translations.StartedOn) - </span>
                    <span>&nbsp;@InvoiceSummary.InvoiceDate.ToString("dd MMMM yyyy")</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.Site) - </span>
                    <span>&nbsp;@InvoiceSummary.SelectedSite?.Name</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.Supplier) - </span>
                    <span>&nbsp;@InvoiceSummary.SelectedSupplier?.SupplierName</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.InvoiceNo) - </span>
                    <span>&nbsp;@InvoiceSummary.InvoiceNumber</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.FreightExtra) - </span>
                    <span>&nbsp;@InvoiceSummary.FreightExtra?.ToString("C")</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.Tax) - </span>
                    <span>&nbsp;@InvoiceSummary.Tax?.ToString("C")</span>
                </div>
                <div>
                    <span class="text-muted">@Localizer.GetString(Translations.Total) - </span>
                    <span>&nbsp;@InvoiceSummary.TotalAmount.ToString("C")</span>
                </div>
            </div>
        </DescriptionContent>
        <ExpandChildContent>
            <div>
                <hr class="mt-2 mb-3" />
                <div class="triquestra-field-box">
                    <div class="fw-500 my-2">
                        @Localizer.GetString(Translations.FreightExtraDisbursement)
                    </div>
                    <div>
                        <InfRadioGroup TValue="FreightDisbursementType"
                                       TItem="KeyValuePair<FreightDisbursementType, string>"
                                       Value="FreightDisbursementType"
                                       ValueChanged="FreightDisbursementTypeChanged"
                                       Data="FreightExtraDisbursementList"
                                       TextField="Value"
                                       ValueField="Key"
                                       Enabled="IsFreightDisbursementEnabled" />
                    </div>
                </div>
                @if (InvoiceSummary.IsMatchToPurchaseOrders)
                {
                    <div class="triquestra-field-box mt-3">
                        <div class="fw-500">
                            @Localizer.GetString(Translations.RunningTotal)
                        </div>
                        <div class="running-total">
                            <span class="total-value">@RunningTotal.ToString("C")</span>
                        </div>
                    </div>
                    <div class="triquestra-field-box mt-3">
                        <div class="fw-500">
                            @Localizer.GetString(Translations.Variance)
                        </div>
                        <div class="variance-value">
                            @Variance.ToString("C")
                        </div>
                    </div>
                }
                else
                {
                    <div class="mt-3 d-flex justify-content-between">
                        <div class="triquestra-field-box w-50">
                            <div class="fw-500 mb-2">
                                @Localizer.GetString(Translations.RunningTotal)
                            </div>
                            <div class="running-total">
                                <span class="total-value">@RunningTotal.ToString("C")</span>
                                <span class="material-icons icon-md me-2">
                                    arrow_forward
                                </span>
                            </div>
                        </div>
                        <div class="triquestra-field-box" style="width: 55%">
                            <div class="fw-500 mb-2 d-flex justify-content-between text-nowrap">
                                @Localizer.GetString(Translations.AdjustmentRequired)
                                <span class="info-icon-wrapper" title="@Localizer.GetString(Translations.AdjustmentRequired_Note)">
                                    <InfoIcon Width="20" Height="20" />
                                </span>
                                <InfTooltip TargetSelector=".info-icon-wrapper" ShowOn="TooltipShowEvent.Click" Width="90%" />
                            </div>
                            <div class="running-total">
                                <span class="total-value">@AdjustmentRequired.ToString("C")</span>
                                <InfButton LabelText="@Localizer.GetString(HasAdjustmentLine ? Translations.Update : Translations.Insert)"
                                           FillMode="FillMode.Outline"
                                           Enabled="(AdjustmentRequired != AdjustmentProductCost)"
                                           ClickCommand="InsertAdjustmentCommand"
                                           Class="insert-adjustment-button" />
                            </div>
                        </div>
                    </div>
                }
                <div class="triquestra-field-box mt-2">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.ReceiptNote)
                    </div>
                    <div class="mt-2">
                        <InfTextArea Width="100%"
                                     PlaceHolder="@Localizer.GetString(Translations.Note_Placeholder)"
                                     Value="@Note"
                                     ValueChanged="NoteChanged" />
                    </div>
                </div>
                @if (InvoiceSummary.IsMatchToPurchaseOrders)
                {
                    <div class="triquestra-field-box mt-3 backorders-wrapper">
                        <InfCheckbox LabelText="@Localizer.GetString(Translations.BackOrders)" 
                                     Value="IsBackOrders"
                                     ValueChanged="@((bool value) => OnBackOrdersChangedAsync(value))" />
                        <div class="backorders-note">@Localizer.GetString(Translations.BackOrdersNote_Mobile)</div>
                    </div>
                }
            </div>
        </ExpandChildContent>
    </ExpandableCardContainer>
</div>