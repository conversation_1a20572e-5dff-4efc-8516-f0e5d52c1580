using NLog;
using Triquestra.Common.Authorization;
using Triquestra.IdentityServer.BffApi.Authorization;
using Triquestra.InfinityAPI.Common.Logging.AppInsights;
using Triquestra.InvoiceReceipts.Bff.Api.Configuration;
using Triquestra.InvoiceReceipts.Common.Configuration;

var builder = WebApplication.CreateBuilder(args);

builder.AddAppInsightsDependencies();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

var configuration = builder.AddServiceDependencies(LogManager.GetCurrentClassLogger());
builder.ConfigureTriquestraAuthorization(configuration);
builder.Services.AddHealthChecks();

builder.Services.AddAuthorizationCore(options =>
{
    options.AddInvoiceReceiptPolicies();
});

// Auth Data Protection
if (!builder.Environment.IsDevelopment())
    builder.Services.AddAuthenticationDataProtection(configuration.IdentityServerConnectionString);

builder.AddNLogDependencies();
var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.ConfigureTriquestraAuthorization();
app.UseHealthChecks($"{configuration.BffBaseUrl}/health");
app.MapBlazorHub();
await app.RunAsync();
