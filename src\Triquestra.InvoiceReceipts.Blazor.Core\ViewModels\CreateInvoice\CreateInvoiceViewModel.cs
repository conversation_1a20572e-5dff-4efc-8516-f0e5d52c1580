﻿using Infinity.InvoiceReceipts.Blazor.Constants;
using Microsoft.AspNetCore.Components;
using System.Collections.ObjectModel;
using Triquesta.InfinityWeb.Common.Base;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Telerik.DataSource.Extensions;
using Infinity.InvoiceReceipts.Blazor.StateContainers;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptsOverview
{
    public class CreateInvoiceViewModel : BaseViewModel
    {
        private readonly NavigationManager _navigation;
        private readonly IInvoiceReceiptService _invoiceReceiptService;

        private readonly BlazorStateChangedService _stateChangedService;

        private decimal _taxRate;

        #region Data Properties

        private bool _isMobile;

        public bool IsMobile
        {
            get => _isMobile;
            set => SetValue(ref _isMobile, value);
        }

        private int? _selectedSiteCode;

        public int? SelectedSiteCode
        {
            get => _selectedSiteCode;
            set
            {
                if (SetValue(ref _selectedSiteCode, value))
                {
                    SelectedSite = Sites.FirstOrDefault(x => x.SiteCode == value);
                }
            }
        }

        public Site? SelectedSite { get; set; }

        private ObservableCollection<Site> _sites = new();

        public ObservableCollection<Site> Sites
        {
            get => _sites;
            set => SetValue(ref _sites, value);
        }

        private bool _isLoadingSuppliers;

        public bool IsLoadingSuppliers
        {
            get => _isLoadingSuppliers;
            set => SetValue(ref _isLoadingSuppliers, value);
        }

        private string? _selectedSupplierCode = null;

        public string? SelectedSupplierCode
        {
            get => _selectedSupplierCode;
            set
            {
                if (SetValue(ref _selectedSupplierCode, value))
                {
                    SelectedSupplier = Suppliers.FirstOrDefault(x => x.SupplierCode.Equals(value, StringComparison.OrdinalIgnoreCase));
                }
            }
        }

        public Supplier? SelectedSupplier { get; set; }

        private ObservableCollection<Supplier> _suppliers = new();

        public ObservableCollection<Supplier> Suppliers
        {
            get => _suppliers;
            set => SetValue(ref _suppliers, value);
        }

        private string _invoiceNumber = string.Empty;

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetValue(ref _invoiceNumber, value);
        }

        private DateTime _invoiceDate = DateTime.Today;

        public DateTime InvoiceDate
        {
            get => _invoiceDate;
            set => SetValue(ref _invoiceDate, value);
        }

        private decimal? _grossAmount;

        public decimal? GrossAmount
        {
            get => _grossAmount;
            set => SetValue(ref _grossAmount, value);
        }

        private decimal? _inputTaxAmount;

        public decimal? InputTaxAmount
        {
            get => _inputTaxAmount;
            set => SetValue(ref _inputTaxAmount, value);
        }

        private decimal? _calculatedTaxAmount;

        public decimal? CalculatedTaxAmount
        {
            get => _calculatedTaxAmount;
            set => SetValue(ref _calculatedTaxAmount, value);
        }

        private decimal? _freightExtra;

        public decimal? FreightExtra
        {
            get => _freightExtra;
            set => SetValue(ref _freightExtra, value);
        }

        private bool _isMatchToPurchaseOrders;

        public bool IsMatchToPurchaseOrders
        {
            get => _isMatchToPurchaseOrders;
            set => SetValue(ref _isMatchToPurchaseOrders, value);
        }

        #endregion Data Properties

        public bool IsDataValid => SelectedSiteCode.HasValue && SelectedSupplierCode.HasValue() &&
            !string.IsNullOrWhiteSpace(InvoiceNumber) && GrossAmount >= 0;

        public decimal? Tax => InputTaxAmount ?? CalculatedTaxAmount;

        public bool IsForeignCurrencySelectedSupplier => !string.IsNullOrWhiteSpace(SelectedSupplier?.Currency);

        public CreateInvoiceViewModel(
            NavigationManager navigation,
            IInvoiceReceiptService invoiceReceiptService,
            BlazorStateChangedService stateChangedService)
        {
            _navigation = navigation;
            _invoiceReceiptService = invoiceReceiptService;
            _stateChangedService = stateChangedService;
        }

        public async Task LoadAsync()
        {
            await Task.WhenAll(LoadSuppliersAsync(), LoadSitesAsync(), GetTaxRateAsync());
        }

        public void RedirectToOverviewPage()
        {
            _navigation.NavigateTo(InvoiceReceiptNavigationUrls.InvoiceReceiptOverview);
        }

        public void StartInvoiceReceipt(List<PurchaseOrder>? purchaseOrders = null)
        {
            var createInvoiceModel = new InvoiceSummaryModel
            {
                SelectedSite = SelectedSite,
                SelectedSupplier = SelectedSupplier,
                Tax = Tax,
                FreightExtra = FreightExtra,
                InvoiceNumber = InvoiceNumber,
                InvoiceDate = InvoiceDate,
                TotalAmount = GetTotalAmount(),
                IsMatchToPurchaseOrders = IsMatchToPurchaseOrders,
                PurchaseOrders = purchaseOrders ?? new(),
            };
            _stateChangedService.SetValue(createInvoiceModel);

            _navigation.NavigateTo(InvoiceReceiptNavigationUrls.NewInvoiceReceipt);
        }

        private async Task LoadSitesAsync()
        {
            if (!_sites.Any())
            {
                var sites = await _invoiceReceiptService.GetSitesAsync();
                Sites.AddRange(sites);
            }

            if (Sites.Count == 1)
            {
                SelectedSiteCode = Sites[0].SiteCode;
            }
        }

        private async Task LoadSuppliersAsync()
        {
            IsLoadingSuppliers = true;

            var suppliers = await _invoiceReceiptService.GetActiveSuppliersAsync();
            
            Suppliers.AddRange(suppliers);

            IsLoadingSuppliers = false;
        }

        private async Task GetTaxRateAsync()
        {
            _taxRate = await _invoiceReceiptService.GetTaxRateAsync();
        }

        public decimal GetTotalAmount()
        {
            if (GrossAmount == null)
            {
                return 0;
            }

            return Math.Round(InputTaxAmount.HasValue ? TotalFromInputTaxAmount : TotalFromTaxRate, UIConstants.RoundingDecimalPlaces);
        }

        private decimal TotalFromTaxRate => (GrossAmount.GetValueOrDefault(0) / (1 + (_taxRate / 100)));
        private decimal TotalFromInputTaxAmount => GrossAmount.GetValueOrDefault(0) - InputTaxAmount.GetValueOrDefault(0);
    }
}