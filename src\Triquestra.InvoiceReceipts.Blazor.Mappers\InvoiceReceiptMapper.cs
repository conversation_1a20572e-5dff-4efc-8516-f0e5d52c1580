﻿using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.DTOs.Bff.Products;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations;
using Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SiteAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SupplierAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;
using Triquestra.InvoiceReceipts.DTOs.Products;
using Triquestra.Common.Bff.DTOs.Bff.SystemConfigurations.Enums;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderReceiptAggregate;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;

namespace Triquestra.InvoiceReceipts.Blazor.Mappers
{
    public class InvoiceReceiptMapper : IInvoiceReceiptMapper
    {
        public SystemConfigurations MapToSystemConfigurations(SystemConfigurationsBffDto bffDto)
        {
            return new()
            {
                IsChangeSellPriceReceipt = bffDto.IsChangeSellPriceReceipt,
                IsGoodsReceiptPrintLabel = bffDto.IsGoodsReceiptPrintLabel,
                IsInvoiceDisbursementsToPurchaseOrder = bffDto.IsInvoiceDisbursementsToPurchaseOrder,
                FreightDisbursementType = MapToFreightExtraDisbursementType(bffDto.FreightDisbursementMethod),
            };
        }

        public Supplier MapToSupplier(SupplierBffDto bffDto)
        {
            var supplier = new Supplier
            {
                SupplierCode = bffDto.SupplierCode,
                SupplierName = bffDto.SupplierName,
                Archived = bffDto.Archived,
                Contact = bffDto.Contact,
                Created = bffDto.Created,
                Email = bffDto.Email,
                ExternalCode = bffDto.ExternalCode,
                FaxNumber = bffDto.FaxNumber,
                PrimaryPhoneNumber = bffDto.PrimaryPhoneNumber,
                SecondaryPhoneNumber = bffDto.SecondaryPhoneNumber,
                SupplierShortName = bffDto.SupplierShortName,
                TaxNumber = bffDto.TaxNumber,
                Updated = bffDto.Updated,
                WebSite = bffDto.WebSite,
                Currency = bffDto.Currency,
                Notes = bffDto.Notes,
                IsDefaultBackOrderStatus = bffDto.IsDefaultBackOrderStatus,
            };

            if (bffDto.Address != null)
            {
                supplier.Address = MapAddressBffDtoToAddress(bffDto.Address);
            }

            return supplier;
        }

        public Site MapToSite(SiteBffDto bffDto)
        {
            return new Site
            {
                IsHeadOffice = bffDto.IsHeadOffice,
                Name = bffDto.Name,
                SiteCode = bffDto.SiteCode,
                DisallowInboundTransfer = bffDto.DisallowInboundTransfer
            };
        }

        public Product MapToProduct(ScannedProductBffDto bffDto)
        {
            return new Product
            {
                ProductCode = bffDto.ProductCode ?? string.Empty,
                SKU = bffDto.Sku ?? string.Empty,
                Description = bffDto.Description ?? string.Empty,
                Unit = bffDto.Unit,
                SupplierCode = bffDto.Supplier,
            };
        }

        public Product MapToProduct(TaxedProductObjectBffDto bffDto)
        {
            var product = new Product
            {
                ProductCode = bffDto.ProductCode,
                Description = bffDto.Description,
                SKU = bffDto.Sku,
                Cost = bffDto.Cost ?? 0,
                SupplierCode = bffDto.SupplierCode ?? string.Empty,
                SupplierName = bffDto.SupplierName,
                Unit = bffDto.Unit ?? string.Empty,
                SupplierProductCode = bffDto.SupplierProductCode,
                TaxRate = bffDto.TaxRate ?? 0,
                TargetMarginPercentage = bffDto.TargetMarginPercentage,
                StandardSellingPrice = bffDto.StandardSellingPrice,
                HierarchyPosition = bffDto.HierarchyPosition,
            };

            if (bffDto.PurchasingRules != null)
            {
                product.PurchasingRules = MapToProductPurchasingRules(bffDto.PurchasingRules);
            }

            return product;
        }

        public ProductInventoryDetail MapToProductInventoryDetail(ProductInventoryBffDto bffDto)
        {
            var productInventoryDetails = new ProductInventoryDetail
            {
                AllocatedQuantity = bffDto.AllocatedQuantity,
                InboundTransferQuantity = bffDto.InboundTransferQuantity,
                MovingAveragePrice = bffDto.MovingAveragePrice,
                OutboundTransferQuantity = bffDto.OutboundTransferQuantity,
                ProductCode = bffDto.ProductCode,
                PurchaseOrderQuantity = bffDto.PurchaseOrderQuantity,
                QuantityOnHand = bffDto.QuantityOnHand,
                SellableQuantity = bffDto.SellableQuantity,
                SiteCode = bffDto.SiteCode,
            };
            return productInventoryDetails;
        }

        public InvoiceReceipt MapToInvoiceReceipt(InvoiceReceiptBffDto bffDto)
        {
            return new()
            {
                StockReceiptCode = bffDto.StockReceiptCode,
                SiteCode = bffDto.SiteCode,
                InvoiceReceiptStatus = bffDto.InvoiceReceiptStatus,
                Created = bffDto.Created?.ToLocalTime(),
                Updated = bffDto.Updated?.ToLocalTime(),
                CreatedBy = bffDto.CreatedBy,
                FreightDisbursmentType = bffDto.FreightDisbursmentType,
                InvoiceCode = bffDto.InvoiceCode,
                InvoiceDate = bffDto.InvoiceDate.ToLocalTime(),
                SiteName = bffDto.SiteName,
                SupplierCode = bffDto.SupplierCode,
                SupplierName = bffDto.SupplierName,
                TotalFreight = bffDto.TotalFreight,
                TotalInvoiceValue = bffDto.TotalInvoiceValue,
                TotalTax = bffDto.TotalInputTax,
                Note = bffDto.Note,
                UpdatedBy = bffDto.UpdatedBy,
                HasReceiptedPurchaseOrder = bffDto.HasReceiptedPurchaseOrder,
                Lines = bffDto.Lines.Select(MapToInvoiceReceiptLine).ToList(),
            };
        }

        public PurchaseOrder MapToPurchaseOrder(PurchaseOrderBffDto dto)
        {
            return new PurchaseOrder
            {
                Archived = dto.Archived,
                AuthorisedByPersonCode = dto.AuthorisedByPersonCode,
                BillOfLading = dto.BillOfLading,
                Created = dto.Created?.ToLocalTime(),
                DestinationSiteCode = dto.DestinationSiteCode,
                DestinationSiteName = dto.DestinationSiteName,
                EstimatedDeliveryDate = dto.EstimatedDeliveryDate,
                ExtendedCostPriceExcludingInputTax = dto.ExtendedCostPriceExcludingInputTax,
                ExternalPurchaseOrderCode = dto.ExternalPurchaseOrderCode,
                Lines = dto.Lines.Select(line => MapToPurchaseOrderLine(dto.PurchaseOrderCode, dto.UpdatedCreated?.ToLocalTime() ?? DateTime.MinValue, line)).ToList(),
                Note = dto.Note,
                NumberOfLines = (int?)dto.NumberOfLines,
                PurchaseOrderCode = dto.PurchaseOrderCode,
                PurchaseOrderStatus = dto.PurchaseOrderStatus,
                PurchasingPersonCode = dto.PurchasingPersonCode,
                SalesOrderCode = dto.SalesOrderCode,
                SiteCode = dto.SiteCode,
                SupplierCode = dto.SupplierCode,
                SupplierName = dto.SupplierName,
                TerminalId = dto.TerminalId,
                TotalInputTax = dto.TotalInputTax,
                Updated = dto.Updated?.ToLocalTime(),
            };
        }

        public PurchaseOrderReceipt MapToPurchaseOrderReceipt(PurchaseOrderReceiptBffDto bffDto)
        {
            return new()
            {
                Created = bffDto.Created?.ToLocalTime(),
                CreatedBy = bffDto.CreatedBy,
                ExternalReference = bffDto.ExternalReference,
                Lines = bffDto.Lines?.Select(MapToPurchaseOrderReceiptLine).ToList() ?? new(),
                NumberOfLines = bffDto.NumberOfLines,
                PurchaseOrderCode = bffDto.PurchaseOrderCode,
                PurchaseOrderReceiptStatus = bffDto.PurchaseOrderReceiptStatus,
                SiteCode = bffDto.SiteCode,
                StockReceiptCode = bffDto.StockReceiptCode,
                Updated = bffDto.Updated?.ToLocalTime(),
                UpdatedBy = bffDto.UpdatedBy,
                ReceiptDate = bffDto.ReceiptDate?.ToLocalTime(),
                SupplierReference = bffDto.SupplierReference,
            };
        }

        private PurchaseOrderReceiptLine MapToPurchaseOrderReceiptLine(PurchaseOrderReceiptLineBffDto bffDto)
        {
            return new()
            {
                BackOrderQuantity = bffDto.BackOrderQuantity,
                LineNumber = bffDto.LineNumber,
                ProductCode = bffDto.ProductCode,
                ReceiptQuantity = bffDto.ReceiptQuantity,
                RequestLabel = bffDto.RequestLabel,
                Cost = bffDto.UnitCostPriceExcludingTax ?? 0,
            };
        }

        private static PurchaseOrderLine MapToPurchaseOrderLine(string purchaseOrderCode, DateTime logged, PurchaseOrderLineBffDto dto)
        {
            return new PurchaseOrderLine
            {
                PurchaseOrderCode = purchaseOrderCode,
                Logged = logged,
                ExtendedCostPriceExcludingInputTax = dto.ExtendedCostPriceExcludingInputTax,
                LineNumber = dto.LineNumber,
                ProductCode = dto.ProductCode,
                PurchaseQuantity = dto.PurchaseQuantity,
                BackOrder = dto.BackOrder,
                PurchaseUnit = dto.PurchaseUnit,
                SupplierProductCode = dto.SupplierProductCode,
                Cost = dto.UnitCostPriceExcludingInputTax,
                Description = dto.Description,
                Sku = dto.Sku,
            };
        }

        private static Address MapAddressBffDtoToAddress(AddressBffDto bffDto)
        {
            return new Address
            {
                Address1 = bffDto.Address1 ?? string.Empty,
                Address2 = bffDto.Address2 ?? string.Empty,
                Address3 = bffDto.Address3 ?? string.Empty,
                City = bffDto.City ?? string.Empty,
                Country = bffDto.Country ?? string.Empty,
                Postcode = bffDto.Postcode ?? string.Empty,
                State = bffDto.State ?? string.Empty
            };
        }

        private static ProductPurchasingRules MapToProductPurchasingRules(ProductPurchasingRulesBffDto bffDto)
        {
            return new ProductPurchasingRules
            {
                BlockPurchaseOrders = bffDto.BlockPurchaseOrders,
                ManualPurchaseOrdersOnly = bffDto.ManualPurchaseOrdersOnly,
                MaximumQuantity = bffDto.MaximumQuantity,
                MinimumQuantity = bffDto.MinimumQuantity,
                PackSize = bffDto.PackSize,
                PackUnit = bffDto.PackUnit
            };
        }


        private static FreightDisbursementType MapToFreightExtraDisbursementType(FreightDisbursementMethod value)
        {
            return value switch
            {
                FreightDisbursementMethod.PerLine => FreightDisbursementType.PerLine,
                FreightDisbursementMethod.PerQuantity => FreightDisbursementType.PerQuantity,
                FreightDisbursementMethod.Manual => FreightDisbursementType.Manual,
                FreightDisbursementMethod.UserSelect => FreightDisbursementType.UserSelect,
                _ => FreightDisbursementType.None,
            };
        }

        private static InvoiceReceiptLine MapToInvoiceReceiptLine(InvoiceReceiptLineBffDto bffDto)
        {
            const string multiplePurchaseOrdersDisplayText = "Multiple";

            var line = new InvoiceReceiptLine()
            {
                FreightDisbursmentAmount = bffDto.FreightDisbursmentAmount,
                InvoiceCostPriceExcludingInputTax = bffDto.InvoiceCostPriceExcludingInputTax,
                InvoiceQuantity = bffDto.InvoiceQuantity,
                LineNumber = bffDto.LineNumber,
                ProductCode = bffDto.ProductCode,
                ReceiptCostPriceExcludingInputTax = bffDto.ReceiptCostPriceExcludingInputTax,
                ReceiptQuantity = bffDto.ReceiptQuantity,
                RequestLabel = bffDto.RequestLabel,
                UpdateStandardSellingPrice = bffDto.UpdateStandardSellingPrice,
            };

            if (bffDto.PurchaseOrderDisbursements?.Count == 1)
            {
                line.PurchaseOrderDisbursements = new List<PurchaseOrderDisbursement>
                {
                    MapToPurchaseOrderDisbursement(bffDto.PurchaseOrderDisbursements[0])
                };
            }
            else if (bffDto.PurchaseOrderDisbursements?.Count > 1) // handle the bad data case
            {
                line.PurchaseOrderDisbursements = new List<PurchaseOrderDisbursement>
                {
                    new()
                    {
                        PurchaseOrderNumber = multiplePurchaseOrdersDisplayText,
                        BackOrderQuantity = bffDto.PurchaseOrderDisbursements.Sum(x => x.BackOrderQuantity),
                        PurchaseQuantity = bffDto.PurchaseOrderDisbursements.Sum(x => x.PurchaseQuantity),
                    }
                };
                
                line.IsCorruptedData = true;
            }

            return line;
        }

        private static PurchaseOrderDisbursement MapToPurchaseOrderDisbursement(PurchaseOrderDisbursementBffDto bffDto)
        {
            return new()
            {
                BackOrderQuantity = bffDto.BackOrderQuantity,
                PurchaseOrderNumber = bffDto.PurchaseOrderNumber,
                PurchaseQuantity = bffDto.PurchaseQuantity,
                Quantity = bffDto.Quantity,
            };
        }
    }
}