﻿using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList.ProductListItemMobile
{
    public partial class SwipeableProductItemHeader
    {
        [Parameter]
        public bool IsSwiped { get; set; }

        [Parameter]
        public EventCallback<bool> IsSwipedChanged { get; set; }

        [Parameter]
        public ProductLineViewModel Product { get; set; }

        [Parameter]
        public bool IsValidationVisible { get; set; } = true;

        [Parameter]
        public IRelayCommand<object>? DeleteCommand { get; set; }

        private bool IsCurrentProfitMarginValid => Product.CurrentProfitMargin >= Product.TargetMarginPercentage;
    }
}