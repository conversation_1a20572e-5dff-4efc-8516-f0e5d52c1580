﻿using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Triquesta.InfinityWeb.Common.Services;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.ReceiptSummaryPanel
{
    public partial class ReceiptSummaryPanelMobile
    {
        public List<KeyValuePair<FreightDisbursementType, string>> FreightExtraDisbursementList { get; private set; } = new();

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Inject]
        public IUserNotificationService UserNotificationService { get; set; }

        [Parameter]
        [EditorRequired]
        public InvoiceSummaryModel InvoiceSummary { get; set; } = new();

        [Parameter]
        [EditorRequired]
        public bool IsDraft { get; set; }

        [Parameter]
        [EditorRequired]
        public decimal RunningTotal { get; set; }

        [Parameter]
        [EditorRequired]
        public decimal AdjustmentProductCost { get; set; }

        [Parameter]
        [EditorRequired]
        public decimal AdjustmentRequired { get; set; }

        [Parameter]
        [EditorRequired]
        public FreightDisbursementType FreightDisbursementType { get; set; }

        [Parameter]
        [EditorRequired]
        public EventCallback<FreightDisbursementType> FreightDisbursementTypeChanged { get; set; }

        [Parameter]
        [EditorRequired]
        public string Note { get; set; } = string.Empty;

        [Parameter]
        [EditorRequired]
        public EventCallback<string> NoteChanged { get; set; }

        [Parameter]
        [EditorRequired]
        public IRelayCommand<object>? InsertAdjustmentCommand { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsFreightDisbursementEnabled { get; set; }

        [Parameter]
        [EditorRequired]
        public IRelayCommand<object>? DeleteDraftCommand { get; set; }

        [Parameter]
        [EditorRequired]
        public decimal Variance { get; set; }

        [Parameter]
        [EditorRequired]
        public bool IsBackOrders { get; set; }

        [Parameter]
        [EditorRequired]
        public EventCallback<bool> IsBackOrdersChanged { get; set; }

        [Parameter]
        [EditorRequired]
        public bool HasAdjustmentLine { get; set; }

        protected override Task OnInitializedAsync()
        {
            FreightExtraDisbursementList = new List<KeyValuePair<FreightDisbursementType, string>>
            {
                new KeyValuePair<FreightDisbursementType, string>(FreightDisbursementType.None, Localizer.GetString(Translations.FreightExtraDisbursement_None)),
                new KeyValuePair<FreightDisbursementType, string>(FreightDisbursementType.PerLine, Localizer.GetString(Translations.FreightExtraDisbursement_PerLine)),
                new KeyValuePair<FreightDisbursementType, string>(FreightDisbursementType.PerQuantity, Localizer.GetString(Translations.FreightExtraDisbursement_PerQty)),
                new KeyValuePair<FreightDisbursementType, string>(FreightDisbursementType.Manual, Localizer.GetString(Translations.FreightExtraDisbursement_Manual)),
            };
            return base.OnInitializedAsync();
        }

        private async Task OnBackOrdersChangedAsync(bool value)
        {
            var isConfirmed = await UserNotificationService.ConfirmAsync(
                Localizer.GetString(Translations.BackOrdersConfirmation_Content),
                Localizer.GetString(Translations.BackOrdersConfirmation),
                Localizer.GetString(Translations.YesChange));

            if (isConfirmed)
            {
                IsBackOrders = value;
                if (IsBackOrdersChanged.HasDelegate)
                {
                    await IsBackOrdersChanged.InvokeAsync(IsBackOrders);
                }
            }
            else
            {
                await ResetIsBackOrdersAsync();
            }
        }

        private async Task ResetIsBackOrdersAsync()
        {
            var oldValue = IsBackOrders;
            IsBackOrders = !IsBackOrders;
            StateHasChanged();

            const int DelayBeforeRevertingValue = 100;
            await Task.Delay(DelayBeforeRevertingValue);

            IsBackOrders = oldValue;
        }
    }
}