using CommunityToolkit.Mvvm.Input;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Infinity.InvoiceReceipts.Blazor.Mappers;
using Infinity.InvoiceReceipts.Blazor.StateContainers;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;
using Telerik.DataSource.Extensions;
using Triquesta.InfinityWeb.Common.Base;
using Triquesta.InfinityWeb.Common.Constants;
using Triquesta.InfinityWeb.Common.Services;
using Triquesta.InfinityWeb.Common.ViewModels;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate;
using Triquestra.InvoiceReceipts.Blazor.Services;
using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums;
using CommunityToolkit.Mvvm.Messaging;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.Common.Constants;
using Infinity.InvoiceReceipts.Blazor.Messages;
using Infinity.InvoiceReceipts.Blazor.Enums;
using Triquestra.Common.Authorization.Authentication;
using Infinity.InvoiceReceipts.Blazor.Utilities;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceReceiptAggregate;
using Infinity.InvoiceReceipts.Blazor.Helpers;
using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;
using Triquestra.InvoiceReceipts.Common.Helpers;

namespace Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptDetails
{
    public sealed class InvoiceReceiptDetailsViewModel : BaseViewModel, IDisposable
    {
        private readonly NavigationManager _navigationManager;

        private readonly IInvoiceReceiptService _invoiceReceiptService;

        private readonly BlazorStateChangedService _stateChangedService;

        private readonly IInvoiceReceiptModelMapper _modelMapper;

        private readonly IStringLocalizer<Resources> _stringLocalizer;

        private readonly IUserNotificationService _userNotificationService;

        private readonly AuthenticationService _authenticationService;

        #region Properties

        public EventCallback DataChanged { get; set; }

        public IRelayCommand<object>? InsertAdjustmentCommand { get; }

        public IRelayCommand<object>? SaveDraftCommand { get; }

        public IRelayCommand<object>? UpdateDraftCommand { get; }

        public IRelayCommand<object>? DeleteDraftCommand { get; }

        public IRelayCommand<IList<PurchaseOrderLine>> DisbursementConfirnAndCompleteCommand { get; }

        public InvoiceReceipt? CurrentInvoiceReceipt { get; private set; }

        private string _currentStockReceiptCode = string.Empty;

        public string CurrentStockReceiptCode
        {
            get => _currentStockReceiptCode;
            set => SetValue(ref _currentStockReceiptCode, value);
        }

        private ObservableCollection<ProductLineViewModel> _productLines = new();

        public ObservableCollection<ProductLineViewModel> ProductLines
        {
            get => _productLines;
            set => SetValue(ref _productLines, value);
        }

        private bool _isDataChanged;

        public bool IsDataChanged
        {
            get => _isDataChanged;
            set => SetValue(ref _isDataChanged, value);
        }

        private bool _isLoading;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetValue(ref _isLoading, value);
        }

        private bool _isLoadingVisible;

        public bool IsLoadingVisible
        {
            get => _isLoadingVisible;
            set => SetValue(ref _isLoadingVisible, value);
        }

        private bool _isDataListLoading;

        public bool IsDataListLoading
        {
            get => _isDataListLoading;
            set => SetValue(ref _isDataListLoading, value);
        }

        private bool _isProductNotFound;

        public bool IsProductNotFound
        {
            get => _isProductNotFound;
            set => SetValue(ref _isProductNotFound, value);
        }

        private SystemConfigurations _systemConfigurations = new();

        public SystemConfigurations SystemConfigurations
        {
            get => _systemConfigurations;
            private set => SetValue(ref _systemConfigurations, value);
        }

        private FreightDisbursementType _freightDisbursementType = FreightDisbursementType.None;

        public FreightDisbursementType FreightDisbursementType
        {
            get => _freightDisbursementType;
            private set => SetValue(ref _freightDisbursementType, value);
        }

        private string _note = string.Empty;

        public string Note
        {
            get => _note;
            set => SetValue(ref _note, value);
        }

        private InvoiceSummaryModel _invoiceSummaryModel = new();

        public InvoiceSummaryModel InvoiceSummaryModel
        {
            get => _invoiceSummaryModel;
            private set => SetValue(ref _invoiceSummaryModel, value);
        }

        private bool _isMobile;

        public bool IsMobile
        {
            get => _isMobile;
            set => SetValue(ref _isMobile, value);
        }

        private bool _isBackOrders;

        public bool IsBackOrders
        {
            get => _isBackOrders;
            set => SetValue(ref _isBackOrders, value);
        }

        private bool _isPurchaseOrderDisbursements;

        public bool IsPurchaseOrderDisbursements
        {
            get => _isPurchaseOrderDisbursements;
            set => SetValue(ref _isPurchaseOrderDisbursements, value);
        }

        private bool _isDataLoaded;

        public bool IsDataLoaded
        {
            get => _isDataLoaded;
            set => SetValue(ref _isDataLoaded, value);
        }

        public string ReturnUrl { get; set; } = string.Empty;

        #endregion Properties

        public decimal AdjustmentProductCost => ProductLines.FirstOrDefault(x => x.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode)?.Cost ?? 0;

        public bool IsDraft => !string.IsNullOrEmpty(CurrentStockReceiptCode);

        private bool HasFreighProductLine => ProductLines.Any(x => x.ProductCode == InvoiceReceiptDataConstants.FreightProductCode);

        public bool HasAdjustmentLine => ProductLines.Any(x => x.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode);

        public decimal Variance => InvoiceSummaryModel.TotalAmount - ProductLines.Sum(x => x.InvoiceQuantity * x.InvoiceCost) - InvoiceSummaryModel.FreightExtra.GetValueOrDefault(0);

        private bool IsCorruptedData => ProductLines.Any(x => x.IsCorruptedData);

        public InvoiceReceiptDetailsViewModel(
            NavigationManager navigationManager,
            IInvoiceReceiptService invoiceReceiptService,
            BlazorStateChangedService stateChangedService,
            IInvoiceReceiptModelMapper modelMapper,
            IStringLocalizer<Resources> stringLocalizer,
            IUserNotificationService userNotificationService,
            AuthenticationService authenticationService)
        {
            _navigationManager = navigationManager;
            _invoiceReceiptService = invoiceReceiptService;
            _stateChangedService = stateChangedService;
            _modelMapper = modelMapper;
            _stringLocalizer = stringLocalizer;
            _userNotificationService = userNotificationService;
            _authenticationService = authenticationService;

            InsertAdjustmentCommand = new AsyncRelayCommand<object?>(_ => InsertAdjustmentAsync());
            SaveDraftCommand = new AsyncRelayCommand<object?>(_ => ConfirmAndSaveDraftAsync(true));
            UpdateDraftCommand = new AsyncRelayCommand<object?>(_ => ConfirmAndUpdateDraftAsync(true));
            DeleteDraftCommand = new AsyncRelayCommand<object?>(_ => DeleteDraftAsync());
            DisbursementConfirnAndCompleteCommand = new AsyncRelayCommand<IList<PurchaseOrderLine>>(DisbursementConfirmAndCompleteAsync);
        }

        public async Task InitializeAsync(string stockReceiptCode)
        {
            CurrentStockReceiptCode = stockReceiptCode;

            IsDataChanged = false;

            var isSucessful = string.IsNullOrEmpty(CurrentStockReceiptCode) ?
                await LoadNewInvoiceReceiptAsync() :
                await LoadExistingInvoiceReceiptAsync();

            if (!isSucessful)
            {
                RedirectToPreviousPage();
                return;
            }

            ProductLines.CollectionChanged += ProductLines_CollectionChanged;

            IsDataLoaded = true;
        }

        public bool ShouldShowPurchaseOrderDisbursement()
        {
            return SystemConfigurations.IsInvoiceDisbursementsToPurchaseOrder && 
                !InvoiceSummaryModel.IsMatchToPurchaseOrders && 
                ProductLines.All(x => string.IsNullOrWhiteSpace(x.PurchaseOrderNumber));
        }

        public bool IsSaveEnabled()
        {
            if (!IsDataLoaded || IsLoading || IsCorruptedData)
            {
                return false;
            }

            if (InvoiceSummaryModel.IsMatchToPurchaseOrders)
            {
                return CurrentInvoiceReceipt?.HasReceiptedPurchaseOrder != true && Math.Round(Variance, UIConstants.RoundingDecimalPlaces) == 0;
            }

            return ProductLines.Count > 0 && GetAdjustmentRequired() == AdjustmentProductCost;
        }

        public bool IsFreightDisbursementEnabled()
        {
            var isEnabled = SystemConfigurations.FreightDisbursementType == FreightDisbursementType.UserSelect && InvoiceSummaryModel.FreightExtra > 0;

            if (IsDraft)
            {
                return isEnabled && !HasFreighProductLine;
            }

            return isEnabled;
        }

        public decimal GetRunningTotal()
        {
            if (ProductLines.Count == 0)
            {
                return 0;
            }

            var totalCost = ProductLines
                .Where(x => !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x.ProductCode!))
                .Sum(x => x.TotalCost);

            return Math.Round(totalCost, UIConstants.RoundingDecimalPlaces) + InvoiceSummaryModel.FreightExtra.GetValueOrDefault(0);
        }

        public decimal GetAdjustmentRequired()
        {
            if (ProductLines.Count == 0)
            {
                return 0;
            }

            return InvoiceSummaryModel.TotalAmount - GetRunningTotal();
        }

        private async Task<bool> LoadNewInvoiceReceiptAsync()
        {
            InvoiceSummaryModel = _stateChangedService.InvoiceSummaryState ?? new();

            if (InvoiceSummaryModel.SelectedSite == null)
            {
                return false;
            }

            IsLoading = true;
            IsDataListLoading = true;

            SystemConfigurations = await _invoiceReceiptService.GetSystemConfigurationsAsync(null);
            FreightDisbursementType = SystemConfigurations.FreightDisbursementType;
            FreightDisbursementType = SystemConfigurations.FreightDisbursementType == FreightDisbursementType.UserSelect ?
                FreightDisbursementType.None :
                SystemConfigurations.FreightDisbursementType;

            if (InvoiceSummaryModel.IsMatchToPurchaseOrders && InvoiceSummaryModel.PurchaseOrders.Count > 0)
            {
                await AddProductsFromPurchaseOrdersAsync(InvoiceSummaryModel.PurchaseOrders);
            }

            IsDataListLoading = false;
            IsLoading = false;

            return true;
        }

        private async Task AddProductsFromPurchaseOrdersAsync(List<PurchaseOrder> purchaseOrders)
        {
            IsBackOrders = InvoiceSummaryModel.SelectedSupplier?.IsDefaultBackOrderStatus == true;

            var productLines = new List<ProductLineViewModel>();

            foreach (var purchaseOrder in purchaseOrders)
            {
                foreach (var line in purchaseOrder.Lines)
                {
                    var productLine = _modelMapper.MapToProductLineViewModel(line);
                    productLine.PurchaseOrderNumber = purchaseOrder.PurchaseOrderCode;
                    productLines.Add(productLine);
                }
            }

            productLines = productLines.OrderBy(x => x.PurchaseOrderNumber).ThenBy(x => x.ProductCode).ToList();

            var detailedProductViewModels = await AssignProductsDetailsForAddingProductsAsync(productLines);

            if (detailedProductViewModels.Count > 0)
            {
                ProductLines.AddRange(detailedProductViewModels);
            }
        }

        private async Task<bool> LoadExistingInvoiceReceiptAsync()
        {
            IsLoading = true;
            CurrentInvoiceReceipt = await _invoiceReceiptService.GetInvoiceReceiptAsync(CurrentStockReceiptCode);

            if (string.IsNullOrEmpty(CurrentInvoiceReceipt.StockReceiptCode) || CurrentInvoiceReceipt.InvoiceReceiptStatus != InvoiceReceiptStatus.Draft)
            {
                await ShowLoadInvoiceReceiptFailedMessageAsync();
                IsLoading = false;
                return false;
            }

            SystemConfigurations = await _invoiceReceiptService.GetSystemConfigurationsAsync(null);
            FreightDisbursementType = ViewHelper.ConvertStringToFreightDisbursementType(CurrentInvoiceReceipt.FreightDisbursmentType);
            InvoiceSummaryModel = _modelMapper.MapToInvoiceSummaryModel(CurrentInvoiceReceipt);
            Note = CurrentInvoiceReceipt.Note ?? string.Empty;

            var productLines = CurrentInvoiceReceipt.Lines.Select(_modelMapper.MapToProductLineViewModel).ToList();

            ProductLines.Clear();
            ProductLines.AddRange(productLines);

            await AssignProductsDetailsForLoadingInvoiceReceiptAsync(productLines);

            if (FreightDisbursementType != FreightDisbursementType.Manual)
            {
                CalculateFreights();
            }

            if (InvoiceSummaryModel.IsMatchToPurchaseOrders)
            {
                IsBackOrders = await GetIsBackOrdersForExistingDraftAsync();
            }

            IsLoading = false;

            if (CurrentInvoiceReceipt.HasReceiptedPurchaseOrder)
            {
                await _userNotificationService.ConfirmAsync(
                   _stringLocalizer.GetString(Translations.POMatchProblemMessage),
                   _stringLocalizer.GetString(Translations.POMatchProblem),
                   _stringLocalizer.GetString(Translations.OK),
                   cancelButtonVisible: false);
            }

            return true;
        }

        private async Task<bool> GetIsBackOrdersForExistingDraftAsync()
        {
            if (ProductLines.Any(x => x.BackOrder > 0))
            {
                return true;
            }
            else
            {
                var supplier = await _invoiceReceiptService.GetSupplierAsync(CurrentInvoiceReceipt!.SupplierCode);

                if (!string.IsNullOrEmpty(supplier.SupplierCode))
                {
                    return supplier.IsDefaultBackOrderStatus == true;
                }
            }

            return false;
        }

        private void ProductLines_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (FreightDisbursementType != FreightDisbursementType.Manual)
            {
                CalculateFreights();
            }
        }

        public async Task NavigateBackAsync()
        {
            if (!IsDataChanged || IsCorruptedData)
            {
                RedirectToPreviousPage();
                return;
            }

            var answer = await _userNotificationService.ConfirmCancelAsync(
               _stringLocalizer.GetString(Translations.LeaveConfirmation_Content),
               _stringLocalizer.GetString(Translations.LeaveConfirmation_Title),
               _stringLocalizer.GetString(Translations.SaveAsDraft),
               _stringLocalizer.GetString(Translations.Discard),
               okButtonEnabled: IsSaveEnabled());

            if (answer == Answer.Yes)
            {
                var stockReceiptCode = string.Empty;

                if (IsDraft)
                {
                    stockReceiptCode = await DoUpdateDraftAsync();
                }
                else
                {
                    stockReceiptCode = await DoSaveAsDraftAsync();
                }

                if (!string.IsNullOrWhiteSpace(stockReceiptCode))
                {
                    if (string.IsNullOrWhiteSpace(ReturnUrl))
                    {
                        await RedirectToOverviewPageAndSendMessageAsync(stockReceiptCode, InvoiceReceiptChangedEventType.Draft);
                    }
                    else
                    {
                        RedirectToPreviousPage();
                    }
                }
            }
            else if (answer == Answer.Cancel)
            {
                RedirectToPreviousPage();
            }
        }

        private void RedirectToOverviewPage()
        {
            _navigationManager.NavigateTo(InvoiceReceiptNavigationUrls.InvoiceReceiptOverview);
        }

        private void RedirectToPreviousPage()
        {
            _navigationManager.NavigateTo(string.IsNullOrWhiteSpace(ReturnUrl) ? InvoiceReceiptNavigationUrls.InvoiceReceiptOverview : ReturnUrl);
        }

        public void OnFreightDisbursementTypeChanged(FreightDisbursementType type)
        {
            FreightDisbursementType = type;
            CalculateFreights();
            IsDataChanged = true;
        }

        public void OnNoteChanged(string value)
        {
            Note = value;
            IsDataChanged = true;
        }

        public void OnIsBackOrdersChanged(bool value)
        {
            IsBackOrders = value;

            foreach (var productLine in ProductLines)
            {
                productLine.BackOrder = IsBackOrders ? InvoiceReceiptCalculator.CalculateBackOrder(productLine.PurchaseQuantity, productLine.ReceiptQuantity) : 0;
            }

            IsDataChanged = true;
        }

        public void CalculateFreights()
        {
            if (InvoiceSummaryModel.FreightExtra.GetValueOrDefault(0) == 0)
            {
                return;
            }

            var adjustmentProduct = ProductLines.FirstOrDefault(x => x.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode);

            if (adjustmentProduct != null)
            {
                adjustmentProduct.FreightExtras = 0;
            }

            var realProducts = ProductLines.Where(x => x.ProductCode != InvoiceReceiptDataConstants.AdjustmentProductCode).ToList();

            if (realProducts.Count == 0)
            {
                return;
            }

            if (FreightDisbursementType == FreightDisbursementType.PerLine)
            {
                var freightPerLine = (InvoiceSummaryModel.FreightExtra ?? 0) / realProducts.Count;
                foreach (var product in realProducts)
                {
                    product.FreightExtras = freightPerLine;
                }
            }
            else if (FreightDisbursementType == FreightDisbursementType.PerQuantity)
            {
                var freightPerUnit = InvoiceReceiptCalculator.CalculateFreightPerUnit(realProducts.Sum(x => x.ReceiptQuantity), InvoiceSummaryModel.FreightExtra ?? 0);

                foreach (var line in realProducts)
                {
                    line.FreightExtras = freightPerUnit * line.ReceiptQuantity;
                }
            }
            else
            {
                foreach (var product in realProducts)
                {
                    product.FreightExtras = 0;
                }
            }
        }

        public async Task<string> ScanProductAsync(string productCode, bool isByAnySupplier)
        {
            IsLoading = true;
            IsProductNotFound = false;

            var scannedProduct = await _invoiceReceiptService.ScanProductAsync(productCode, InvoiceSummaryModel.SelectedSite?.SiteCode);

            if (scannedProduct == null || (!isByAnySupplier && !scannedProduct.SupplierCode.Equals(InvoiceSummaryModel.SelectedSupplier?.SupplierCode, StringComparison.OrdinalIgnoreCase)))
            {
                IsProductNotFound = true;
                IsLoading = false;
                return string.Empty;
            }

            var productViewModel = _modelMapper.MapToProductLineViewModel(scannedProduct);

            var detailedProductViewModels = await AssignProductsDetailsForAddingProductsAsync(new() { productViewModel });

            if (detailedProductViewModels.Count == 0)
            {
                IsProductNotFound = true;
                IsLoading = false;
                return string.Empty;
            }

            AddToProductList(detailedProductViewModels[0], true);
            IsDataChanged = true;
            IsLoading = false;

            return scannedProduct!.ProductCode;
        }

        private void RemoveHighlightBackgroundFromAddedProduct(params string[] productCodes)
        {
            // not add await here because it will block the grid from rendering
            _ = Task.Run(async () =>
            {
                await UpdateProductsIsFlaggedForUpdateState(productCodes);
                if (DataChanged.HasDelegate)
                {
                    await DataChanged.InvokeAsync();
                }
            });
        }

        private async Task UpdateProductsIsFlaggedForUpdateState(params string[] productCodes)
        {
            if (productCodes != null && productCodes.Length > 0)
            {
                await Task.Delay(CommonUIConstants.TimeToHighlightBackgroundMiliseconds);
                var products = ProductLines.Where(x => productCodes.Contains(x.ProductCode));

                foreach (var item in products)
                {
                    item.IsFlaggedForUpdatedState = false;
                }
            }
        }

        private void AddToProductList(ProductLineViewModel productViewModel, bool isFlaggedForUpdatedState)
        {
            var existProduct = ProductLines.FirstOrDefault(p => p.ProductCode == productViewModel.ProductCode);

            if (existProduct != null)
            {
                existProduct.ReceiptQuantity += 1;
                existProduct.IsFlaggedForUpdatedState = isFlaggedForUpdatedState;
                ProductLines.Remove(existProduct);
                ProductLines.Insert(0, existProduct);
            }
            else
            {
                productViewModel.ReceiptQuantity = 1;
                productViewModel.IsFlaggedForUpdatedState = isFlaggedForUpdatedState;
                ProductLines.Insert(0, productViewModel);
            }

            RemoveHighlightBackgroundFromAddedProduct(productViewModel.ProductCode!);
        }

        private async Task<List<ProductLineViewModel>> AssignProductsDetailsForAddingProductsAsync(List<ProductLineViewModel> lineViewModels)
        {
            var productCodes = lineViewModels.Select(x => x.ProductCode!).ToList();

            var productsDetails = await _invoiceReceiptService.GetProductsAsync(productCodes, InvoiceSummaryModel.SelectedSite?.SiteCode);

            var validProductCodes = productsDetails.Select(x => x.ProductCode);
            var returnedLineViewModels = lineViewModels.Where(x => validProductCodes.Contains(x.ProductCode!)).ToList();

            foreach (var lineViewModel in returnedLineViewModels)
            {
                var product = productsDetails.Find(x => x.ProductCode == lineViewModel.ProductCode)!;

                lineViewModel.SKU = product.SKU;
                lineViewModel.Description = product.Description;
                lineViewModel.SupplierProductCode = product.SupplierProductCode;

                if (!InvoiceSummaryModel.IsMatchToPurchaseOrders)
                {
                    lineViewModel.Cost = product.Cost ?? 0;
                }

                lineViewModel.SupplierName = product.SupplierName;
                lineViewModel.StandardSellingPrice = product.StandardSellingPrice;
                lineViewModel.TaxRate = product.TaxRate;
                lineViewModel.PackSize = product.PurchasingRules?.PackSize ?? 1;
                lineViewModel.TargetMarginPercentage = product.TargetMarginPercentage ?? 0;

                lineViewModel.IsRequestLabel = SystemConfigurations.IsGoodsReceiptPrintLabel;
            }

            if (returnedLineViewModels.Count > 0)
            {
                await AssignProductsQuantityOnHandsAsync(returnedLineViewModels);
            }

            return returnedLineViewModels;
        }

        private async Task AssignProductsDetailsForLoadingInvoiceReceiptAsync(List<ProductLineViewModel> lineViewModels)
        {
            var productCodes = lineViewModels
                .Select(x => x.ProductCode!)
                .Where(x => !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x))
                .ToList();

            var productsDetails = await _invoiceReceiptService.GetProductsAsync(productCodes, InvoiceSummaryModel.SelectedSite?.SiteCode);

            foreach (var lineViewModel in lineViewModels)
            {
                if (lineViewModel.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode)
                {
                    lineViewModel.Description = InvoiceReceiptDataConstants.AdjustmentProductDescription;
                }
                else if (lineViewModel.ProductCode == InvoiceReceiptDataConstants.FreightProductCode)
                {
                    lineViewModel.Description = InvoiceReceiptDataConstants.FreightProductDescription;
                }
                else
                {
                    var product = productsDetails.Find(x => x.ProductCode == lineViewModel.ProductCode)!;

                    lineViewModel.SKU = product.SKU;
                    lineViewModel.Description = product.Description;
                    lineViewModel.SupplierProductCode = product.SupplierProductCode;

                    lineViewModel.SupplierCode = product.SupplierCode;
                    lineViewModel.SupplierName = product.SupplierName;
                    lineViewModel.TaxRate = product.TaxRate;
                    lineViewModel.PackSize = product.PurchasingRules?.PackSize ?? 1;
                    lineViewModel.TargetMarginPercentage = product.TargetMarginPercentage ?? 0;
                }
            }

            await AssignProductsQuantityOnHandsAsync(lineViewModels);
        }

        private async Task AssignProductsQuantityOnHandsAsync(List<ProductLineViewModel> lineViewModels)
        {
            var productCodes = lineViewModels
                .Select(x => x.ProductCode!)
                .Where(x => !InvoiceReceiptLogicHelper.IsAdjustmentOrFreightProductCode(x))
                .ToList();
            var productInventoryDetails = await _invoiceReceiptService.GetProductInventoriesAsync(productCodes!, InvoiceSummaryModel.SelectedSite?.SiteCode);

            foreach (var lineViewModel in lineViewModels)
            {
                var productInventory = productInventoryDetails.Find(x => x.ProductCode == lineViewModel.ProductCode);

                if (productInventory != null)
                {
                    lineViewModel.QtyOnHand = productInventory.SellableQuantity ?? 0;
                    lineViewModel.QtyOnOrder = productInventory.OnOrderQuantity ?? 0;
                }
            }

            if (DataChanged.HasDelegate)
            {
                await DataChanged.InvokeAsync();
            }
        }

        private async Task InsertAdjustmentAsync()
        {
            var answer = await _userNotificationService.ConfirmCancelAsync(
                _stringLocalizer.GetString(Translations.InsertAdjustmentMessage),
                _stringLocalizer.GetString(Translations.InsertAdjustmentConfirmation),
                _stringLocalizer.GetString(Translations.YesInsert));

            if (answer == Answer.Yes)
            {
                var product = ProductLines.FirstOrDefault(x => x.ProductCode == InvoiceReceiptDataConstants.AdjustmentProductCode);
                var adjustment = InvoiceReceiptCalculator.CalculateAdjustment(InvoiceSummaryModel.TotalAmount, GetRunningTotal());

                if (product == null)
                {
                    product = new ProductLineViewModel
                    {
                        ProductCode = InvoiceReceiptDataConstants.AdjustmentProductCode,
                        Description = InvoiceReceiptDataConstants.AdjustmentProductDescription,
                        Cost = adjustment,
                        PackSize = 1,
                        ReceiptPack = 1,
                        ReceiptQuantity = 1,
                        TotalCost = adjustment,
                        IsFlaggedForUpdatedState = true,
                    };

                    ProductLines.Add(product);
                }
                else
                {
                    product.Cost = adjustment;
                    product.IsFlaggedForUpdatedState = true;

                    await DataChanged.InvokeAsync();
                }

                IsDataChanged = true;

                RemoveHighlightBackgroundFromAddedProduct(product!.ProductCode!);
            }
        }

        public async Task ConfirmAndCompleteAsync()
        {
            var result = await ShowConfirmCompleteMessageAsync();

            if (result.Answer != Answer.Yes)
            {
                return;
            }

            await DoCompleteInvoiceReceiptAsync(result.IsChecked);
        }

        private async Task DoCompleteInvoiceReceiptAsync(bool shouldShowReport)
        {
            IsLoading = true;

            var stockReceiptCode = string.Empty;

            if (IsDraft)
            {
                stockReceiptCode = await DoCompleteDraftInvoiceReceiptAsync();
            }
            else
            {
                stockReceiptCode = await DoCompleteNewInvoiceReceiptAsync();
            }

            IsLoading = false;

            if (string.IsNullOrEmpty(stockReceiptCode))
            {
                await ShowCompleteFailedMessageAsync();
                return;
            }

            if (shouldShowReport)
            {
                await RedirectToCompletedPageAndSendMessagesAsync(stockReceiptCode);
            }
            else
            {
                await RedirectToOverviewPageAndSendMessageAsync(stockReceiptCode, InvoiceReceiptChangedEventType.Complete);
            }
        }

        private async Task RedirectToCompletedPageAndSendMessagesAsync(string stockReceiptCode)
        {
            _navigationManager.NavigateTo(string.Format(InvoiceReceiptNavigationUrls.CompletedInvoiceReceipt, stockReceiptCode));
            await Task.Delay(UIConstants.DelaySendingMessageAfterRedirecting);
            WeakReferenceMessenger.Default.Send(new ShowReceiptReportMessage(stockReceiptCode));
            WeakReferenceMessenger.Default.Send(new InvoiceReceiptChangedMessage(stockReceiptCode, InvoiceReceiptChangedEventType.Complete));
        }

        private async Task<string> DoCompleteNewInvoiceReceiptAsync()
        {
            var requestDto = CreateInvoiceReceiptCreateBffDto(ProductLines, InvoiceReceiptStatus.Complete);
            return await _invoiceReceiptService.CreateInvoiceReceiptAsync(requestDto);
        }

        private async Task<string> DoCompleteDraftInvoiceReceiptAsync()
        {
            var stockReceiptCode = string.Empty;

            if (IsDataChanged)
            {
                var requestDto = CreateInvoiceReceiptUpdateBffDto(ProductLines);
                stockReceiptCode = await _invoiceReceiptService.UpdateInvoiceReceiptAsync(requestDto!);

                if (string.IsNullOrEmpty(stockReceiptCode))
                {
                    return string.Empty;
                }
            }

            stockReceiptCode = await _invoiceReceiptService.UpdateInvoiceReceiptStatusAsync(new()
            {
                InvoiceReceiptStatus = InvoiceReceiptStatus.Complete,
                StockReceiptCode = CurrentStockReceiptCode,
                SiteCode = CurrentInvoiceReceipt!.SiteCode,
                UpdatedBy = _authenticationService.UserName
            });

            return stockReceiptCode;
        }

        private async Task ConfirmAndSaveDraftAsync(bool shouldRedirectToOverviewPage)
        {
            if (!await ValidateFreightDisbursementAsync())
            {
                return;
            }

            var isConfirmed = await _userNotificationService.ConfirmAsync(
               _stringLocalizer.GetString(Translations.SaveDraft_Confirm_Content),
               _stringLocalizer.GetString(Translations.SaveDraft_Confirm_Title),
               _stringLocalizer.GetString(Translations.YesSaveAsDraft),
               cancelButtonVisible: true);

            if (isConfirmed)
            {
                var stockReceiptCode = await DoSaveAsDraftAsync();

                if (!string.IsNullOrEmpty(stockReceiptCode))
                {
                    if (shouldRedirectToOverviewPage)
                    {
                        await RedirectToOverviewPageAndSendMessageAsync(stockReceiptCode, InvoiceReceiptChangedEventType.Draft);
                    }
                    else
                    {
                        RedirectToPreviousPage();
                    }
                }
            }
        }

        private async Task ConfirmAndUpdateDraftAsync(bool shouldRedirectToOverviewPage)
        {
            if (!await ValidateFreightDisbursementAsync())
            {
                return;
            }

            var isConfirmed = await _userNotificationService.ConfirmAsync(
               _stringLocalizer.GetString(Translations.UpdateDraftConfirmation_Content),
               _stringLocalizer.GetString(Translations.UpdateDraftConfirmation),
               _stringLocalizer.GetString(Translations.YesUpdateDraft),
               cancelButtonVisible: true);

            if (isConfirmed)
            {
                var stockReceiptCode = await DoUpdateDraftAsync();

                if (!string.IsNullOrEmpty(stockReceiptCode))
                {
                    if (shouldRedirectToOverviewPage)
                    {
                        await RedirectToOverviewPageAndSendMessageAsync(stockReceiptCode, InvoiceReceiptChangedEventType.Draft);
                    }
                    else
                    {
                        RedirectToPreviousPage();
                    }
                }
            }
        }

        private async Task<string> DoUpdateDraftAsync()
        {
            IsLoading = true;
            IsLoadingVisible = true;

            var requestDto = CreateInvoiceReceiptUpdateBffDto(ProductLines);

            var stockReceiptCode = await _invoiceReceiptService.UpdateInvoiceReceiptAsync(requestDto!);

            IsLoading = false;
            IsLoadingVisible = false;

            if (string.IsNullOrEmpty(stockReceiptCode))
            {
                await ShowUpdateDraftFailedMessage();
                return string.Empty;
            }

            return stockReceiptCode;
        }

        public async Task<bool> ValidateFreightDisbursementAsync()
        {
            if (!IsFreightDisbursementValid())
            {
                await _userNotificationService.ConfirmAsync(
                   _stringLocalizer.GetString(Translations.FreightDisbursementProblem_Content),
                   _stringLocalizer.GetString(Translations.FreightDisbursementProblem_Title),
                   _stringLocalizer.GetString(Translations.OK),
                   cancelButtonVisible: false);
                return false;
            }

            return true;
        }

        private bool IsFreightDisbursementValid()
        {
            if (FreightDisbursementType == FreightDisbursementType.None)
            {
                return true;
            }

            var sumFreight = Math.Round(ProductLines.Sum(x => x.FreightExtras).GetValueOrDefault(0), UIConstants.RoundingDecimalPlaces);
            return sumFreight == Math.Round(InvoiceSummaryModel.FreightExtra.GetValueOrDefault(0), UIConstants.RoundingDecimalPlaces);
        }

        private async Task<string> DoSaveAsDraftAsync()
        {
            IsLoading = true;
            IsLoadingVisible = true;

            var requestDto = CreateInvoiceReceiptCreateBffDto(ProductLines, InvoiceReceiptStatus.Draft);
            var stockReceiptCode = await _invoiceReceiptService.CreateInvoiceReceiptAsync(requestDto);

            IsLoading = false;
            IsLoadingVisible = false;

            if (string.IsNullOrEmpty(stockReceiptCode))
            {
                await ShowSaveDraftFailedMessage();
                return string.Empty;
            }

            return stockReceiptCode;
        }

        private async Task RedirectToOverviewPageAndSendMessageAsync(string stockReceiptCode, InvoiceReceiptChangedEventType eventType)
        {
            RedirectToOverviewPage();

            await Task.Delay(UIConstants.DelaySendingMessageAfterRedirecting);
            WeakReferenceMessenger.Default.Send(new InvoiceReceiptChangedMessage(stockReceiptCode, eventType));
        }

        public async Task DeleteDraftAsync()
        {
            bool isConfirmed = await ShowConfirmDeleteDraftAsync();

            if (isConfirmed)
            {
                var stockReceiptCode = await _invoiceReceiptService.UpdateInvoiceReceiptStatusAsync(new()
                {
                    InvoiceReceiptStatus = InvoiceReceiptStatus.Cancelled,
                    StockReceiptCode = CurrentStockReceiptCode,
                    SiteCode = CurrentInvoiceReceipt!.SiteCode,
                    UpdatedBy = _authenticationService.UserName
                });

                if (string.IsNullOrEmpty(stockReceiptCode))
                {
                    await ShowDeleteDraftFailedMessage();
                    return;
                }

                SendDeleteMessageAndRedirectToOverviewPage(stockReceiptCode);
            }
        }

        private void SendDeleteMessageAndRedirectToOverviewPage(string stockReceiptCode)
        {
            WeakReferenceMessenger.Default.Send(new InvoiceReceiptChangedMessage(stockReceiptCode, InvoiceReceiptChangedEventType.Delete));
            RedirectToOverviewPage();
        }

        private async Task DisbursementConfirmAndCompleteAsync(IList<PurchaseOrderLine>? purchaseOrderLines)
        {
            var confirmResult = await ShowConfirmCompleteMessageAsync();

            if (confirmResult.Answer != Answer.Yes)
            {
                return;
            }

            IsPurchaseOrderDisbursements = true;

            AddPurchaseOrderDisbursements(purchaseOrderLines!);

            IsDataChanged = true;

            await DoCompleteInvoiceReceiptAsync(confirmResult.IsChecked);
        }

        private void AddPurchaseOrderDisbursements(IList<PurchaseOrderLine> purchaseOrderLines)
        {
            foreach (var line in purchaseOrderLines!)
            {
                var existProduct = ProductLines.FirstOrDefault(p => p.ProductCode == line.ProductCode);

                if (existProduct != null)
                {
                    existProduct.PurchaseOrderDisbursements.Add(new PurchaseOrderDisbursement
                    {
                        BackOrderQuantity = line.BackOrder,
                        PurchaseOrderNumber = line.PurchaseOrderCode,
                        Quantity = line.ReceiptQuantity,
                    });
                }
            }
        }

        private InvoiceReceiptUpdateBffDto CreateInvoiceReceiptUpdateBffDto(IEnumerable<ProductLineViewModel> lines)
        {
            var lineBffDtos = CreateInvoiceReceiptLineBffDtos(lines);

            return new()
            {
                StockReceiptCode = CurrentStockReceiptCode,
                UpdatedBy = _authenticationService.UserName,
                SiteCode = InvoiceSummaryModel.SelectedSite!.SiteCode,
                FreightDisbursmentType = ViewHelper.ConvertFreightDisbursementTypeToString(FreightDisbursementType),
                InvoiceCode = InvoiceSummaryModel.InvoiceNumber,
                InvoiceDate = InvoiceSummaryModel.InvoiceDate,
                InvoiceReceiptStatus = InvoiceReceiptStatus.Draft,
                SupplierCode = InvoiceSummaryModel.SelectedSupplier!.SupplierCode,
                TotalFreight = InvoiceSummaryModel.FreightExtra ?? 0,
                TotalInvoiceValue = InvoiceSummaryModel.TotalAmount,
                TotalInputTax = InvoiceSummaryModel.Tax ?? 0,
                Note = Note,
                Lines = lineBffDtos,
            };
        }

        private InvoiceReceiptCreateBffDto CreateInvoiceReceiptCreateBffDto(IEnumerable<ProductLineViewModel> lines, string status)
        {
            var lineBffDtos = CreateInvoiceReceiptLineBffDtos(lines);

            return new InvoiceReceiptCreateBffDto
            {
                CreatedBy = _authenticationService.UserName,
                SiteCode = InvoiceSummaryModel.SelectedSite!.SiteCode,
                FreightDisbursmentType = ViewHelper.ConvertFreightDisbursementTypeToString(FreightDisbursementType),
                InvoiceCode = InvoiceSummaryModel.InvoiceNumber,
                InvoiceDate = InvoiceSummaryModel.InvoiceDate,
                InvoiceReceiptStatus = status,
                SupplierCode = InvoiceSummaryModel.SelectedSupplier!.SupplierCode,
                TotalFreight = InvoiceSummaryModel.FreightExtra ?? 0,
                TotalInvoiceValue = InvoiceSummaryModel.TotalAmount,
                TotalInputTax = InvoiceSummaryModel.Tax ?? 0,
                Note = Note,
                Lines = lineBffDtos,
            };
        }

        private List<InvoiceReceiptLineBffDto> CreateInvoiceReceiptLineBffDtos(IEnumerable<ProductLineViewModel> lines)
        {
            var lineBffDtos = new List<InvoiceReceiptLineBffDto>();
            for (int i = 0; i < lines.Count(); i++)
            {
                var item = lines.ElementAt(i);
                var line = _modelMapper.MapToInvoiceReceiptLineBffDto(item, InvoiceSummaryModel.IsMatchToPurchaseOrders, IsPurchaseOrderDisbursements);
                line.LineNumber = i + 1;
                lineBffDtos.Add(line);
            }

            if (InvoiceSummaryModel.FreightExtra > 0 && FreightDisbursementType == FreightDisbursementType.None && !lineBffDtos.Exists(x => x.ProductCode == InvoiceReceiptDataConstants.FreightProductCode))
            {
                lineBffDtos.Add(new InvoiceReceiptLineBffDto
                {
                    LineNumber = lines.Count() + 1,
                    ProductCode = InvoiceReceiptDataConstants.FreightProductCode,
                    FreightDisbursmentAmount = 0,
                    ReceiptQuantity = 1,
                    ReceiptCostPriceExcludingInputTax = InvoiceSummaryModel.FreightExtra.Value,
                    UpdateStandardSellingPrice = 0,
                });
            }

            return lineBffDtos;
        }

        private async Task ShowSaveDraftFailedMessage()
        {
            await _userNotificationService.ConfirmAsync(
                _stringLocalizer.GetString(Translations.SaveDraftFailed_Content),
                _stringLocalizer.GetString(Translations.SaveDraftFailed_Title),
                _stringLocalizer.GetString(Translations.OK), false);
        }

        private async Task ShowUpdateDraftFailedMessage()
        {
            await _userNotificationService.ConfirmAsync(
                _stringLocalizer.GetString(Translations.UpdateReceiptFailed_Content),
                _stringLocalizer.GetString(Translations.UpdateReceiptFailed),
                _stringLocalizer.GetString(Translations.OK), false);
        }

        private async Task<ConfirmCheckboxAnswerModel> ShowConfirmCompleteMessageAsync()
        {
            var confirmMessage = _stringLocalizer.GetString(Translations.CompleteConfirmation_Content);
            var hasPrintCheckbox = !IsMobile;

            var confirmModel = new TriquestraConfirmWithCheckboxDialogModel
            {
                Content = confirmMessage,
                Title = _stringLocalizer.GetString(Translations.CompleteConfirmation),
                ButtonText = _stringLocalizer.GetString(Translations.YesComplete),
                CancelButtonVisible = true,
                IsCheckBoxChecked = hasPrintCheckbox,
                CheckBoxText = hasPrintCheckbox ? _stringLocalizer.GetString(Translations.PrintReceipt) : string.Empty,
                IsHtmlContent = true
            };
            var result = await _userNotificationService.ConfirmWithCheckboxAsync(confirmModel);
            return result;
        }

        private async Task ShowCompleteFailedMessageAsync()
        {
            await _userNotificationService.ConfirmAsync(
                _stringLocalizer.GetString(Translations.CompleteFailed_Content),
                _stringLocalizer.GetString(Translations.CompleteFailed_Title),
                _stringLocalizer.GetString(Translations.OK), false);
        }

        private async Task ShowLoadInvoiceReceiptFailedMessageAsync()
        {
            await _userNotificationService.ConfirmAsync(
                _stringLocalizer.GetString(Translations.LoadReceiptFailed_Content),
                _stringLocalizer.GetString(Translations.LoadReceiptFailed),
                _stringLocalizer.GetString(Translations.OK), false);
        }

        private Task<bool> ShowConfirmDeleteDraftAsync()
        {
            return _userNotificationService.ConfirmAsync(
                               _stringLocalizer.GetString(Translations.DeleteDraftConfirm_Content),
                               _stringLocalizer.GetString(Translations.DeleteConfirmation),
                               _stringLocalizer.GetString(Translations.YesDelete), true);
        }

        private Task<bool> ShowDeleteDraftFailedMessage()
        {
            return _userNotificationService.ConfirmAsync(
                               _stringLocalizer.GetString(Translations.DeleteDraftFailed_Content),
                               _stringLocalizer.GetString(Translations.DeleteDraftFailed),
                               _stringLocalizer.GetString(Translations.OK), false);
        }

        public void Dispose()
        {
            _stateChangedService.RemoveValue<InvoiceSummaryModel>();
            ProductLines.CollectionChanged -= ProductLines_CollectionChanged;
            GC.SuppressFinalize(this);
        }
    }
}