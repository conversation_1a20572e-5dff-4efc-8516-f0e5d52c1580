﻿.details-grid {
    width: 100%;
}

.grid-empty-content-wrapper {
    display: flex;
    width: 100%;
    min-height: 200px;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
    background-color: #fff;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    font-size: 14px;
}

.details-grid-mobile-card {
    height: 102px;
    border-radius: 12px !important;
    margin-bottom: 15px;
    max-width: calc(100vw - 30px);
}

    .details-grid-mobile-card .k-card-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .details-grid-mobile-card ::deep .k-card-body {
        display: flex;
        flex-direction: column;
        height: 102px;
        padding: 15px 0 15px 20px;
        align-items: flex-start;
        box-shadow: none;
    }

        .details-grid-mobile-card ::deep .k-card-body .k-card-title {
            margin: 0;
            font-weight: 500;
            font-size: 14px;
            line-height: 16.41px;
            color: #202020;
            align-self: center;
        }

        .details-grid-mobile-card ::deep .k-card-body .k-card-description {
            font-weight: 400;
            font-size: 12px;
            line-height: 14.06px;
            color: #898989;
        }

.circle {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin: 0 5px;
    padding-top: 2px;
}

    .circle:after {
        content: '';
        display: table;
        width: 12px;
        height: 12px;
        background: #D9D9D9;
        border-radius: 50%;
    }
