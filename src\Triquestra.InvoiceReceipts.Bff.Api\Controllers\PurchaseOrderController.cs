using Duende.Bff;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triquestra.Common.Authorization.Authentication;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.InvoiceReceipts.Bff.Api.Configuration;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Api.Controllers
{
    [ApiController]
    [DynamicRoute("[controller]")]
    [BffApi]
    [Authorize(Policy = InvoiceReceiptPolicies.INVOICE_RECEIPT)]
    public class PurchaseOrderController : ControllerBase
    {
        private readonly ILogger<PurchaseOrderController> _logger;
        private readonly IPurchaseOrderBffService _purchaseOrderBffService;
        private readonly AuthenticationService _authenticationService;

        public PurchaseOrderController(
            ILogger<PurchaseOrderController> logger,
            IPurchaseOrderBffService purchaseOrderBffService,
            AuthenticationService authenticationService)
        {
            _logger = logger;
            _purchaseOrderBffService = purchaseOrderBffService;
            _authenticationService = authenticationService;
        }

        [HttpPost]
        [Route("searchpurchaseordersfordisbursement")]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderBffDto>>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderBffDto>>>(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchOpenPurchaseOrdersForDisbursement(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, requestBffDto.DestinationSiteCodes.Cast<int?>().ToList());
                if (!valid)
                    return Unauthorized();

                var result = await _purchaseOrderBffService.SearchPurchaseOrdersForDisbursementAsync(requestBffDto);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Search purchase orders for disbursement failed.");
                return Ok(new BffResultDto<List<PurchaseOrderBffDto>> { Data = new List<PurchaseOrderBffDto>(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpPost]
        [Route("searchcompletedpurchaseorderreceipts")]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderReceiptBffDto>>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderReceiptBffDto>>>(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchCompletedPurchaseOrderReceipts(PurchaseOrderReceiptSearchRequestBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, requestBffDto.SiteCodes.Cast<int?>().ToList());
                if (!valid)
                    return Unauthorized();

                var result = await _purchaseOrderBffService.SearchCompletedPurchaseOrderReceiptsAsync(requestBffDto);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Search purchase order receipts failed.");
                return Ok(new BffResultDto<List<PurchaseOrderReceiptBffDto>> { Data = new List<PurchaseOrderReceiptBffDto>(), StatusCode = 500, ErrorMsg = ex.Message });
            }
        }

        [HttpPost]
        [Route("searchopenpurchaseorders")]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderBffDto>>>(StatusCodes.Status200OK)]
        [ProducesResponseType<BffResultDto<List<PurchaseOrderBffDto>>>(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SearchOpenPurchaseOrders(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            try
            {
                var valid = _authenticationService.ValidateSiteCodes(HttpContext.User, requestBffDto.DestinationSiteCodes.Cast<int?>().ToList());
                if (!valid)
                    return Unauthorized();

                var result = await _purchaseOrderBffService.SearchOpenPurchaseOrdersAsync(requestBffDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Search purchase orders failed.");

                return Ok(new BffResultDto<List<PurchaseOrderBffDto>>
                {
                    Data = new List<PurchaseOrderBffDto>(),
                    StatusCode = 500,
                    ErrorMsg = ex.Message
                });
            }
        }
    }
}