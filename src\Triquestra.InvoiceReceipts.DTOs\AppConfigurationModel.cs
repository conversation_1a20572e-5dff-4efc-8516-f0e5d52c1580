﻿using Triquestra.IdentityServer.BffApi.Authorization.Configuration;

namespace Triquestra.InvoiceReceipts.DTOs
{
    public class AppConfigurationModel : AuthorizationConfigurationModel
    {
        public string? ExecutingAssemblyName { get; set; }
        public string DomainApiClientId { get; set; } = string.Empty;
        public string DomainApiClientSecret { get; set; } = string.Empty;
        public string TriquestraApiApiRoot { get; set; } = string.Empty;
        public string TriquestraApiBaseUrl { get; set; } = string.Empty;

        public string InfinityApiApiRoot { get; set; } = string.Empty;

        public int CacheExpirationInMinutes { get; set; }
    }
}