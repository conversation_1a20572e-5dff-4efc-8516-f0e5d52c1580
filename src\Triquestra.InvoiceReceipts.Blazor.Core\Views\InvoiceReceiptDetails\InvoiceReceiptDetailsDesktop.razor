@using Infinity.Blazor.Components.ProductSearchDropdown.View
@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Loaders
@using Infinity.Blazor.UIControls.Tags
@using Infinity.Blazor.UIControls.Utillities
@using Infinity.InvoiceReceipts.Blazor.ViewModels.InvoiceReceiptDetails
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductList
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ProductSearchPanel
@using Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Desktop
@using Infinity.InvoiceReceipts.Blazor.Views.Common.ReceiptSummaryPanel
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.Extensions.Localization;
@using Triquesta.InfinityWeb.Common.Base;
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums

@inherits RazorBaseMvvm<InvoiceReceiptDetailsViewModel>
<div class="page-container">
    <!-- header -->
    <div>
        <div class="page-header">
            <div class="header-group">
                <InfBackButton OnClick="ViewModel.NavigateBackAsync" />
                <div class="title">
                    @Localizer.GetString(Translations.InvoiceReceipt)
                </div>
            </div>
            <div class="buttons">
                @if (ViewModel.IsDraft)
                {
                    <InfButton Enabled="ViewModel.IsDataLoaded"
                               LabelText="@Localizer.GetString(Translations.DeleteDraft)"
                               FillMode="FillMode.Flat"
                               ClickCommand="ViewModel.DeleteDraftCommand" />

                    <InfButton Enabled="@(ViewModel.IsDataChanged && ViewModel.IsSaveEnabled())"
                               LabelText="@Localizer.GetString(Translations.UpdateDraft)"
                               FillMode="FillMode.Outline"
                               ClickCommand="ViewModel.UpdateDraftCommand" />
                }
                else
                {
                    <InfButton Enabled="@ViewModel.IsSaveEnabled()"
                               LabelText="@Localizer.GetString(Translations.SaveAsDraft)"
                               FillMode="FillMode.Outline"
                               ClickCommand="ViewModel.SaveDraftCommand" />
                }

                <InfButton Enabled="ViewModel.IsSaveEnabled()"
                           LabelText="@Localizer.GetString(Translations.CompleteReceipt)"
                           FillMode="FillMode.Solid"
                           ButtonType="ButtonType.Button"
                           ClickCommand="_completeReceiptCommand">
                </InfButton>
            </div>
        </div>
    </div>
    @if (ViewModel.IsDataLoaded)
    {
        <ReceiptSummaryPanelDesktop IsDraft="ViewModel.IsDraft"
                                    InvoiceSummary="ViewModel.InvoiceSummaryModel"
                                    RunningTotal="ViewModel.GetRunningTotal()"
                                    AdjustmentProductCost="ViewModel.AdjustmentProductCost"
                                    AdjustmentRequired="ViewModel.GetAdjustmentRequired()"
                                    FreightDisbursementType="ViewModel.FreightDisbursementType"
                                    FreightDisbursementTypeChanged="ViewModel.OnFreightDisbursementTypeChanged"
                                    IsFreightDisbursementEnabled="ViewModel.IsFreightDisbursementEnabled()"
                                    Note="@ViewModel.Note"
                                    NoteChanged="ViewModel.OnNoteChanged"
                                    InsertAdjustmentCommand="ViewModel.InsertAdjustmentCommand"
                                    Variance="ViewModel.Variance"
                                    IsBackOrders="ViewModel.IsBackOrders"
                                    IsBackOrdersChanged="ViewModel.OnIsBackOrdersChanged"
                                    HasAdjustmentLine="ViewModel.HasAdjustmentLine" />
        @if (!ViewModel.InvoiceSummaryModel.IsMatchToPurchaseOrders)
        {
            <div class="product-search-wrapper">
                <ProductSearchPanelDesktop @ref="_productSearchPanelRef"
                                           IsLoading="ViewModel.IsLoading"
                                           IsProductNotFound="ViewModel.IsProductNotFound"
                                           SearchBarcodeCommand="_productDropdownSearchBarcodeCommand"
                                           SiteCode="(short?)ViewModel.InvoiceSummaryModel.SelectedSite?.SiteCode"
                                           SupplierCode="@ViewModel.InvoiceSummaryModel.SelectedSupplier?.SupplierCode" />

            </div>
        }
    }
    else
    {
        <InfLoadingBox />
    }
</div>

@if (ViewModel.IsDataLoaded)
{
    <div class="productlist-wrapper">
        <ProductListDesktop @ref="_productListRef"
                            ProductLines="ViewModel.ProductLines"
                            DataChanged="ProductListDataChangedHandler"
                            IsLoading="ViewModel.IsDataListLoading"
                            IsChangeSellPriceReceipt="ViewModel.SystemConfigurations.IsChangeSellPriceReceipt"
                            IsFreightEditable="ViewModel.FreightDisbursementType == FreightDisbursementType.Manual"
                            IsMatchToPurchaseOrders="ViewModel.InvoiceSummaryModel.IsMatchToPurchaseOrders"
                            IsBackOrders="ViewModel.IsBackOrders" />
    </div>
}
<DisbursementDialogDesktop @ref="_disbursementDialogRef" @bind-Visible="_isDisbursementDialogVisible" ConfirmCommand="ViewModel.DisbursementConfirnAndCompleteCommand" />
<InfLoadingPopup @bind-Visible="ViewModel.IsLoadingVisible" IsModal />