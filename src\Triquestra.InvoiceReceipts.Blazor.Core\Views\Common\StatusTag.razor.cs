﻿using Infinity.Blazor.UIControls.Tags;
using Infinity.InvoiceReceipts.Blazor.Constants;
using Microsoft.AspNetCore.Components;
using Triquestra.InvoiceReceipts.Common.Constants;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common
{
    public partial class StatusTag
    {
        [Parameter]
        [EditorRequired]
        public string Status { get; set; } = string.Empty;

        private static TagType GetTagType(string status)
            => status switch
            {
                InvoiceReceiptStatus.Complete => TagType.Success,
                _ => TagType.Default
            };

        private static string GetDisplayStatusTranslationKey(string status)
            => status switch
            {
                InvoiceReceiptStatus.Draft => Translations.DraftInvoice,
                InvoiceReceiptStatus.Complete => Translations.CompletedInvoice,
                _ => string.Empty
            };
    }
}