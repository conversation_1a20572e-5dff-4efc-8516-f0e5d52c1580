﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.Icons
@using Infinity.Blazor.Components.MobileSwipeableProductContainer

@inject IStringLocalizer<Resources> Localizer
@inject NavigationManager Navigation

<SwipeableProductContainer IsSwiped="IsSwiped" IsSwipedChanged="IsSwipedChanged" Enabled="@(DeleteCommand != null)">
    <span class="k-icon k-panelbar-expand k-i-arrow-chevron-down"></span>
    <div class="product-description p-1  @(!IsValidationVisible || IsCurrentProfitMarginValid ? string.Empty : "text-red")">
        @Product.Description?.AddEllipsis(UIConstants.ProductDescriptionMaxLengthMobile)
    </div>
    <div class="d-flex flex-column">
        <label class="receipt-quantity-label">@Localizer.GetString(Translations.ReceiptQty)</label>
        <span class="receipt-quantity @(!IsValidationVisible || IsCurrentProfitMarginValid ? string.Empty : "text-red")">@Product.ReceiptQuantity.ToString(UIConstants.DecimalDisplayFormat)</span>
    </div>
    @if (DeleteCommand != null)
    {
        <div @onclick:stopPropagation @onclick:preventDefault class="delete-button @(IsSwiped ? "move" : "")">
            <InfButton FillMode="FillMode.Outline" ClickCommand="DeleteCommand" ClickCommandParameter="Product">
                <IconTemplate>
                    <DeleteIcon Color="#fff" />
                </IconTemplate>
            </InfButton>
        </div>
    }
</SwipeableProductContainer>