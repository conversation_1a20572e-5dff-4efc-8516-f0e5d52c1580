﻿using Infinity.Blazor.UIControls.Grids;
using Infinity.Blazor.UIControls.Grids.Mapping;
using Infinity.Blazor.UIControls.Grids.Parameters;
using Infinity.InvoiceReceipts.Blazor.ViewModels.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using System.Collections.ObjectModel;

namespace Infinity.InvoiceReceipts.Blazor.Views.CompletedInvoiceReceipt.CompletedProductList
{
    public partial class CompletedProductListDesktop
    {
        private readonly InfGridViewSelectionParameters<ProductLineViewModel> _selectionParameters = new()
        {
            SelectionMode = SelectionMode.None
        };

        private readonly InfGridViewPageParemeters _pageParameters = new()
        {
            PageSize = 50,
            PageSizes = new() { null, 5, 10, 20, 50 }
        };

        private InfGridView<ProductLineViewModel>? _gridRef;

        [Inject]
        public IStringLocalizer<Resources> Localizer { get; set; }

        [Parameter]
        public ObservableCollection<ProductLineViewModel> ProductLines { get; set; }

        [Parameter]
        public int SiteCode { get; set; }

        [Parameter]
        public bool IsLoading { get; set; }
    }
}