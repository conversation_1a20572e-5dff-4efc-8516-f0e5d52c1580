﻿namespace Triquestra.InvoiceReceipts.Blazor.Models.ProductAggregate
{
    public class ProductInventoryDetail
    {
        public string? ProductCode { get; set; }

        public int? SiteCode { get; set; }

        public decimal? QuantityOnHand { get; set; }

        public decimal? SellableQuantity { get; set; }

        public decimal? AllocatedQuantity { get; set; }

        public decimal? PurchaseOrderQuantity { get; set; }

        public decimal? OutboundTransferQuantity { get; set; }

        public decimal? InboundTransferQuantity { get; set; }

        public decimal? MovingAveragePrice { get; set; }

        public decimal? OnOrderQuantity => PurchaseOrderQuantity + InboundTransferQuantity;
    }
}
