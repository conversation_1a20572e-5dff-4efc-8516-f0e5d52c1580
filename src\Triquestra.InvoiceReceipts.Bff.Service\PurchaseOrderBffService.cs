using System.Net;
using Triquestra.Common.Bff.DTOs.Bff;
using Triquestra.Common.Bff.Services.Service;
using Triquestra.InfinityAPI.Transactions.StockReceipt.Models.DTOs;
using Triquestra.InvoiceReceipts.Bff.Interface;
using Triquestra.InvoiceReceipts.Bff.Service.ApiWrapper;
using Triquestra.InvoiceReceipts.Bff.Service.Mappers;
using Triquestra.InvoiceReceipts.Blazor.Common.Constants;
using Triquestra.InvoiceReceipts.Common.Constants;
using Triquestra.InvoiceReceipts.DTOs.InvoiceReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrderReceipts;
using Triquestra.InvoiceReceipts.DTOs.PurchaseOrders;

namespace Triquestra.InvoiceReceipts.Bff.Service
{
    public class PurchaseOrderBffService : IPurchaseOrderBffService
    {
        private readonly IPurchaseOrderBffMapper _bffMapper;
        private readonly InvoiceReceiptApiWrapper _wrapper;

        private readonly IBffDataService _bffDataService;

        public PurchaseOrderBffService(
            IPurchaseOrderBffMapper mapper,
            InvoiceReceiptApiWrapper wrapper,
            IBffDataService bffDataService)
        {
            _bffMapper = mapper;
            _wrapper = wrapper;
            _bffDataService = bffDataService;
        }

        public async Task<BffResultDto<List<PurchaseOrderBffDto>>> SearchPurchaseOrdersForDisbursementAsync(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            const string OpenStatus = "open";
            var searchParameter = _bffMapper.ToSearchParametersDto(requestBffDto);
            searchParameter.PurchaseOrderStatus = OpenStatus;
            var purchaseOrdersResponse = _wrapper.ApiWrapper.PurchaseOrdersProxy.SearchPurchaseOrders(searchParameter);

            if (!purchaseOrdersResponse.IsSucessful)
            {
                return new() { Data = new(), StatusCode = (int)HttpStatusCode.InternalServerError, ErrorMsg = purchaseOrdersResponse.ErrorMessage };
            }

            var purchaseOrders = purchaseOrdersResponse.Data?.PurchaseOrders?.Select(_bffMapper.ToPurchaseOrderBffDto).ToList() ?? new();

            await TruncateAndAssignProductsDetails(requestBffDto.ProductCodes, (short)requestBffDto.DestinationSiteCodes[0], purchaseOrders);

            return new() { Data = purchaseOrders, StatusCode = (int)HttpStatusCode.OK };
        }

        private async Task TruncateAndAssignProductsDetails(List<string> productCodes, short siteCode, List<PurchaseOrderBffDto> purchaseOrders)
        {
            var productsResponse = await _bffDataService.GetProductsAsync(productCodes, siteCode);

            if (productsResponse.IsSucessful)
            {
                foreach (var purchaseOrder in purchaseOrders)
                {
                    purchaseOrder.Lines = purchaseOrder.Lines.Where(x => productCodes.Contains(x.ProductCode)).ToList();

                    foreach (var line in purchaseOrder.Lines)
                    {
                        var product = productsResponse.Data.Find(x => x.ProductCode == line.ProductCode);
                        if (product != null)
                        {
                            line.Sku = product.Sku;
                            line.Description = product.Description;
                        }
                    }
                }
            }
        }

        public async Task<BffResultDto<List<PurchaseOrderReceiptBffDto>>> SearchCompletedPurchaseOrderReceiptsAsync(PurchaseOrderReceiptSearchRequestBffDto requestBffDto)
        {
            var tasks = requestBffDto.PurchaseOrderCodes
                .Select(purchaseOrderCode => SearchCompletedReceiptsForPurchaseOrderAsync(requestBffDto.SiteCodes, purchaseOrderCode));

            await Task.WhenAll(tasks);

            var purchaseOrderReceipts = new List<PurchaseOrderReceiptBffDto>();

            foreach (var task in tasks)
            {
                var receipts = await task;
                if (receipts.Count > 0)
                {
                    purchaseOrderReceipts.AddRange(receipts);
                }
            }

            return new() { Data = purchaseOrderReceipts, StatusCode = 200 };
        }

        private Task<List<PurchaseOrderReceiptBffDto>> SearchCompletedReceiptsForPurchaseOrderAsync(List<int> siteCodes, string purchaseOrderCode)
        {
            var searchRequest = new PurchaseOrderReceiptSearchRequestDto
            {
                PurchaseOrderCode = purchaseOrderCode,
                PurchaseOrderReceiptStatus = PurchaseOrderStatus.Complete,
                ResponseType = ApiConstants.FullResponseType,
                SiteCodes = siteCodes,
            };
            var response = _wrapper.ApiWrapper.StockReceiptProxy.SearchPurchaseOrderReceipts(searchRequest);

            List<PurchaseOrderReceiptBffDto> results = new();

            if (response.IsSucessful && response.Data.TotalRecords > 0)
            {
                var receipts = response.Data.SearchResult.Select(_bffMapper.ToPurchaseOrderReceiptBffDto);
                results.AddRange(receipts);
            }

            return Task.FromResult(results);
        }

        public async Task<BffResultDto<List<PurchaseOrderBffDto>>> SearchOpenPurchaseOrdersAsync(PurchaseOrderSearchRequestBffDto requestBffDto)
        {
            var searchParameter = _bffMapper.ToSearchParametersDto(requestBffDto);
            searchParameter.PurchaseOrderStatus = PurchaseOrderStatus.Open;
            var purchaseOrdersResponse = _wrapper.ApiWrapper.PurchaseOrdersProxy.SearchPurchaseOrders(searchParameter);

            if (!purchaseOrdersResponse.IsSucessful)
            {
                return new() { Data = new(), StatusCode = (int)HttpStatusCode.InternalServerError, ErrorMsg = purchaseOrdersResponse.ErrorMessage };
            }

            var purchaseOrders = purchaseOrdersResponse.Data?.PurchaseOrders?.Select(_bffMapper.ToPurchaseOrderBffDto).ToList() ?? new();

            await GetDataAndAssignBackOrderToPurchaseOrdersAsync(requestBffDto.DestinationSiteCodes, purchaseOrders);
            
            purchaseOrders = purchaseOrders.Where(x => x.Lines.Count > 0).ToList();

            return new() { Data = purchaseOrders, StatusCode = (int)HttpStatusCode.OK };
        }

        public BffResultDto<PurchaseOrderBffDto> GetPurchaseOrder(string purchaseOrderCode)
        {
            var purchaseOrderResponse = _wrapper.ApiWrapper.PurchaseOrdersProxy.GetPurcahseOrder(purchaseOrderCode);

            if (!purchaseOrderResponse.IsSucessful || purchaseOrderResponse.Data == null)
            {
                return new() { Data = new(), StatusCode = (int)HttpStatusCode.InternalServerError, ErrorMsg = purchaseOrderResponse.ErrorMessage };
            }

            var purchaseOrder = _bffMapper.ToPurchaseOrderBffDto(purchaseOrderResponse.Data);

            return new() { Data = purchaseOrder, StatusCode = (int)HttpStatusCode.OK };
        }

        public async Task<List<PurchaseOrderBffDto>> GetPurchaseOrdersAsync(List<string> purchaseOrderCodes)
        {
            var purchaseOrderTasks = purchaseOrderCodes.Select(purchaseOrderCode => Task.Run(() =>
            {
                return GetPurchaseOrder(purchaseOrderCode);
            }));

            await Task.WhenAll(purchaseOrderTasks);
            return purchaseOrderTasks.Select(x => x.Result.Data).ToList();
        }

        private async Task GetDataAndAssignBackOrderToPurchaseOrdersAsync(List<int> siteCodes, List<PurchaseOrderBffDto> purchaseOrders)
        {
            var completedReceiptsResponse = await SearchCompletedPurchaseOrderReceiptsAsync(new PurchaseOrderReceiptSearchRequestBffDto
            {
                PurchaseOrderCodes = purchaseOrders.Select(x => x.PurchaseOrderCode).ToList(),
                SiteCodes = siteCodes,
            });

            if (completedReceiptsResponse.IsSucessful && completedReceiptsResponse.Data.Count > 0)
            {
                var completedReceipts = completedReceiptsResponse.Data;

                foreach (var purchaseOrder in purchaseOrders)
                {
                    AssignBackOrderToPurchaseOrder(completedReceipts, purchaseOrder);
                }
            }
        }

        private static void AssignBackOrderToPurchaseOrder(List<PurchaseOrderReceiptBffDto> completedReceipts, PurchaseOrderBffDto purchaseOrder)
        {
            var matchedReceipts = completedReceipts
                                    .Where(x => x.PurchaseOrderCode == purchaseOrder.PurchaseOrderCode)
                                    .OrderByDescending(x => x.UpdatedCreated)
                                    .ToList();

            if (matchedReceipts.Count > 0)
            {
                foreach (var purchaseOrderLine in purchaseOrder.Lines)
                {
                    var matchedReceipt = matchedReceipts.Find(receipt => receipt.Lines.Exists(line => line.ProductCode == purchaseOrderLine.ProductCode));

                    if (matchedReceipt != null)
                    {
                        purchaseOrderLine.BackOrder = matchedReceipt.Lines.Find(x => x.ProductCode == purchaseOrderLine.ProductCode)!.BackOrderQuantity ?? 0;
                    }
                }
            }

            // Remove lines that have been fully receipted.
            purchaseOrder.Lines = purchaseOrder.Lines.Where(x => x.BackOrder == null || x.BackOrder > 0).ToList();
        }
    }
}