﻿using Triquestra.InvoiceReceipts.Blazor.Models.InvoiceSummaryAggregate;

namespace Infinity.InvoiceReceipts.Blazor.StateContainers
{
    public class BlazorStateChangedService
    {
        public InvoiceSummaryModel? InvoiceSummaryState { get; private set; }

        public void SetValue<T>(T value)
        {
            if (value is InvoiceSummaryModel invoiceSummaryState)
                InvoiceSummaryState = invoiceSummaryState;
        }

        public void RemoveValue<T>()
        {
            if (typeof(T) == typeof(InvoiceSummaryModel))
            {
                InvoiceSummaryState = null;
            }    
        }
    }
}