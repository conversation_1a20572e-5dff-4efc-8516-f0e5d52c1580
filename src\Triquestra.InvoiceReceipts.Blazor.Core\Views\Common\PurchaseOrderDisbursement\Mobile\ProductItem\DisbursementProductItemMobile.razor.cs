using CommunityToolkit.Mvvm.Input;
using Microsoft.AspNetCore.Components;
using System.Windows.Input;
using Telerik.Blazor.Components;
using Triquestra.InvoiceReceipts.Blazor.Models.PurchaseOrderAggregate;

namespace Infinity.InvoiceReceipts.Blazor.Views.Common.PurchaseOrderDisbursement.Mobile.ProductItem
{
    public partial class DisbursementProductItemMobile
    {
        public IRelayCommand<PanelBarItemClickEventArgs> _panelBarClickCommand;

        public bool _isExpanded;

        [Parameter]
        public bool IsExpanded
        {
            get { return _isExpanded; }
            set
            {
                _isExpanded = value;

                if (_isExpanded)
                {
                    ExpandedItems = new List<string> { PurchaseOrderLine.Key };
                }
                else
                {
                    ExpandedItems = new List<string>();
                }
            }
        }

        [Parameter]
        public PurchaseOrderLine PurchaseOrderLine { get; set; }

        [Parameter]
        public IRelayCommand<string> SelectProductCommand { get; set; }

        [Parameter]
        public ICommand? DataChangedCommand { get; set; }

        [Parameter]
        public int SiteCode { get; set; }

        private IEnumerable<object> _expandedItems = new List<string>();
        [Parameter]
        public IEnumerable<object> ExpandedItems { get; set; }

        public DisbursementProductItemMobile()
        {
            _panelBarClickCommand = new RelayCommand<PanelBarItemClickEventArgs>(PanelBarClickHandler);
        }

        private void PanelBarClickHandler(PanelBarItemClickEventArgs? args)
        {
            var key = args!.Item.ToString();
            if (SelectProductCommand?.CanExecute(key) == true)
            {
                SelectProductCommand.Execute(key);
            }
        }
    }
}