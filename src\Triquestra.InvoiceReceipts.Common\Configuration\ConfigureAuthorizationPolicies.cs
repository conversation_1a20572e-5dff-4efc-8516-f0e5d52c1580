﻿using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Triquestra.InvoiceReceipts.Common.Constants;

namespace Triquestra.InvoiceReceipts.Common.Configuration
{
    public static class ConfigureAuthorizationPolicies
    {
        public static void AddInvoiceReceiptPolicies(this AuthorizationOptions options)
        {
            options.AddPolicy(InvoiceReceiptPolicies.INVOICE_RECEIPT, policy =>
             policy.RequireAssertion(context => context.User.HasClaim(c =>
             {
                 return CheckClaim(c, InvoiceReceiptPermissions.INVOICE_RECEIPT.ToString());
             })));
        }

        private static bool CheckClaim(Claim claim, params string[] claimValue)
        {
            if (claim.Type != "actions")
                return false;

            var values = claim.Value.Split(',').ToList();
            return claimValue?.Any(cv => values.Contains(cv)) ?? false;
        }
    }
}