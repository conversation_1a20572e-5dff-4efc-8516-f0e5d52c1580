@using Infinity.Blazor.UIControls.Buttons
@using Triquestra.InvoiceReceipts.Blazor.Models.SystemConfigurationsAggregate.Enums
@using Triquestra.InvoiceReceipts.Common.Constants

<div class="summary-panel-container">
    <ExpandableCardContainer>
        <TitleContent>
            <div class="position-relative">
                <div class="fw-normal">
                    <StatusTag Status="@InvoiceReceiptStatus.Complete" />
                </div>
            </div>
        </TitleContent>
        <DescriptionContent>
            <div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.CompletedOn) - </span>
                    <span>&nbsp;@InvoiceReceipt.UpdatedOrCreated?.ToString("dd MMMM yyyy")</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.CompletedBy) - </span>
                    <span>&nbsp;@InvoiceReceipt.UpdatedBy</span>
                </div>
                <div class="pb-2">
                    <span class="text-muted">@Localizer.GetString(Translations.ReceiptNo) - </span>
                    <span>&nbsp;@InvoiceReceipt.StockReceiptCode</span>
                </div>
            </div>
        </DescriptionContent>
        <ExpandChildContent>
            <div>
                <hr class="mt-2 mb-3" />
                <div class="fs-16 mb-4">@Localizer.GetString(Translations.ReceiptInformation)</div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.Site)
                    </div>
                    <div class="text-end">@InvoiceReceipt.SiteName</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.Supplier)
                    </div>
                    <div class="text-end">@InvoiceReceipt.SupplierName</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.InvoiceNo)
                    </div>
                    <div>@InvoiceReceipt.InvoiceCode</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.FreightExtra)
                    </div>
                    <div>@InvoiceReceipt.TotalFreight?.ToString("C")</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.Tax)
                    </div>
                    <div>@InvoiceReceipt.TotalTax?.ToString("C")</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.InvoiceTotal)
                    </div>
                    <div>@InvoiceReceipt.TotalInvoiceValue?.ToString("C")</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.ReceiptTotal)
                    </div>
                    <div class="total-value">@InvoiceReceipt.ReceiptTotal.ToString("C")</div>
                </div>
                <div class="triquestra-field-box">
                    <div class="fw-500">
                        @Localizer.GetString(Translations.Note)
                    </div>
                    <div class="text-end text-break">@InvoiceReceipt.Note</div>
                </div>
            </div>
        </ExpandChildContent>
    </ExpandableCardContainer>
</div>