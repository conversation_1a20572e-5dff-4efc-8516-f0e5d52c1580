﻿.receipt-details-container label {
    font-weight: 500;
    padding: 10px 0;
}

.page-container {
    margin-top: 80px;
}

.page-container ::deep .card-component {
    padding: 0px 16px !important;
    gap: 38px;
    display: flex;
    flex-direction: column;
}

.page-header {
    background-color: #ffffff;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 72px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.08);
}

    .page-header .title {
        align-self: center;
        font-weight: 400;
        font-size: 24px;
        line-height: 28.13px;
        color: #202020;
    }

    .page-header .header-group {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
    }

    .page-header ::deep .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 10px;
        height: 40px;
        margin-right: 2.5rem;
        align-self: center;
    }

    .page-header .buttons ::deep button {
        padding: 8px 15px;
    }
/*****/
.page-container ::deep .k-card-body {
    display: flex;
    background-color: #FFFFFF;
    padding-top: 16px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 12px;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08);
    gap: 24px;
    border-radius: 12px;
    margin-top: 15px;
}

/*******/
.page-container ::deep .custom-tabs .k-splitter,
.page-container ::deep .k-card, .k-panelbar.customized-panelbar {
    border: none;
    margin-bottom: 3px;
}

.k-card-body .expandAll {
    position: absolute;
    right: 4rem;
    z-index: 10;
    color: #202020;
    font-weight: 400;
    font-size: 14px;
    line-height: 16.41px;
    margin-top: 0.5rem;
}

    .k-card-body .expandAll input {
        width: 20px;
        height: 20px;
        gap: 10px;
    }

@media (max-width: 640px) {
    .page-header {
        justify-content: space-between;
    }

        .page-header .title {
            align-self: center;
            font-weight: 700;
            font-size: 18px;
            line-height: 25px;
            color: #111827;
        }

    .back-button ::deep .triquestra-button {
        width: 24px;
        height: 24px;
    }

    .page-header .buttons {
        display: flex;
        justify-content: space-around;
        flex-direction: row;
        margin-left: 0;
        height: 40px;
        align-self: center;
        margin-right: 10px;
    }

        .page-header .buttons button {
            margin-left: 0;
        }
}

@media (max-width: 410px) {
    .mobile-delete-draft-container {
        margin-top: 10px;
        position: unset;
    }
}

.expand-cardcontainer .separator {
    display: flex;
    align-items: center;
    text-align: center;
    width: auto;
    background: none;
    font-weight: 500;
}

    .expand-cardcontainer .separator::before,
    .expand-cardcontainer .separator::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #DCDCDC;
    }

::deep .k-card.telerik-blazor.k-card-vertical {
    /* make datepicker popup overlaps its parent */
    overflow: visible;
}

.product-list-header {
    padding-left: 16px;
}