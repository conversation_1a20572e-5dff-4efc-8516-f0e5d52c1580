﻿using Microsoft.AspNetCore.Mvc;

namespace Triquestra.InvoiceReceipts.Bff.Api.Configuration
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
    internal sealed class DynamicRouteAttribute : RouteAttribute
    {
        public DynamicRouteAttribute(string template)
            : base($"{CleanTemplateRoute(Environment.GetEnvironmentVariable("InvoiceReceiptsBffBasePath", EnvironmentVariableTarget.Process))}/{template}")
        {
            if (string.IsNullOrWhiteSpace(template))
                throw new ArgumentNullException(nameof(template));
        }

        private static string CleanTemplateRoute(string? route)
        {
            return route?.Replace("//", "/") ?? string.Empty;
        }
    }
}
