﻿@using Infinity.Blazor.UIControls.Buttons
@using Infinity.Blazor.UIControls.DatePicker
@using Infinity.Blazor.UIControls.Grids
@using Infinity.Blazor.UIControls.Grids.Mapping
@using Infinity.Blazor.UIControls.InputFields
@using Infinity.Blazor.UIControls.Layouts
@using Infinity.Blazor.UIControls.Selectors
@using Infinity.InvoiceReceipts.Blazor.ViewModels.Common
@using Telerik.Blazor
@using Telerik.Blazor.Components

<div class="productlist-container">
    <CardContainer>
        <CardBody Class="p-0 align-items-start">
            @if (IsLoading)
            {
                <div class="w-100 text-center p-5">
                    <Infinity.Blazor.UIControls.Loaders.InfLoadingBox Height="300px" />
                </div>
            }
            else
            {
                <InfGridView @ref="_gridRef"
                             Data="@ProductLines"
                             TItem="ProductLineViewModel"
                             Class="custom-grid no-scroll"
                             Reorderable="true"
                             Sortable="true"
                             RowHeight="50"
                             EnableLoaderContainer="true"
                             FilterMode="@FilterMode.FilterMenu"
                             PageParameters="@_pageParameters"
                             SelectionParameters="@_selectionParameters">
                    <ColumnsTemplate>
                        <GridColumn Field="@nameof(ProductLineViewModel.Description)"
                                    Title="@Localizer.GetString(Translations.Product)"
                                    Width="400px">
                            <Template Context="context">
                                @{
                                    var item = context as ProductLineViewModel;
                                    <div class="w-100">
                                        <div class="product_description text-truncate">
                                            @item.Description
                                        </div>
                                        <label class="product_code">@item.ProductCode</label>
                                    </div>
                                }
                            </Template>
                        </GridColumn>
                        <GridColumn Width="250px" Field="@nameof(ProductLineViewModel.SKU)" Title="@Localizer.GetString(Translations.SKU)" />
                        <GridColumn Width="250px" Field="@nameof(ProductLineViewModel.SupplierProductCode)" Title="@Localizer.GetString(Translations.SupProductCode)" />
                        <GridColumn Width="150px"
                                    Field="@nameof(ProductLineViewModel.ReceiptQuantity)"
                                    Title="@Localizer.GetString(Translations.ReceiptQty)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat" />
                        <GridColumn Width="150px"
                                    Field="@nameof(ProductLineViewModel.ReceiptPack)"
                                    Title="@Localizer.GetString(Translations.ReceiptPack)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridDecimalDisplayFormat" />
                        <GridColumn Width="150px"
                                    Field="@nameof(ProductLineViewModel.Cost)"
                                    Title="@Localizer.GetString(Translations.Cost)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridCurrencyDisplayFormat" />
                        <GridColumn Width="150px"
                                    Field="@nameof(ProductLineViewModel.TotalCost)"
                                    Title="@Localizer.GetString(Translations.TotalCost)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridCurrencyDisplayFormat" />
                        <GridColumn Width="150px"
                                    Field="@nameof(ProductLineViewModel.FreightExtras)"
                                    Title="@Localizer.GetString(Translations.FreightExtras)"
                                    Filterable="false"
                                    HeaderClass="text-end"
                                    TextAlign="ColumnTextAlign.Right"
                                    DisplayFormat="@UIConstants.DataGridCurrencyDisplayFormat" />
                    </ColumnsTemplate>
                </InfGridView>
            }
        </CardBody>
    </CardContainer>
</div>