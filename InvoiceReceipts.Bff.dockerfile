FROM mcr.microsoft.com/dotnet/aspnet:8.0-bookworm-slim AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0-bookworm-slim AS build
WORKDIR /src
COPY . ./

FROM build AS publish
RUN dotnet publish "src/Triquestra.InvoiceReceipts.Bff.Api/Triquestra.InvoiceReceipts.Bff.Api.csproj"  -c Release -o /app -r linux-x64 --self-contained

ARG PROGET_API_KEY
ARG PROGET_SOURCE
ARG PACKAGE_VERSION

RUN dotnet tool install pgutil --create-manifest-if-needed
RUN dotnet tool run pgutil builds scan --input='src/Triquestra.InvoiceReceipts.Bff.Api/Triquestra.InvoiceReceipts.Bff.Api.csproj' --project-name="InfinityPLUS_InvoiceReceipts.BFF" --version=$PACKAGE_VERSION --project-type=application --api-key=$PROGET_API_KEY --source=$PROGET_SOURCE

FROM base AS final
WORKDIR /app
COPY --from=publish /app .
ENTRYPOINT ["dotnet", "Triquestra.InvoiceReceipts.Bff.Api.dll", "--urls", "http://*:80"]